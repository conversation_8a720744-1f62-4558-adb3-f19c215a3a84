import { makeStyles } from '@material-ui/styles';

const styles = makeStyles(theme => ({
  info: {
    padding: theme.spacing(1),
    marginBottom: 0
  },
  infoTitle: {
    padding: theme.spacing(1),
    fontWeight: 'bold',
    marginBottom: 30
  },
  cardContainer: {
    marginBottom: 15,
    marginTop: 15,
    marginLeft: 1,
    maxWidth: '100%'
  },
  cardContainerPensionInfo: {
    marginBottom: 15,
    marginTop: 15,
    marginLeft: 1,
    maxWidth: '100%'
  },
  cardHeader: {
    backgroundColor: '#E4E8E8'
  },
  title: {
    fontWeight: 'bolder'
  },
  actionButtons: {
    flexDirection: 'row',
    '@mediaQuery(minWidth: 600px)': {
      flexDirection: 'column'
    },
    justifyContent: 'flex-end',
    width: 'calc(100% + 8px)',
    margin: '-4px',
    paddingTop: '10px'
  },
  containerActionButton: {
    overflowX: 'scroll'
  },
  editSave: {
    marginBottom: 15,
    marginRight: 15
  },
  modalCollector: {
    width: '30%',
    marginTop: '20%',
    marginLeft: '30%',
    padding: 10,
    borderWidth: 1,
    backgroundColor: 'white'
  },
  modalButton: {
    marginTop: 20,
    marginLeft: 10,
    marginRight: 10
  },
  acceptButton: {
    marginTop: 20,
    marginLeft: 10,
    marginRight: 10,
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.primary.light,
      color: theme.palette.primary.contrastText
    }
  },
  cancelButton: {
    marginTop: 20,
    marginLeft: 10,
    marginRight: 10,
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.error.light,
      color: theme.palette.error.contrastText
    }
  },
  divFileUpload: {
    width: '89px',
    margin: '-37px 0px -13px 0px',
    height: '50px',
    color: 'red'
  }
}));

export default styles;
