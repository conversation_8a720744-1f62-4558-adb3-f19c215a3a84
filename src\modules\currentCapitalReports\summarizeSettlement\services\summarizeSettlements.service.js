/* eslint-disable no-console */

import moment from 'moment';
import { setCurrentDateReports, setIsValidDateRangeReports } from '../actions';
import { axiosRequest, excelRequest } from '../../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

//------------------------------------------------------------------------------------------------
export const queryTimeInterval = async (startingDate, endingDate) => {
  const starting = moment(startingDate, 'YYYY-MM').toString();
  const ending = moment(endingDate, 'YYYY-MM').toString();
  const { data, isError } = await excelRequest
    .get(`${api}/summarizeSettlements/fetchExcelData/:${starting}/:${ending}`)
    .catch(err => {
      console.error(err);
      return {
        data: null,
        isError: true
      };
    });
  return { data, isError };
};
//------------------------------------------------------------------------------------------------
export const getCurrentDate = async dispatch => {
  const { data, isError = false } = await axiosRequest
    .get(`${api}/summarizeSettlements/time`)
    .catch(err => {
      console.error(err);
      dispatch(setIsValidDateRangeReports(false));
      return { data: '', isError: true };
    });
  !isError && dispatch(setCurrentDateReports(new Date(data)));
};
