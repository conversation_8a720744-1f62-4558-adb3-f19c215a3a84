/* eslint-disable import/no-unresolved */
/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import Grid from '@material-ui/core/Grid';
import { useSelector, useDispatch } from 'react-redux';
import useOnlineStatus from '@rehooks/online-status';
import useStyles from '../../../retirementQuery/views/styles';
import Select from '@material-ui/core/Select';
import FormControl from '@material-ui/core/FormControl';
import InputLabel from '@material-ui/core/InputLabel';
import MenuItem from '@material-ui/core/MenuItem';
import moment from 'moment';
import TextField from '@material-ui/core/TextField';
import { datePickerStyles } from '../../components/picker/pickerStyles';
import DatePicker from '../../components/picker/picker';
import { makeStyles } from '@material-ui/core/styles';
import CircularProgress from '@material-ui/core/CircularProgress';
import { useHistory } from 'react-router-dom';
import { Tooltip } from '@material-ui/core';
import { Help } from '@material-ui/icons';
import { formatCurrency, normalizeValue } from '../../utils/discountsAndAssetsValidations';

import validator from 'validator';
import MuiAlert from '@material-ui/lab/Alert';
import {
  setFormulableLabel,
  setFormulableReason,
  setFormulableAssetType,
  setFormulableAmount,
  setFormulableTypeRetention,
  setFormulableValidity,
  setFormulableAmountRetention,
  setFormulableStartDate,
  setFormulableEndDate,
  setRetentionVisibility,
  setModifiedFieldErrors
} from '../../actions';

import Snackbar from '@material-ui/core/Snackbar';
import { loadMotive } from '../../../../modules/nomenclators/motive/services';
import CollectorCardJudicialRetention from './collectorJudicialRetention';
import SettlementPaymentRetentionCard from './settlementPaymentJudicialRetention';
import Button from '@material-ui/core/Button';
import { updatePensionerAssetsAndDiscounts } from '../../../../modules/retirementQuery/services/queryPensions.service';
import numbUtils from '../../../../utils/numberOperations';

import { checkRutAndDV } from '../../utils/formatters';

const dateRangeErrMsg = 'Debe ser igual o mayor a Fecha de inicio';
const startDateErrMsg = 'Debe ser mayor o igual a la fecha actual';
const amountErrMsg = 'El valor es inválido';
const ayuda =
  'Para Reajuste IMR y Sueldo Vital si desea ingresar 1/2 sueldo vital debe ingresar 50 y si desea ingresar 2 debe ingresar 200.';
const succesSnackbarMsg = 'Los registros se guardaron correctamente';
const errorSnackbarMsg = 'Error al guardar el registro';
const pickerCustomStyle = { minWidth: '250px', marginTop: '0px' };
const pickerInputProps = { style: { height: '32px' } };
const maxYear = new Date().getFullYear() + 11;
const DATE_FORMAT = 'MM-YYYY';
const currentDate = moment(new Date()).format(DATE_FORMAT);
const keyDiscount = /Descuento/i;
const keyRetention = /Retenci[oó]n Judicial/i;
const MIN_NUMBER_ALLOWED = 0;
let MAX_NUMBER_ALLOWED = 9999999;
const makeStyle = makeStyles({
  root: {
    width: '250px'
  },
  input: {
    height: '16px'
  },
  saveButton: { width: '280px', display: 'grid', placeItems: 'end' },
  backButton: { width: '300px' }
});

function Alert(props) {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
}

const AssetsAndDiscountsFormulable = ({
  values,
  valuesLiquidation,
  beneficiaryRut,
  causantRut,
  pensionCodeId,
  isEditing,
  isReadOnly
}) => {
  const editable = isEditing ? false : true;
  const classes = useStyles();
  const styles = makeStyle();
  const dispatch = useDispatch();
  const onlineStatus = useOnlineStatus();
  let timeout;
  const [reasons, setReasons] = useState([]);
  const [startDateError, setStartDateError] = useState(false);
  const history = useHistory();
  const [dateRangeError, setDateRangeError] = useState(false);
  const [amountError, setAmountError] = useState(false);
  const [loading, setLoading] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackBarMessage, setSnackbarMessage] = useState('');
  const [snackBarSeverity, setSnackbarSeverity] = useState('');
  const [amountRetentionError, setAmountRetentionError] = useState(false);
  const [amountRetentionErrMsg, setAmountRetentionErrMsg] = useState('');

  const formulableLabel = useSelector(store => store.queryPensions.formulableLabel);
  const formulableAssetType = useSelector(store => store.queryPensions.formulableAssetType);
  const formulableAmount = useSelector(store => store.queryPensions.formulableAmount);
  const formulableReason = useSelector(store => store.queryPensions.formulableReason);
  const formulableTypeRetention = useSelector(store => store.queryPensions.formulableTypeRetention);
  const formulableValidity = useSelector(store => store.queryPensions.formulableValidity);
  const formulableAmountRetention = useSelector(
    store => store.queryPensions.formulableAmountRetention
  );
  const formulableStartDate = useSelector(store => store.queryPensions.formulableStartDate);
  const formulableEndDate = useSelector(store => store.queryPensions.formulableEndDate);
  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);

  const collectorRetentionRut = useSelector(store => store.queryPensions.collectorRetentionRut);
  const collectorRetentionName = useSelector(store => store.queryPensions.collectorRetentionName);
  const collectorRetentionLastName = useSelector(
    store => store.queryPensions.collectorRetentionLastName
  );
  const collectorRetentionMothersLastName = useSelector(
    store => store.queryPensions.collectorRetentionMothersLastName
  );
  const collectorRetentionAddress = useSelector(
    store => store.queryPensions.collectorRetentionAddress
  );
  const collectorRetentionCommune = useSelector(
    store => store.queryPensions.collectorRetentionCommune
  );
  const collectorRetentionCity = useSelector(store => store.queryPensions.collectorRetentionCity);

  const retentionPaymentGateway = useSelector(store => store.queryPensions.retentionPaymentGateway);
  const retentionBank = useSelector(store => store.queryPensions.retentionBank);
  const retentionBranchOffice = useSelector(store => store.queryPensions.retentionBranchOffice);
  const retentionAccountNumber = useSelector(store => store.queryPensions.retentionAccountNumber);
  const retentionVisibility = useSelector(store => store.queryPensions.retentionVisibility);
  const retentionDiscountsAndAssets = useSelector(store => store.queryPensions.discountsAndAssets);
  const showSnackbar = (severity, message) => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setOpenSnackbar(true);
  };

  const handlePickerChange = (field, oppositeField, value) => {
    validateDatePicker({
      field,
      oppositeField,
      value
    });
  };

  const validateDatePicker = ({ field, _oppositeField, value }) => {
    const methods = { endDate: 'isSameOrAfter', startDate: 'isSameOrBefore' };
    const startDateIsValid = moment(value, DATE_FORMAT).isSameOrAfter(
      moment(currentDate, DATE_FORMAT)
    );
    const rangeIsValid = moment(value, DATE_FORMAT)[methods[field]](
      moment(formulableStartDate, DATE_FORMAT)
    );
    setDateRangeError(!rangeIsValid);
    field === 'startDate' && setStartDateError(!startDateIsValid);
    if (field === 'startDate') {
      dispatch(setFormulableStartDate(moment(value, DATE_FORMAT).format(DATE_FORMAT)));
    } else if (field === 'endDate') {
      dispatch(setFormulableEndDate(moment(value, DATE_FORMAT).format(DATE_FORMAT)));
    }
  };

  const goBack = () => {
    dispatch(
      setModifiedFieldErrors({
        ...modifiedFieldErrors,
        retentionAccountNumber: false,
        retentionBank: false,
        retentionPaymentGateway: false
      })
    );

    history.push('/pensionados/consulta-pensionados/ver-mas', {
      beneficiaryRut: beneficiaryRut,
      causantRut: causantRut,
      pensionCodeId: pensionCodeId,
      tabIndex: 2
    });
  };

  const isValidDecimal = (val, parameterType) => {
    const stringValue = `${val}`;
    if (stringValue.length === 0) {
      dispatch(setFormulableAmountRetention(val));
      return false;
    }

    if (!validator.isDecimal(stringValue, { decimal_digits: '0,2', locale: 'es-ES' })) {
      dispatch(setFormulableAmountRetention(val));
      return false;
    }
    if (parameterType === 'Porcentaje') {
      // eslint-disable-next-line no-magic-numbers
      MAX_NUMBER_ALLOWED = 100;
    } else if (parameterType === 'ReajusteIMR' || parameterType === 'SueldoVital') {
      // eslint-disable-next-line no-magic-numbers
      MAX_NUMBER_ALLOWED = 999.99;
    } else {
      // eslint-disable-next-line no-magic-numbers
      MAX_NUMBER_ALLOWED = 9999999;
    }

    const fixedValue = numbUtils.roundValue(stringValue.replace(',', '.'));
    if (fixedValue < MIN_NUMBER_ALLOWED || fixedValue > MAX_NUMBER_ALLOWED) {
      dispatch(setFormulableAmountRetention(val));
      return false;
    }

    dispatch(setFormulableAmountRetention(val));
    return true;
  };

  const handleFieldChange = ({ target }) => {
    let { value } = target;
    if (target.name === 'formulableLabel') {
      dispatch(setFormulableReason(''));
      dispatch(setRetentionVisibility(false));
      dispatch(setFormulableLabel(value));
    } else if (target.name === 'formulableAssetType') {
      dispatch(setFormulableAssetType(value));
    } else if (target.name === 'formulableReason') {
      dispatch(setFormulableReason(value));
    } else if (target.name === 'formulableAmount') {
      value = formatCurrency(target.value);
      setAmountError(!+normalizeValue(value));
      dispatch(setFormulableAmount(value));
    } else if (target.name === 'formulableTypeRetention') {
      dispatch(setFormulableTypeRetention(value));
      dispatch(setFormulableAmountRetention(''));
      setAmountRetentionError(false);
      setAmountRetentionErrMsg('');
    } else if (target.name === 'formulableValidity') {
      dispatch(setFormulableValidity(value));
    } else if (target.name === 'formulableAmountRetention') {
      value = target.value;
      setAmountRetentionError(false);
      setAmountRetentionErrMsg('');
      if (isValidDecimal(value, formulableTypeRetention)) {
        let aux = parseFloat(value.replace(',', '.'));
        if (formulableTypeRetention === 'Porcentaje' && aux > 100) {
          setAmountRetentionError(true);
          setAmountRetentionErrMsg('Valores permitidos: [0,01 - 100]');
          console.log(aux);
        } else if (
          (formulableTypeRetention === 'ReajusteIMR' ||
            formulableTypeRetention === 'SueldoVital') &&
          // eslint-disable-next-line no-magic-numbers
          aux > 999.99
        ) {
          setAmountRetentionError(true);
          setAmountRetentionErrMsg('Valores permitidos: [0,01 - 999,99]');
        }

        if (aux <= 0) {
          setAmountRetentionError(true);
          if (formulableTypeRetention === 'Porcentaje') {
            setAmountRetentionErrMsg('Valores permitidos: [0,01 - 100]');
          } else if (
            formulableTypeRetention === 'ReajusteIMR' ||
            formulableTypeRetention === 'SueldoVital'
          ) {
            setAmountRetentionErrMsg('Valores permitidos: [0,01 - 999,99]');
          } else if (
            formulableTypeRetention === 'ReajusteIPC' ||
            formulableTypeRetention === 'Monto'
          ) {
            setAmountRetentionErrMsg('Valores permitidos: [1 - 9999999]');
          }
        }
      } else {
        setAmountRetentionError(true);
        if (formulableTypeRetention === 'Porcentaje') {
          setAmountRetentionErrMsg('Valores permitidos: [0,01 - 100]');
        } else if (
          formulableTypeRetention === 'ReajusteIMR' ||
          formulableTypeRetention === 'SueldoVital'
        ) {
          setAmountRetentionErrMsg('Valores permitidos: [0,01 - 999,99]');
        } else if (
          formulableTypeRetention === 'ReajusteIPC' ||
          formulableTypeRetention === 'Monto'
        ) {
          setAmountRetentionErrMsg('Valores permitidos: [1 - 9999999]');
        }
      }
    }
    if (target.name === 'formulableReason' && keyDiscount.test(formulableLabel)) {
      keyRetention.test(value)
        ? dispatch(setRetentionVisibility(true))
        : dispatch(setRetentionVisibility(false));
    }
  };

  useEffect(() => {
    dispatch(loadMotive()).then(motives => setReasons(motives));
    return () => {
      clearTimeout(timeout);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getReasons = reasonType => {
    return reasons.filter(({ option }) => option.toLowerCase() === reasonType.toLowerCase());
  };

  const save = async () => {
    const mapper = { haber: 'assetsNonFormulable', descuento: 'discountsNonFormulable' };
    const { discountsAndAssets, ...data } = retentionDiscountsAndAssets;

    if (retentionVisibility) {
      const judicialRetention = {
        paymentInfo: {
          paymentGateway: retentionPaymentGateway,
          accountNumber: retentionAccountNumber,
          bank: retentionBank === 'SELECCIONE BANCO' ? ' ' : retentionBank,
          branchOffice: retentionBranchOffice
        },
        collector: {
          rut: collectorRetentionRut,
          name: collectorRetentionName,
          lastName: collectorRetentionLastName,
          mothersLastName: collectorRetentionMothersLastName
            ? collectorRetentionMothersLastName
            : '',
          address: collectorRetentionAddress,
          commune: collectorRetentionCommune,
          city: collectorRetentionCity
        },
        retention: {
          validity: formulableValidity === 'Si' ? true : false,
          type: formulableTypeRetention,
          amount: parseFloat(formulableAmountRetention.replace(',', '.'))
        }
      };
      data.judicialRetention = JSON.parse(JSON.stringify(judicialRetention));
    }
    data.amount = normalizeValue(formulableAmount);
    data.label = formulableLabel;
    data.reason = formulableReason;
    data.creationDate = new Date();
    data.startDate = formulableStartDate;
    data.endDate = formulableEndDate;
    data.assetType = formulableAssetType || '';
    const key = mapper[formulableLabel.toLowerCase()];

    if (isEditing) {
      const index = discountsAndAssets[key].findIndex(r => r._id === data._id);
      discountsAndAssets[key][index] = data;
    } else {
      if (!discountsAndAssets[key]) discountsAndAssets[key] = [];
      discountsAndAssets[key].push(data);
    }
    setLoading(true);
    const { error } = await updatePensionerAssetsAndDiscounts(discountsAndAssets);

    if (error) {
      setLoading(false);
      return showSnackbar('error', errorSnackbarMsg);
    }

    showSnackbar('success', succesSnackbarMsg);
    timeout = setTimeout(() => {
      setLoading(false);
      goBack();
      // eslint-disable-next-line no-magic-numbers
    }, 5000);
  };

  const formIsValid =
    (!(formulableLabel === 'Haber' && !formulableAssetType) &&
      !(!isEditing && !formulableReason) &&
      !!formulableStartDate &&
      !!formulableEndDate &&
      !(!+normalizeValue(formulableAmount) && !retentionVisibility) &&
      !dateRangeError &&
      !startDateError &&
      !retentionVisibility) ||
    (!(formulableLabel === 'Haber' && !formulableAssetType) &&
      !(!isEditing && !formulableReason) &&
      !!formulableStartDate &&
      !!formulableEndDate &&
      !(!+normalizeValue(formulableAmount) && !retentionVisibility) &&
      !dateRangeError &&
      !startDateError &&
      retentionVisibility &&
      formulableAmountRetention.length &&
      !amountRetentionError &&
      formulableTypeRetention.length &&
      formulableValidity.length &&
      checkRutAndDV(collectorRetentionRut) &&
      collectorRetentionName.length &&
      collectorRetentionLastName.length &&
      !modifiedFieldErrors.retentionAccountNumber &&
      !modifiedFieldErrors.retentionPaymentGateway &&
      !modifiedFieldErrors.retentionBank);

  return (
    <>
      <Grid container spacing={5}>
        <Grid item>
          <FormControl variant="outlined">
            <InputLabel>Haber o Descuento</InputLabel>
            <Select
              className={classes.selectField}
              name="formulableLabel"
              label="Haber o Descuento"
              disabled={!onlineStatus || isEditing}
              value={formulableLabel}
              onChange={handleFieldChange}
            >
              <MenuItem value="Haber">Haber</MenuItem>
              <MenuItem value="Descuento">Descuento</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item>
          {formulableLabel.toLowerCase() === 'haber' ? (
            <FormControl variant="outlined">
              <InputLabel>Tipo de Haber</InputLabel>
              <Select
                className={classes.selectField}
                label="Tipo de Haber"
                name="formulableAssetType"
                value={formulableAssetType || ''}
                disabled={!onlineStatus || isEditing}
                onChange={handleFieldChange}
              >
                <MenuItem value="liquido">Líquido</MenuItem>
                <MenuItem value="imponible">Imponible</MenuItem>
              </Select>
            </FormControl>
          ) : null}
        </Grid>
      </Grid>

      <Grid container spacing={5}>
        <Grid item>
          <FormControl variant="outlined">
            <InputLabel>Seleccione el Motivo</InputLabel>
            <Select
              className={classes.selectField}
              name="formulableReason"
              label="Seleccione el Motivo"
              disabled={!onlineStatus || isEditing}
              value={formulableReason || ''}
              onChange={handleFieldChange}
            >
              {isEditing && <MenuItem value={formulableReason}>{formulableReason}</MenuItem>}
              {formulableLabel
                ? getReasons(formulableLabel).map(({ motive }) => (
                    <MenuItem value={motive.toLowerCase()}>{motive}</MenuItem>
                  ))
                : null}
            </Select>
          </FormControl>
        </Grid>
        {!retentionVisibility ? (
          <Grid item>
            <TextField
              name="formulableAmount"
              variant="outlined"
              label="Monto"
              className={styles.root}
              inputProps={{ className: styles.input }}
              disabled={!onlineStatus || loading}
              error={amountError}
              helperText={amountError && amountErrMsg}
              value={formulableAmount}
              onChange={handleFieldChange}
            />
          </Grid>
        ) : null}
      </Grid>
      {retentionVisibility ? (
        <Grid container spacing={5}>
          <Grid item>
            <FormControl variant="outlined">
              <InputLabel>Tipo Retención</InputLabel>
              <Select
                className={classes.selectField}
                name="formulableTypeRetention"
                label="Tipo Retención"
                disabled={!onlineStatus || isEditing}
                value={formulableTypeRetention}
                onChange={handleFieldChange}
              >
                <MenuItem value="Porcentaje">Porcentaje</MenuItem>
                <MenuItem value="Monto">Monto</MenuItem>
                <MenuItem value="ReajusteIPC">Reajuste IPC</MenuItem>
                <MenuItem value="ReajusteIMR">Reajuste IMR</MenuItem>
                <MenuItem value="SueldoVital">Sueldo Vital</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item>
            <FormControl variant="outlined">
              <InputLabel>Vigente</InputLabel>
              <Select
                className={classes.selectField}
                label="Vigente"
                name="formulableValidity"
                value={formulableValidity || ''}
                disabled={!onlineStatus || isEditing}
                onChange={handleFieldChange}
              >
                <MenuItem value="Si">Si</MenuItem>
                <MenuItem value="No">No</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      ) : null}
      {retentionVisibility ? (
        <Grid container spacing={5}>
          <Grid item>
            <TextField
              name="formulableAmountRetention"
              variant="outlined"
              label="Monto o Porcentaje"
              className={styles.root}
              inputProps={{ className: styles.input }}
              disabled={!onlineStatus || loading || isReadOnly}
              error={amountRetentionError}
              helperText={amountRetentionError && amountRetentionErrMsg}
              value={formulableAmountRetention}
              onChange={handleFieldChange}
            />
            <Tooltip title={ayuda} style={{ margin: '0px 0px -22px 0px' }}>
              <Help></Help>
            </Tooltip>
          </Grid>
        </Grid>
      ) : null}
      <Grid container spacing={5}>
        <Grid item>
          <DatePicker
            label="Fecha Inicio"
            className={classes}
            disabled={!onlineStatus || loading || isReadOnly}
            format={DATE_FORMAT}
            value={moment(formulableStartDate, DATE_FORMAT)}
            error={startDateError}
            helperText={startDateError && startDateErrMsg}
            minDate={new Date()}
            maxDate={new Date(maxYear, 0, 1)}
            disableFuture={false}
            onChange={e => handlePickerChange('startDate', 'endDate', e)}
            style={pickerCustomStyle}
            inputProps={pickerInputProps}
            MuiStyle={datePickerStyles}
          />
        </Grid>
        {!retentionVisibility ? (
          <Grid item>
            <DatePicker
              label="Fecha Fin"
              className={classes}
              disabled={!onlineStatus || loading || isReadOnly}
              format={DATE_FORMAT}
              value={moment(formulableEndDate, DATE_FORMAT)}
              error={dateRangeError}
              helperText={dateRangeError && dateRangeErrMsg}
              minDate={new Date()}
              maxDate={new Date(maxYear, 0, 1)}
              disableFuture={false}
              onChange={e => handlePickerChange('endDate', 'startDate', e)}
              style={pickerCustomStyle}
              inputProps={pickerInputProps}
              MuiStyle={datePickerStyles}
            />
          </Grid>
        ) : null}
      </Grid>

      {retentionVisibility ? (
        <Grid container spacing={5}>
          <Grid item className={classes.gridStyle}>
            <CollectorCardJudicialRetention
              values={values}
              rutBeneficiario={''}
              rutCausante={''}
              editable={editable}
              readOnly={isReadOnly}
            />
          </Grid>
        </Grid>
      ) : null}
      {retentionVisibility ? (
        <Grid container spacing={5}>
          <Grid item className={classes.gridStyle}>
            <SettlementPaymentRetentionCard
              values={valuesLiquidation}
              editable={editable}
              readOnly={isReadOnly}
            />
          </Grid>
        </Grid>
      ) : null}
      <Grid container spacing={5}>
        <Grid item className={styles.backButton}>
          <Button color="primary" variant="outlined" size="large" onClick={() => goBack()}>
            Atras
          </Button>
        </Grid>
        <Grid item className={styles.saveButton}>
          <Button
            color="primary"
            variant="contained"
            size="large"
            disabled={!onlineStatus || !formIsValid || loading || isReadOnly}
            onClick={save}
          >
            {loading && <CircularProgress color="secondary" />}
            Guardar
          </Button>
        </Grid>
      </Grid>
      <Snackbar open={openSnackbar} autoHideDuration={5000} onClose={() => setOpenSnackbar(false)}>
        <Alert onClose={() => setOpenSnackbar(false)} severity={snackBarSeverity}>
          {snackBarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

AssetsAndDiscountsFormulable.propTypes = {
  values: PropTypes.shape({}).isRequired
};

export default AssetsAndDiscountsFormulable;
