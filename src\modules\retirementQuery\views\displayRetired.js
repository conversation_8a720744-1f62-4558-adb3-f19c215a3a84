/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React, { useState, useEffect } from 'react';
import { Button, Grid, Typography } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import { useHistory } from 'react-router-dom';
import { HeaderContent } from 'components';
import { useSelector, useDispatch } from 'react-redux';
import TextField from '@material-ui/core/TextField';
import InputAdornment from '@material-ui/core/InputAdornment';
import SearchIcon from '@material-ui/icons/Search';
import FormControl from '@material-ui/core/FormControl';
import Select from '@material-ui/core/Select';
import MenuItem from '@material-ui/core/MenuItem';
import InputLabel from '@material-ui/core/InputLabel';
import ClearIcon from '@material-ui/icons/Clear';
import useStyles from './styles';
import SettlementsTable from '../components/table/table';
import { useDataDownload, queryPensionsByField } from '../hooks/useDownloadData';
import { queryToSelect, buildQuery, options } from '../hooks/queryBuilder';
import {
  dynamicFormatting,
  pensionCodeIdFormatter,
  isValidPensionCodeID,
  checkDigitValidation,
  RUT_PATTERN
} from '../utils/formatters';
import { setPensionData, setWasDataSearched } from '../actions';

const TITLE = 'Pensionados';
const SUBTITLE = 'Consulta Pensionado';

const DisplayRetired = props => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const history = useHistory();
  const dispatch = useDispatch();

  const data = useSelector(store => store.queryPensions.data);
  const wasDataSearched = useSelector(store => store.queryPensions.wasDataSearched);

  const [inputSelect, setInputSelect] = useState('');
  const [inputText, setInputText] = useState('');
  const [numVar, setNumVar] = useState(0);
  const [isValidInput, setIsValidInput] = useState(true);

  const {
    location: { state }
  } = history;

  useEffect(() => {
    const { isReturning = '', isCommingFromHome, fieldToSearch, valueToSearch, selectedOption } =
      state || {};
    if (!isReturning) {
      dispatch(setPensionData([]));
      dispatch(setWasDataSearched(false));
    }
    if (isCommingFromHome) {
      queryPensionsByField(buildQuery, queryToSelect, fieldToSearch, valueToSearch, dispatch);
      setNumVar(selectedOption);
      setInputText(valueToSearch);
      setInputSelect(fieldToSearch);
      setIsValidInput(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleChange = e => {
    if (options[numVar] !== 'Número de pensión') {
      const dynamicallyFormattedRut = dynamicFormatting(e.target.value);
      setInputText(dynamicallyFormattedRut);
      const isValidPattern = RUT_PATTERN.test(dynamicFormatting(dynamicallyFormattedRut));
      const isValidRut = isValidPattern && checkDigitValidation(dynamicallyFormattedRut);
      setIsValidInput(isValidRut);
    } else {
      const formatted = pensionCodeIdFormatter(e.target.value);
      setInputText(formatted);
      setIsValidInput(isValidPensionCodeID(formatted));
    }
  };

  const handleInputChange = e => {
    setInputSelect(e.target.value.toLowerCase());
    setInputText('');
    setIsValidInput('');
    setNumVar(options.indexOf(e.target.value));
  };

  const onFilteringData = useDataDownload(
    buildQuery,
    queryToSelect,
    options[numVar],
    inputText,
    dispatch
  );

  const errorMessge = option =>
    option !== 'Número de pensión' ? 'Rut incorrecto' : 'Número de pensión incorrecto';

  return (
    <>
      <Grid container>
        <Grid item xs={12}>
          <Typography className={classes.subtitle} color="textSecondary">
            {SUBTITLE}
          </Typography>
        </Grid>
        <Grid item xs={12}>
          <HeaderContent className={classes.title} title={TITLE} />
        </Grid>
        <Grid />
        <Grid container spacing={2}>
          <Grid item className={classes.gridStyle}>
            <FormControl variant="outlined" className={classes.formControl} size="small">
              <InputLabel>Buscar por</InputLabel>
              <Select
                value={options[numVar]}
                onChange={handleInputChange}
                placeholder="Buscar por"
                style={{ width: 220 }}
                label="Buscar por"
                disabled={!onlineStatus}
              >
                {options.map(x => (
                  <MenuItem key={x} value={x}>
                    {x}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item className={classes.gridStyle}>
            <TextField
              className={classes.textField}
              style={{ width: 245 }}
              label={`Digite ${inputSelect}`}
              placeholder={`Digite ${inputSelect}`}
              variant="outlined"
              size="small"
              disabled={!numVar || !onlineStatus}
              value={inputText || ''}
              error={isValidInput === false}
              helperText={isValidInput !== false ? '' : errorMessge(options[numVar])}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <ClearIcon
                      onClick={e => {
                        setInputText('');
                        setIsValidInput('');
                      }}
                    />
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
              onChange={handleChange}
              InputLabelProps={{
                shrink: true
              }}
            />
          </Grid>
          <Grid item xs={7} sm={4} md={3} lg={2}>
            <Button
              variant="contained"
              size="medium"
              color="primary"
              onClick={onFilteringData}
              disabled={!numVar || !onlineStatus || !isValidInput}
            >
              Filtrar
            </Button>
          </Grid>
        </Grid>
        <Grid item xs={12} sm={12}>
          <br />
          <SettlementsTable data={data} wasSearched={wasDataSearched} onlineStatus={onlineStatus} />
        </Grid>
      </Grid>
    </>
  );
};

export default DisplayRetired;
