/* eslint-disable react/prop-types */
/* eslint-disable react/forbid-prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Typography, CardHeader, Card, Button } from '@material-ui/core';
import { useDispatch } from 'react-redux';
import useRouter from '../../../../utils/useRouter';

import styles from '../styles';
import ResumeError from './resumeError';
import ResumeSuccess from './resumeSuccess';
import { cleanTemporaryPensionsErrors } from '../../actions';

const Resume = props => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { fileDataError, isError, data, linkProps } = props;
  const classes = styles();

  const handleOnClick = () => {
    if (isError) {
      dispatch(cleanTemporaryPensionsErrors());
    }
    router.history.push('/nuevas-pensiones/importar');
  };

  const handleResume = err =>
    err ? (
      <>
        <div className={classes.principalErrors}>
          <Typography>
            <b>Existen errores en el archivo que impidieron su importación</b>
          </Typography>
        </div>
        <ResumeError
          errors={fileDataError.errors}
          warnings={fileDataError.warnings}
          requireFields={`Código identificador pensión, Pensión base, Fecha de nacimiento
Beneficiario, Género beneficiario, Nombres causante, Apellido paterno causante, Nombre
cobrante, Apellido paterno cobrante, Nombres beneficiario, Apellido paterno
beneficiario, Rut causante, Rut Cobrante, Rut beneficiario, Número de resolución, Fecha
de resolución, Grado de incapacidad, Fecha de accidente, Tipo de pensión, Tipo de Incapacidad, Tipo de vigencia, Nacionalidad, Transitoria,
Pensión base inicial, Número de siniestro, Fecha de inicio de pensión, Artículo 40, Descuento salud UF,
Grupo familiar, Incremento Ley 19.578, Incremento Ley 19.953, Incremento Ley 20.102,Pensión Base sin incrementos, Trabajo pesado, RUT madre (para pensiones de Orfandad), 
Estado civil, Otra Pensión, Régimen otra pension, Ingreso base, Total pensión devengada`}
        />
      </>
    ) : (
      <ResumeSuccess
        linkProps={linkProps}
        data={data}
        warnings={fileDataError.warnings}
        title="El archivo se cargó correctamente"
      />
    );

  return (
    <Card className={classes.content}>
      <CardHeader title="Resumen de importación" className={classes.headerImport} />
      {handleResume(isError)}

      {!linkProps && (
        <div className={classes.footerButton}>
          <Button onClick={handleOnClick} className={classes.acceptButton}>
            Aceptar
          </Button>
        </div>
      )}
    </Card>
  );
};

Resume.propTypes = {
  fileDataError: PropTypes.object,
  isError: PropTypes.bool,
  linkProps: PropTypes.oneOfType([PropTypes.object, PropTypes.bool]).isRequired
};
Resume.defaultProps = {
  fileDataError: { errors: [], warnings: [] },
  isError: false
};

export default Resume;
