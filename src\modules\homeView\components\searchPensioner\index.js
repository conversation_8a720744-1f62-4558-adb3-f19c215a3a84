import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Grid,
  Button,
  TextField,
  Typography,
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select
} from '@material-ui/core';
import ClearIcon from '@material-ui/icons/Clear';
import SearchIcon from '@material-ui/icons/Search';
import { useHistory } from 'react-router-dom';
import useOnlineStatus from '@rehooks/online-status';

import { options } from './queryBuilder';
import {
  dynamicFormatting,
  pensionCodeIdFormatter,
  isValidPensionCodeID,
  checkDigitValidation,
  RUT_PATTERN
} from '../../utils/formatters';
import useStyles from '../styles';

const SearchBar = ({ title }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const history = useHistory();

  const [inputSelect, setInputSelect] = useState('');
  const [inputText, setInputText] = useState('');
  const [numVar, setNumVar] = useState(0);
  const [isValidInput, setIsValidInput] = useState(true);

  const handleChange = e => {
    if (options[numVar] !== 'Número de pensión') {
      const dynamicallyFormattedRut = dynamicFormatting(e.target.value);
      setInputText(dynamicallyFormattedRut);
      const isValidPattern = RUT_PATTERN.test(dynamicFormatting(dynamicallyFormattedRut));
      const isValidRut = isValidPattern && checkDigitValidation(dynamicallyFormattedRut);
      setIsValidInput(isValidRut);
    } else {
      const formatted = pensionCodeIdFormatter(e.target.value);
      setInputText(formatted);
      setIsValidInput(isValidPensionCodeID(formatted));
    }
  };

  const handleInputChange = e => {
    setInputSelect(e.target.value.toLowerCase());
    setInputText('');
    setIsValidInput('');
    setNumVar(options.indexOf(e.target.value));
  };

  const errorMessage = option =>
    option !== 'Número de pensión' ? 'Rut incorrecto' : 'Número de pensión incorrecto';

  return (
    <div className={classes.root}>
      <Typography className={classes.componentTitle}>{title}</Typography>
      <Grid container className={classes.gridStyleSearchPensioner} spacing={2}>
        <Grid item className={classes.gridStyle}>
          <FormControl variant="outlined" className={classes.formControl} size="small">
            <InputLabel>Buscar por</InputLabel>
            <Select
              value={options[numVar]}
              onChange={handleInputChange}
              placeholder="Buscar por"
              className={classes.textField}
              label="Buscar por"
              disabled={!onlineStatus}
            >
              {options.map(x => (
                <MenuItem key={x} value={x}>
                  {x}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item className={classes.gridStyle}>
          <TextField
            className={classes.textField}
            label={`Digite ${inputSelect}`}
            placeholder={`Digite ${inputSelect}`}
            variant="outlined"
            size="small"
            disabled={!numVar || !onlineStatus}
            value={inputText || ''}
            error={isValidInput === false}
            helperText={isValidInput !== false ? '' : errorMessage(options[numVar])}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <ClearIcon
                    onClick={_e => {
                      setInputText('');
                      setIsValidInput('');
                    }}
                  />
                  <SearchIcon />
                </InputAdornment>
              )
            }}
            onChange={handleChange}
            InputLabelProps={{
              shrink: true
            }}
          />
          <Button
            variant="contained"
            size="medium"
            color="primary"
            onClick={() => {
              history.push({
                pathname: '/pensionados/consulta-pensionados/',
                state: {
                  isCommingFromHome: true,
                  fieldToSearch: options[numVar],
                  valueToSearch: inputText,
                  selectedOption: numVar
                }
              });
            }}
            disabled={!numVar || !onlineStatus || !isValidInput}
          >
            Filtrar
          </Button>
        </Grid>
      </Grid>
    </div>
  );
};

SearchBar.propTypes = {
  title: PropTypes.string
};

SearchBar.defaultProps = {
  title: ''
};

export default SearchBar;
