//------------------------------------------------------------------------------------------------
export const RESET_PREVIRED_FILE = 'RESET_PREVIRED_FILE';
export const resetPreviredFile = () => {
  return {
    type: RESET_PREVIRED_FILE
  };
};
//------------------------------------------------------------------------------------------------
export const SET_PREVIRED_FILE = 'SET_PREVIRED_FILE';
export const setPreviredFile = fileObject => {
  return {
    type: SET_PREVIRED_FILE,
    data: fileObject
  };
};
//------------------------------------------------------------------------------------------------
export const SET_IS_DOWNLOADING = 'SET_IS_DOWNLOADING';
export const setIsDownloading = status => {
  return {
    type: SET_IS_DOWNLOADING,
    data: status
  };
};
