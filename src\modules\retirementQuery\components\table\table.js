/* eslint-disable jsx-a11y/alt-text */
import React, { forwardRef } from 'react';

import { useHistory } from 'react-router-dom';

import MaterialTable from 'material-table';
import {
  FirstPage,
  LastPage,
  NavigateNext,
  NavigateBefore,
  RemoveRedEye
} from '@material-ui/icons';
import PropTypes from 'prop-types';
import useStyles from './styles';

const tableIcons = {
  Edit: forwardRef((props, ref) => <RemoveRedEye {...props} ref={ref} />),
  FirstPage: forwardRef((props, ref) => <FirstPage style={{ width: 18 }} {...props} ref={ref} />),
  LastPage: forwardRef((props, ref) => <LastPage style={{ width: 18 }} {...props} ref={ref} />),
  NextPage: forwardRef((props, ref) => <NavigateNext style={{ width: 18 }} {...props} ref={ref} />),
  PreviousPage: forwardRef((props, ref) => (
    <NavigateBefore style={{ width: 18 }} {...props} ref={ref} />
  ))
};

const headerStyles = { fontWeight: 'bold' };

export default function RetireesTable(props) {
  const { title, data, wasSearched, onlineStatus } = props;
  const classes = useStyles();

  const messageOnStart = () => {
    return (
      <div className={classes.messageNotFound}>
        <img src="/images/find_in_page48dp.svg" className={classes.tableImage} />
        <h5>
          Debe seleccionar un filtro
          <br />
          de búsqueda para poder continuar.
        </h5>
      </div>
    );
  };

  const messageOnNoData = () => {
    return (
      <div className={classes.messageNotFound}>
        <img src="/images/find_in_page48dp.svg" className={classes.tableImage} />
        <h5>
          No hay registros que coincidan
          <br />
          con la búsqueda aplicada.
        </h5>
      </div>
    );
  };

  const history = useHistory();

  const seeMore = rowData => {
    history.push({
      pathname: '/pensionados/consulta-pensionados/ver-mas',
      state: rowData
    });
  };

  return (
    <div>
      <MaterialTable
        icons={tableIcons}
        columns={[
          {
            title: 'Número de pensión',
            field: 'pensionCodeId',
            sorting: false,
            headerStyle: headerStyles
          },
          {
            title: 'Beneficiario',
            field: 'beneficiary',
            sorting: false,
            headerStyle: headerStyles
          },
          {
            title: 'Fecha de nacimiento',
            field: 'dateOfBirth',
            sorting: false,
            headerStyle: headerStyles
          },
          {
            title: 'Rut causante',
            field: 'causantRut',
            sorting: false,
            headerStyle: headerStyles
          },
          {
            title: 'Rut cobrante',
            field: 'collectorRut',
            sorting: false,
            headerStyle: headerStyles
          },
          {
            title: 'Tipo de pensión',
            field: 'pensionType',
            sorting: false,
            headerStyle: headerStyles
          },
          {
            title: 'Tipo de vigencia',
            field: 'validityType',
            sorting: false,
            headerStyle: headerStyles
          },
          {
            title: 'Fecha de fin de vigencia',
            field: 'endDateOfValidity',
            sorting: false,
            headerStyle: headerStyles
          }
        ]}
        title={title}
        data={data}
        options={{
          search: false,
          showTitle: !!title,
          actionsColumnIndex: -1,
          paging: false,
          toolbar: false,
          draggable: false,
          maxBodyHeight: 700
        }}
        localization={{
          body: {
            emptyDataSourceMessage: wasSearched ? messageOnNoData() : messageOnStart()
          },
          header: {
            actions: ''
          }
        }}
        editable={{
          isEditable: () => false
        }}
        actions={[
          () => ({
            icon: RemoveRedEye,
            tooltip: 'Ver más',
            onClick: (event, rowData) => seeMore(rowData),
            disabled: !onlineStatus
          })
        ]}
      />
    </div>
  );
}

RetireesTable.propTypes = {
  title: PropTypes.string,
  data: PropTypes.oneOfType([PropTypes.array, PropTypes.func]).isRequired,
  wasSearched: PropTypes.bool,
  onlineStatus: PropTypes.bool
};

RetireesTable.defaultProps = {
  title: null,
  wasSearched: false,
  onlineStatus: true
};
