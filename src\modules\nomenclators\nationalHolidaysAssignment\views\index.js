/* eslint-disable no-console */
/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React from 'react';
import { renderRoutes } from 'react-router-config';
import { Page, SnackbarManager, HeaderContent } from 'components';
import PropTypes from 'prop-types';
import useStyles from './styles';
import { useSnackbar } from './useSnackbar';
import NationalHoliDaysAssignment from './nationalHolidaysAssignment';
import NationalHolidaysResume from './nationalHolidaysResume';

const NationalHoliDaysAssignmentPage = ({ route }) => {
  const classes = useStyles();

  const {
    text,
    setText,
    error,
    setError,
    successSnackbar,
    errorSnackbar,
    setSuccessSnackbar,
    setErrorSnackbar
  } = useSnackbar();

  const onSuccessSnackbar = val => {
    setText(val);
    setSuccessSnackbar(true);
    setErrorSnackbar(false);
  };

  const onErrorSnackbar = err => {
    setError(err);
    setSuccessSnackbar(false);
    setErrorSnackbar(true);
  };

  return (
    <Page className={classes.root} title="Asignación de Aguinaldos">
      <HeaderContent overline="Configuraciones / Mantenedores" title="Asignación de Aguinaldos" />
      {renderRoutes(route.routes, { onSuccessSnackbar, onErrorSnackbar })}
      <SnackbarManager
        handleRoute={false}
        error={error}
        text={text}
        successSnackbar={successSnackbar}
        errorSnackbar={errorSnackbar}
        setErrorSnackbar={setErrorSnackbar}
        setSuccessSnackbar={setSuccessSnackbar}
      />
    </Page>
  );
};

NationalHoliDaysAssignmentPage.propTypes = {
  route: PropTypes.shape({
    path: PropTypes.string,
    routes: PropTypes.array,
    component: PropTypes.func
  }).isRequired
};

// eslint-disable-next-line import/prefer-default-export
export { NationalHoliDaysAssignmentPage, NationalHoliDaysAssignment, NationalHolidaysResume };
