/* eslint-disable react/forbid-prop-types */
/* eslint-disable import/no-unresolved */
import React from 'react';
import PropTypes from 'prop-types';
import { TableCell, TableRow } from '@material-ui/core';
import Table from 'components/Table';

import { makeStyles } from '@material-ui/styles';

const useStyles = makeStyles(_theme => ({
  content: {
    margin: 30
  },
  acceptButton: {
    display: 'flex',
    margin: 15,
    justifyContent: 'center'
  },
  requiredFields: {
    margin: 15
  }
}));

const errorHeaders = [{ label: 'Fila' }, { label: 'Mensaje de error' }];
const warningsHeaders = [{ label: 'Fila' }, { label: 'Advertencias' }];

const renderTableData = result =>
  result.map(({ row, message }) => (
    <TableRow hover key={`${row}`}>
      <TableCell align="left" style={{ width: '10%' }}>
        {row}
      </TableCell>
      <TableCell align="left" style={{ width: '90%' }}>
        {message}
      </TableCell>
    </TableRow>
  ));

const renderTableHeaders = headers =>
  headers.map((key, index) => (
    <TableCell key={`${index + key.label}`} align="left">
      <b>{key.label}</b>
    </TableCell>
  ));

const ResumeError = props => {
  const classes = useStyles();
  const { errors, warnings, requireFields } = props;

  return (
    <>
      <Table
        renderHeader={() => renderTableHeaders(errorHeaders)}
        renderTable={() => renderTableData(errors)}
      />
      {warnings.length > 0 && (
        <>
          <br />
          <Table
            renderHeader={() => renderTableHeaders(warningsHeaders)}
            renderTable={() => renderTableData(warnings)}
          />
        </>
      )}
      <br />
      <div className={classes.requiredFields}>
        <b>* Campos obligatorios: </b>
        {requireFields}
      </div>      
      <div className={classes.requiredFields}>
        <b>** Transitoria igual a si entonces campo Días pagados pensión transitoria es obligatorio </b>        
      </div>      
    </>
  );
};

ResumeError.propTypes = {
  errors: PropTypes.array.isRequired,
  warnings: PropTypes.array.isRequired,
  requireFields: PropTypes.string.isRequired
};
ResumeError.defaultProptypes = {
  errors: [],
  warnings: [],
  requireFields: ''
};

export default ResumeError;
