<system.webServer>
    <rewrite>
      <rules>        
        <rule name="Main Rule" stopProcessing="true">
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
  <security>
    <requestFiltering removeServerHeader="true" />
  </security>
</system.webServer>