/* eslint-disable react/prop-types */
import React, { useState, useEffect, useRef } from 'react';
import Modal from 'react-modal';
import { useSelector, useDispatch } from 'react-redux';
import { Button, Grid, Typography } from '@material-ui/core';
import useStyles from './style';

import {
  setAgreesToChangeCollectorRut,
  setIsFirstChangeInCollectorRut,
  setCollectorRut,
  setWasModifiedAFieldThatRequiresAFile,
  setModifiedFieldErrors,
  setBank,
  setPaymentGateway,
  setAccountNumber,
  setBeneficiaryEmail,
  setBranchOffice
} from '../../actions';

import bankList from '../../utils/bankList';

import { paymentGatewayList } from '../../utils/paymentGatewayList';

const WARNING_MESSAGE =
  'Al cambiar el cobrante, debe actualizar los datos de la vía de pago. si confirma se limpiará la información de Pago de liquidación';

const CollectorChangeModal = ({ values }) => {
  const styles = useStyles();
  const mountedRef = useRef();
  const dispatch = useDispatch();

  const collectorRut = useSelector(store => store.queryPensions.collectorRut);
  const isFirstChangeInCollectorRut = useSelector(
    store => store.queryPensions.isFirstChangeInCollectorRut
  );
  const wasInfoLoaded = useSelector(store => store.queryPensions.wasInfoLoaded);
  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);

  const [isOpen, setIsOpen] = useState(false);

  const close = () => setIsOpen(false);

  const accept = () => {
    close();
    dispatch(setAgreesToChangeCollectorRut(true));
    dispatch(setBank(bankList[0]));
    dispatch(setPaymentGateway(paymentGatewayList[0]));
    dispatch(setAccountNumber(''));
    dispatch(setBeneficiaryEmail(''));
    dispatch(
      setModifiedFieldErrors({
        ...modifiedFieldErrors,
        accountNumber: true,
        bank: true,
        paymentGateway: true
      })
    );
  };

  const cancel = () => {
    close();
    dispatch(setWasModifiedAFieldThatRequiresAFile(false));
    dispatch(setCollectorRut(values.collectorRut));
    dispatch(setIsFirstChangeInCollectorRut(true));
    dispatch(setModifiedFieldErrors({ ...modifiedFieldErrors, collectorRut: false }));
  };

  const setInitialStateNoPaymentGateway = () => {
    dispatch(setPaymentGateway(paymentGatewayList[0]));
    dispatch(setBank(bankList[0]));
    dispatch(setAccountNumber(''));
    dispatch(setBranchOffice(values.branchOffice));
    dispatch(
      setModifiedFieldErrors({
        ...modifiedFieldErrors,
        accountNumber: true,
        bank: true,
        paymentGateway: true
      })
    );
  };

  const setInitialStatePaymentGateway = () => {
    dispatch(setPaymentGateway(values.paymentGateway));
    dispatch(setBank(values.bank));
    dispatch(setAccountNumber(values.accountNumber));
    dispatch(setBranchOffice(values.branchOffice));
    dispatch(
      setModifiedFieldErrors({
        ...modifiedFieldErrors,
        paymentGateway: false
      })
    );
  };

  useEffect(() => {
    if (
      mountedRef.current &&
      wasInfoLoaded &&
      isFirstChangeInCollectorRut &&
      values.collectorRut !== collectorRut
    ) {
      setIsOpen(true);
      dispatch(setWasModifiedAFieldThatRequiresAFile(true));
      dispatch(setIsFirstChangeInCollectorRut(false));
    }
    if (values.collectorRut === collectorRut) {
      dispatch(setIsFirstChangeInCollectorRut(true));
      dispatch(setAgreesToChangeCollectorRut(false));
      dispatch(setWasModifiedAFieldThatRequiresAFile(false));
      values.paymentGateway && setInitialStatePaymentGateway();
      !values.paymentGateway && setInitialStateNoPaymentGateway();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [collectorRut]);

  useEffect(() => {
    mountedRef.current = true;
  }, []);

  return (
    <>
      <Modal isOpen={isOpen} className={styles.modalCollector}>
        <Typography>{WARNING_MESSAGE}</Typography>

        <Grid container justify="flex-end">
          <Grid item>
            <Button className={styles.acceptButton} size="small" onClick={accept}>
              Aceptar
            </Button>
          </Grid>
          <Grid item>
            <Button className={styles.cancelButton} size="small" onClick={cancel}>
              Cancelar
            </Button>
          </Grid>
        </Grid>
      </Modal>
    </>
  );
};

export default CollectorChangeModal;
