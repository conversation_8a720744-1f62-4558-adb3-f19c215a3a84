import { sendConcurencyData, sendFactorData } from '../services/capitalFactors.service';
import {
  setHasConcurrenciesFile,
  setHasFactorsFile,
  setIsDownloading,
  setFactorsFileData,
  setConcurrenciesFileData
} from '../actions';

const sendData = async ({
  factorsFileData = [],
  concurrenciesFileData = [],
  progress,
  dispatch,
  onErrorSnackbar,
  onSuccessSnackbar
}) => {
  dispatch(setIsDownloading(true));
  let isFactorCorrectlyUploaded = true;
  let isConcurrencycorrectlyUploaded = true;
  let errorMessage = '';
  progress.show();
  if (factorsFileData.length > 0) {
    [isFactorCorrectlyUploaded, errorMessage] = await sendFactorData(factorsFileData);
    isFactorCorrectlyUploaded && dispatch(setHasFactorsFile(true));
  }
  if (concurrenciesFileData.length > 0) {
    isConcurrencycorrectlyUploaded = await sendConcurencyData(concurrenciesFileData);
    isConcurrencycorrectlyUploaded && dispatch(setHasConcurrenciesFile(true));
  }
  dispatch(setIsDownloading(false));
  dispatch(setFactorsFileData([]));
  dispatch(setConcurrenciesFileData([]));
  if (!isFactorCorrectlyUploaded || !isConcurrencycorrectlyUploaded) {
    onErrorSnackbar(errorMessage);
    progress.hide();

    return false;
  }
  onSuccessSnackbar('Importación Exitosa');
  progress.hide();
  return true;
};

const useSendData = ({
  factorsFileData,
  concurrenciesFileData,
  dispatch,
  progress,
  onErrorSnackbar,
  onSuccessSnackbar
}) => {
  return () => {
    sendData({
      factorsFileData,
      concurrenciesFileData,
      dispatch,
      progress,
      onErrorSnackbar,
      onSuccessSnackbar
    });
  };
};

export default useSendData;
