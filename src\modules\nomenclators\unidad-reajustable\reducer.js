import { SET_NOM_SALARY, UPDATE_NOM_SALARY } from './actions';

const initialState = {
  data: [],
  errors: []
}; // attr data: array of afps

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case UPDATE_NOM_SALARY: {
      const data = [...state.data];
      data[data.indexOf(action.data.prevSalary)] = action.data.newSalary;
      return { ...state, data };
    }
    case SET_NOM_SALARY: {
      const data = [...action.data.salary];
      return { ...state, data };
    }
    default:
      return state;
  }
}
