export const SET_NATIONAL_HOLIDAYS_FILE_ERROR = 'SET_NATIONAL_HOLIDAYS_FILE_ERROR';
export const setFileError = error => {
  return {
    type: SET_NATIONAL_HOLIDAYS_FILE_ERROR,
    data: { fileError: error }
  };
};

export const CLEAN_NATIONAL_HOLIDAYS_FILE_ERROR = 'CLEAN_NATIONAL_HOLIDAYS_FILE_ERROR';
export const cleanFileError = () => {
  return {
    type: CLEAN_NATIONAL_HOLIDAYS_FILE_ERROR
  };
};

export const SET_HOLIDAYS_FILEDATA_ERROR = 'SET_HOLIDAYS_FILEDATA_ERROR';
export const setHolidaysFileDataError = errors => {
  return {
    type: SET_HOLIDAYS_FILEDATA_ERROR,
    data: { errors, isError: errors.length > 0 }
  };
};
