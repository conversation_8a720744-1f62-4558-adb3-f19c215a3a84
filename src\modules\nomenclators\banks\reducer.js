import { CREATE_NOM_BANK, DELETE_NOM_BANK, SET_NOM_BANK, UPDATE_NOM_BANK } from './actions';

const initialState = {
  data: [],
  errors: []
}; // attr data: array of banks

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case CREATE_NOM_BANK: {
      const { bank } = action.data;
      return {
        ...state,
        data: [...state.data, bank]
      };
    }
    case UPDATE_NOM_BANK: {
      const data = [...state.data];
      data[data.indexOf(action.data.prevBank)] = action.data.newBank;
      return { ...state, data };
    }
    case DELETE_NOM_BANK: {
      const data = [...state.data];
      data.splice(data.indexOf(action.data.bank), 1);
      return { ...state, data };
    }
    case SET_NOM_BANK: {
      const data = [...action.data.banks];
      return { ...state, data };
    }
    default:
      return state;
  }
}
