//------------------------------------------------------------------------------------------------
export const CREATE_NOM_MOTIVE = 'CREATE_NOM_MOTIVE';
export const createNomenclatorMotive = motive => {
  return {
    type: CREATE_NOM_MOTIVE,
    data: { motive }
  };
};
//------------------------------------------------------------------------------------------------
export const DELETE_NOM_MOTIVE = 'DELETE_NOM_MOTIVE';
export const deleteNomenclatorMotive = motive => {
  return {
    type: DELETE_NOM_MOTIVE,
    data: { motive }
  };
};
//------------------------------------------------------------------------------------------------
export const UPDATE_NOM_MOTIVE = 'UPDATE_NOM_MOTIVE';
export const updateNomenclatorMotive = (prevMotive, newMotive) => {
  return {
    type: UPDATE_NOM_MOTIVE,
    data: { prevMotive, newMotive }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_NOM_MOTIVE = 'SET_NOM_MOTIVE';
export const loadNomenclatorMotive = motive => {
  return {
    type: SET_NOM_MOTIVE,
    data: { motive }
  };
};
