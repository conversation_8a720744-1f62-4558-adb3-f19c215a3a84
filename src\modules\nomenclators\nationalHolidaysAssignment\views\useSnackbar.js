/* eslint-disable import/no-unresolved */
import { useState } from 'react';

// eslint-disable-next-line import/prefer-default-export
export const useSnackbar = () => {
  const [text, setText] = useState('');
  const [error, setError] = useState('');
  const [successSnackbar, setSuccessSnackbar] = useState(false);
  const [errorSnackbar, setErrorSnackbar] = useState(false);

  return {
    text,
    error,
    successSnackbar,
    errorSnackbar,
    setSuccessSnackbar,
    setErrorSnackbar,
    setText,
    setError
  };
};
