import { getHistoricalSettlementsForPDFFile } from '../services/queryPensions.service';
import formatPDFData from '../utils/formatPDFData';

const NoSettlementsFound =
  'El pensionado no tiene liquidaciones generadas dentro del rango seleccionado';

const getPDFData = async ({
  beneficiaryRut,
  causantRut,
  pensionCodeId,
  lowerDate,
  upperDate,
  setIsCurrentlyDownloading,
  progress,
  onErrorSnackbar,
  setSettlementData,
  pensionerData
}) => {
  progress.show();
  setIsCurrentlyDownloading(true);
  const { data, isError } = await getHistoricalSettlementsForPDFFile({
    beneficiaryRut,
    causantRut,
    pensionCodeId,
    lowerDate,
    upperDate
  });

  if (isError || !data?.historicalSettlements?.length) {
    onErrorSnackbar(NoSettlementsFound);
    progress.hide();
    setIsCurrentlyDownloading(false);
    return;
  }
  setSettlementData(formatPDFData({ ...pensionerData, ...data, lowerDate, upperDate }));
};

const handleDownloadPDFData = ({
  beneficiaryRut,
  causantRut,
  pensionCodeId,
  lowerDate,
  upperDate,
  setIsCurrentlyDownloading,
  progress,
  onErrorSnackbar,
  setSettlementData,
  pensionerData
}) => {
  return () =>
    getPDFData({
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      lowerDate,
      upperDate,
      setIsCurrentlyDownloading,
      progress,
      onErrorSnackbar,
      setSettlementData,
      pensionerData
    });
};

export default handleDownloadPDFData;
