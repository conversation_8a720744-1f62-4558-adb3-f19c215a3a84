import { CREATE_NOM_MOTIVE, DELETE_NOM_MOTIVE, SET_NOM_MOTIVE, UPDATE_NOM_MOTIVE } from './actions';

const initialState = {
  data: [],
  errors: []
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case CREATE_NOM_MOTIVE: {
      const { motive } = action.data;
      return {
        ...state,
        data: [...state.data, motive]
      };
    }
    case UPDATE_NOM_MOTIVE: {
      const data = [...state.data];
      data[data.indexOf(action.data.prevMotive)] = action.data.newMotive;
      return { ...state, data };
    }
    case DELETE_NOM_MOTIVE: {
      const data = [...state.data];
      data.splice(data.indexOf(action.data.motive), 1);
      return { ...state, data };
    }
    case SET_NOM_MOTIVE: {
      const data = [...action.data.motive];
      return { ...state, data };
    }
    default:
      return state;
  }
}
