/* eslint-disable import/prefer-default-export */

import {
  servipagBranchOffices,
  deleteNomenclatorServipag,
  updateNomenclatorServipag,
  createNomenclatorServipag
} from '../actions';

export const getServipagBranchOffices = () => async (
  dispatch,
  _getState,
  { api, axiosRequest }
) => {
  const { data } = await axiosRequest.get(`${api}/nomenclators/servipag`).catch(err => {
    // eslint-disable-next-line no-console
    console.error('err:', err);

    return { data: [] };
  });
  await dispatch(servipagBranchOffices(data));
};

export const processServipag = (prevServipag, newServipag, operation) => async (
  dispatch,
  _getState,
  { api, axiosRequest }
) => {
  let createdServipag = newServipag;
  if (operation === 'create') {
    const {
      data: { result, error, isError }
    } = await createServipag({ axiosRequest, api, servipag: createdServipag });
    if (isError) {
      throw new Error(error.code);
    }
    createdServipag = result;

    await dispatch(createNomenclatorServipag(createdServipag));
  }
  if (operation === 'delete') {
    await deleteServipag({ axiosRequest, api, servipag: createdServipag });
    await dispatch(deleteNomenclatorServipag(createServipag));
  }
  if (operation === 'update') {
    const {
      data: { error, isError }
    } = await updateServipag({ axiosRequest, api, servipag: createdServipag });
    if (isError) {
      throw new Error(error.code);
    }

    await dispatch(updateNomenclatorServipag(prevServipag, createdServipag));
  }
};
const createServipag = async ({ axiosRequest, api, servipag }) =>
  axiosRequest.post(`${api}/nomenclators/servipag`, { servipag });

const updateServipag = async ({ axiosRequest, api, servipag }) =>
  axiosRequest.put(`${api}/nomenclators/servipag`, { servipag });

const deleteServipag = async ({ axiosRequest, api, servipag }) =>
  axiosRequest.delete(`${api}/nomenclators/servipag/${servipag.id}`);
