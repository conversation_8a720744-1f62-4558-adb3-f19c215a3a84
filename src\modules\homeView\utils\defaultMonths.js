const months = {
  0: '<PERSON><PERSON>',
  1: '<PERSON><PERSON><PERSON>',
  2: '<PERSON><PERSON>',
  3: '<PERSON><PERSON><PERSON>',
  4: '<PERSON>',
  5: '<PERSON><PERSON>',
  6: '<PERSON>',
  7: 'Agos<PERSON>',
  8: 'Septiembre',
  9: 'Octubre',
  10: '<PERSON><PERSON><PERSON>',
  11: 'Diciembre'
};

// eslint-disable-next-line import/prefer-default-export
export const defaultPlotData = totalToFill => {
  const result = [];

  for (let index = 0; index < totalToFill; index += 1) {
    const backMonth = new Date();
    backMonth.setMonth(backMonth.getMonth() - (totalToFill - (index + 1)));

    result.push({
      spentProfessionalDisease: 0,
      spentAccident: 0,
      spentSurvival: 0,
      totalSpent: 0,
      month: months[backMonth.getMonth()],
      date: backMonth,
      validPensioners: 0,
      year: backMonth.getFullYear()
    });
  }
  return result;
};
