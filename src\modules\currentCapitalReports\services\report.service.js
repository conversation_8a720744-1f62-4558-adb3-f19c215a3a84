/* eslint-disable no-console */

import { axiosRequest, excelRequest } from '../../../services/axiosRequest';
const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

//------------------------------------------------------------------------------------------------
export const getInactivationsReactivationsReport = async () => {
  const { data, isError } = await excelRequest
    .get(`${api}/reports/inactivation-reactivation`)
    .catch(err => {
      console.error(err);
      return { data: null, isError: true };
    });

  return { data, isError };
};
//------------------------------------------------------------------------------------------------
export const getReportsCurrentDate = async () => {
  const { data } = await axiosRequest
    .get(`${api}/reports/inactivation-reactivation/time`)
    .catch(err => {
      console.error(err);
      return { data: '', isError: true };
    });
  return data;
};
