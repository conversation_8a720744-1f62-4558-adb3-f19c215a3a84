/* eslint-disable react/prop-types */
import React, { useContext } from 'react';
import { Redirect } from 'react-router-dom';
import Cookies from 'universal-cookie';

import { AppContext } from '../../../provider/app';

const NO_READ_REGEX = /NoRead/i;

const ProtectedRoute = props => {
  const { module, viewNumber, component: Component, ...rest } = props;

  const { loggedUser } = useContext(AppContext);

  if (!loggedUser) {
    const cookies = new Cookies();
    cookies.remove('user', { path: '/' });
    return <Redirect to="/auth/login" />;
  }

  const { userViews } = loggedUser;
  const viewObj = userViews.find(
    view =>
      view.viewNumber === viewNumber &&
      view.module.match(new RegExp(module, 'i')) &&
      !NO_READ_REGEX.test(view.permission)
  );

  if (module && viewNumber && !viewObj) {
    return <Redirect to="/errors/error-500" />;
  }

  return <Component {...rest} role={viewObj} />;
};

export default ProtectedRoute;
