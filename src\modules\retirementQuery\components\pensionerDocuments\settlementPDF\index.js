/* eslint-disable react/prop-types */
import React from 'react';
import { Document, Image, Page, Text, View } from '@react-pdf/renderer';

import styles from './styles';

const UpperLeftComponent = () => (
  <View style={styles.upperLeftContainer}>
    <Text style={styles.text}>Asociación Chilena de Seguridad </Text>
    <Text style={styles.text}>RUT: 70.360.100-6</Text>
    <Text style={styles.text}>Av. Vicuña <PERSON> 186</Text>
    <Text style={styles.text}>Providencia</Text>
  </View>
);

const UpperRightCorner = ({ pensionCodeId = '#######' }) => (
  <View style={styles.upperRightContainer}>
    <Text style={styles.text}>{`Pensión: ${pensionCodeId}`}</Text>
    <Image style={styles.image} src="/Logo_ACHS.png" />
  </View>
);

const MainHeader = ({
  beneficiaryFullName = '',
  beneficiaryRut = '',
  startingDate = '',
  endingDate = '',
  currentDate = ''
}) => {
  return (
    <View style={styles.MainHeaderContainer}>
      <Text style={styles.headerTitle}>Certificado Pensiones Percibidas</Text>
      <Text style={styles.headerDate}>{`Santiago, ${currentDate}`}</Text>
      <Text style={styles.text}>
        {`La Asociación Chilena de Seguridad (ACHS), certifica que el pensionado ${beneficiaryFullName},
      Rut N° ${beneficiaryRut} en su calidad de pensionado de la Ley 16.744, para el periodo ${startingDate}-${endingDate}, ha percibido:`}
      </Text>
    </View>
  );
};

const TableOfSettlements = ({
  historicalSettlements = [],
  totalTaxablePension = 0,
  totalNetPension = 0,
  totalDiscounts = 0
}) => {
  return (
    <>
      <View style={styles.tableContainer}>
        <View style={styles.tableHeader}>
          <Text style={styles.textTable}>Periodo</Text>
          <Text style={styles.textTable}>Pensión Imponible</Text>
          <Text style={styles.textTable}>Descuentos</Text>
          <Text style={styles.textTable}>Pensión Líquida</Text>
        </View>

        {historicalSettlements.map(row => (
          <View style={styles.tableBody}>
            <Text style={styles.textTable}>{row?.updatedAt}</Text>
            <Text style={styles.textTable}>{row?.taxablePension}</Text>
            <Text style={styles.textTable}>{row?.totalDiscounts}</Text>
            <Text style={styles.textTable}>{row?.netPension}</Text>
          </View>
        ))}
        <View style={styles.tableTotals}>
          <Text style={styles.textTable}>Total</Text>
          <Text style={styles.textTable}>{totalTaxablePension}</Text>
          <Text style={styles.textTable}>{totalDiscounts}</Text>
          <Text style={styles.textTable}>{totalNetPension}</Text>
        </View>
      </View>
    </>
  );
};

const LowerBody = () => {
  return (
    <View styles={styles.lowerBodyContainer}>
      <Text style={styles.text}>
        Se extiende el presente certificado, a solicitud del pensionado para los fines que este
        estime conveniente, sin posterior responsabilidad para esta MUTUAL.
      </Text>
      <Text style={styles.text}>
        Para efectos tributarios las pensiones de la Ley 16.744 no constituyen RENTA (Art.17).
      </Text>
    </View>
  );
};

const Signature = ({ pecBoss = '' }) => {
  return (
    <View styles={styles.signatureContainer}>
      <Text style={styles.textFooter}>{pecBoss}</Text>
      <Text style={styles.textFooter}>Jefe Depto. Prestaciones Económicas</Text>
    </View>
  );
};

const SettlementDocument = ({ data }) => {
  const {
    beneficiaryFullName,
    beneficiaryRut,
    lowerDate,
    upperDate,
    currentDate,
    historicalSettlements,
    totalTaxablePension,
    totalNetPension,
    sumOfTotalDiscounts,
    pecBoss,
    pensionCodeId
  } = data;
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.upperContainer}>
          <UpperLeftComponent />
          <UpperRightCorner pensionCodeId={pensionCodeId} />
        </View>
        <MainHeader
          beneficiaryFullName={beneficiaryFullName}
          beneficiaryRut={beneficiaryRut}
          startingDate={lowerDate}
          endingDate={upperDate}
          currentDate={currentDate}
        />

        <TableOfSettlements
          historicalSettlements={historicalSettlements}
          totalTaxablePension={totalTaxablePension}
          totalNetPension={totalNetPension}
          totalDiscounts={sumOfTotalDiscounts}
        />

        <LowerBody />
        <Signature pecBoss={pecBoss} />
      </Page>
    </Document>
  );
};

export default SettlementDocument;
