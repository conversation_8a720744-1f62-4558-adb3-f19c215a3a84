/* eslint-disable react/prop-types */
/* eslint-disable react/forbid-prop-types */
import { Button, Grid, Tooltip, IconButton } from '@material-ui/core';
import { ArrowDownward, Clear, Check, DeleteOutline, Edit } from '@material-ui/icons';
import MaterialTable, { MTableEditRow, MTableToolbar } from 'material-table';
import PropTypes from 'prop-types';
import React, { forwardRef, useRef, useState, useEffect } from 'react';
import { ValidatorForm } from 'react-material-ui-form-validator';
import useOnlineStatus from '@rehooks/online-status';
import { checkWritePermission } from 'utils/checkUserPermission';
import { defaultFormatter } from '../../validator/validField';

import SecureTextField, { matchRule } from './secureTextField';
import SelectTextField from './selectTextField';
import useStyles from './styles';

const tableIcons = {
  Check: forwardRef((props, ref) => <Check {...props} ref={ref} />),
  Edit: forwardRef((props, ref) => <Edit {...props} ref={ref} />),
  Delete: forwardRef((props, ref) => <DeleteOutline {...props} ref={ref} />),
  Clear: forwardRef((props, ref) => <Clear {...props} ref={ref} />),
  SortArrow: forwardRef((props, ref) => <ArrowDownward {...props} ref={ref} />)
};
const isRowValid = ({ motive, option }) => {
  return matchRule(motive) && matchRule(option);
};

const lengthObject = { name: '100', option: '10', default: '20' };
const getMaxLength = fieldName => ({ maxLength: lengthObject[fieldName] || '20' });
const editableFn = (fieldName, labelName, validators, errorMessages, formater) => ({
  onChange,
  value
}) => (
  <SecureTextField
    name={fieldName}
    size="small"
    displayName={labelName}
    inputprops={getMaxLength(fieldName)}
    validators={validators}
    errorMessages={errorMessages}
    onChange={e => onChange(formater ? formater(e.target.value) : defaultFormatter(e.target.value))}
    onBlur={e => onChange(e.target.value.trim())}
    value={value || ''}
  />
);

const SelectFn = (fieldName, labelName) => ({ onChange, value = '' }) => {
  return (
    <SelectTextField
      name={fieldName}
      displayName={labelName}
      size="small"
      inputprops={getMaxLength(fieldName)}
      onChange={e => onChange(e.target.value)}
      value={value}
    />
  );
};

export default function MotiveTable(props) {
  const { data, onCreate, onUpdate, onDelete, onSubmit, enabledButton, userRole } = props;
  const [disabledAddBtn, setDisabledAddBtn] = useState(enabledButton);
  const onlineStatus = useOnlineStatus();
  const hasWritePermission = checkWritePermission(userRole);

  useEffect(() => {
    setDisabledAddBtn(enabledButton);
  }, [enabledButton]);

  const formRef = useRef(null);
  const classes = useStyles();

  const nameComponent = editableFn('name', 'Nombre');
  const selectComponent = SelectFn('option', 'Opcion');

  return (
    <ValidatorForm onSubmit={onSubmit} ref={formRef} instantValidate>
      <MaterialTable
        components={{
          Action: p => {
            let { action, data = {} } = p;
            const { isDefault = false } = data;
            if (typeof p.action === 'function') {
              action = p.action();
            }

            return (
              <Tooltip title={!action.disabled && !p.disabled ? action.tooltip : ''}>
                <IconButton
                  onClick={event => {
                    if (action.tooltip === 'Editar' || action.tooltip === 'Eliminar') {
                      setDisabledAddBtn(true);
                    }
                    action.onClick(event, p.data);
                  }}
                  color="inherit"
                  variant="contained"
                  className={classes.iconButtonStyle}
                  size={p.size}
                  disabled={
                    !hasWritePermission ||
                    action.disabled ||
                    p.disabled ||
                    !onlineStatus ||
                    isDefault
                  }
                >
                  {action.icon.render()}
                </IconButton>
              </Tooltip>
            );
          },
          Toolbar: p => {
            return (
              <Grid container justify="space-between" className={classes.gridContainerStyle}>
                <MTableToolbar {...p} actions={[]} />
                <Grid item className={classes.gridStyle}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                      setDisabledAddBtn(true);
                      p.actions[0].onClick();
                    }}
                    disabled={!hasWritePermission || disabledAddBtn || !onlineStatus}
                  >
                    Agregar Motivo
                  </Button>
                </Grid>
              </Grid>
            );
          },
          EditRow: p => (
            <MTableEditRow
              {...p}
              onEditingCanceled={(mode, rowData) => {
                setDisabledAddBtn(false);
                return p.onEditingCanceled(mode, rowData);
              }}
              onEditingApproved={async (mode, newData, oldData) => {
                if (!isRowValid(newData)) {
                  formRef.current.submit();
                  return null;
                }

                return p.onEditingApproved(mode, newData, oldData);
              }}
            />
          )
        }}
        icons={tableIcons}
        columns={[
          {
            title: 'Motivo',
            field: 'motive',
            defaultSort: 'asc',
            editComponent: nameComponent
          },
          {
            title: 'Haber/Descuento',
            field: 'option',
            sorting: false,
            width: 500,
            editComponent: selectComponent
          }
        ]}
        data={data}
        options={{
          addRowPosition: 'last',
          thirdSortClick: false,
          paging: false,
          search: false,
          showTitle: false,
          actionsColumnIndex: -1,
          toolbar: true
        }}
        localization={{
          body: {
            emptyDataSourceMessage: 'No existen registros ingresados',
            deleteTooltip: 'Eliminar',
            editTooltip: 'Editar',
            editRow: {
              deleteText: '¿Está seguro que desea eliminar el elemento?',
              saveTooltip: 'Aceptar',
              cancelTooltip: 'Cancelar'
            }
          },
          header: {
            actions: 'Acciones'
          }
        }}
        editable={{
          onRowAdd: newData =>
            new Promise(async (resolve, reject) => {
              if (!isRowValid(newData)) {
                setDisabledAddBtn(false);

                return reject();
              }
              try {
                await onCreate({ ...newData }, {});
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return resolve();
              }
            }),
          onRowUpdate: (newData, oldData) =>
            new Promise(async (resolve, reject) => {
              if (!isRowValid(newData)) {
                return reject();
              }
              try {
                await onUpdate(newData, oldData);
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return resolve();
              }
            }),
          onRowDelete: oldData =>
            new Promise(async (resolve, reject) => {
              try {
                await onDelete(oldData);
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return reject();
              }
            })
        }}
      />
    </ValidatorForm>
  );
}

MotiveTable.propTypes = {
  data: PropTypes.oneOfType([PropTypes.array, PropTypes.func]).isRequired,
  onCreate: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  onSubmit: PropTypes.func
};

MotiveTable.defaultProps = {
  onSubmit: () => { /*any*/ }
};
