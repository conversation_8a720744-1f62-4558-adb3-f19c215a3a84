import React from 'react';
import { Button } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import useDownloadReport from './hooks/useDownloadReport';
import useStyles from './styles';

// eslint-disable-next-line react/prop-types
const DownloadButton = ({ onErrorSnackbar }) => {
  const [isDownloading, handleDownloadReport, cleanDownload] = useDownloadReport(onErrorSnackbar);
  const onlineStatus = useOnlineStatus();
  const classes = useStyles();
  return (
    <>
      <Button
        className={classes.buttom}
        onClick={handleDownloadReport}
        variant="contained"
        color="primary"
        onMouseUp={cleanDownload}
        disabled={isDownloading || !onlineStatus}
      >
        Exportar
      </Button>
    </>
  );
};

export default DownloadButton;
