import { setPdfFile, setWasPdfUploaded } from '../actions';

const ON_SUCCESS_TEXT = 'Importación Exitosa';

const AFP_AFFILIATION = /^Certificado_de_afiliacion_AFP\.pdf$/i;
const INSTITUTIONAL_PATIENT_FILENAME = /^Certificado_neurologico\.pdf$/i;
const NON_INSTITUTIONAL_PATIENT_FILENAME = /^Dictamen_de_tribunales\.pdf$/i;
const COLLECTOR_RUT_CHANGE = /^Poder_notarial_para_cambio_de_cobrante.pdf$/i;
const SI = /s[ií]/i;
const NO = /no/i;
const MAX_FILE_SIZE = 10485760;

const extractValueAndKey = ({ values, afpAffiliation, institutionalPatient, collectorRut, attachNeurologicalCertificate }) => {
  if (values.afpAffiliation !== afpAffiliation)
    return { key: 'afpAffiliation', value: afpAffiliation };
  if (values.institutionalPatient !== institutionalPatient)
    return { key: 'institutionalPatient', value: institutionalPatient };
  if (/Si/.test(institutionalPatient) && attachNeurologicalCertificate) 
    return { key: 'institutionalPatient', value: institutionalPatient };    
  if (values.collectorRut !== collectorRut) return { key: 'collectorRut', value: collectorRut };

  return { key: 'default' };
};

const validationRules = {
  institutionalPatient: ({ filename, onErrorSnackbar, transformedValue }) => {
    if (SI.test(transformedValue)) {
      const isValidFilename = INSTITUTIONAL_PATIENT_FILENAME.test(filename);
      !isValidFilename &&
        onErrorSnackbar(`El nombre del archivo debe ser "Certificado_neurologico.pdf"`);
      return isValidFilename;
    }
    if (NO.test(transformedValue)) {
      const isValidFilename = NON_INSTITUTIONAL_PATIENT_FILENAME.test(filename);
      !isValidFilename &&
        onErrorSnackbar(`El nombre del archivo debe ser "Dictamen_de_tribunales.pdf"`);
      return isValidFilename;
    }
    return false;
  },
  afpAffiliation: ({ filename, onErrorSnackbar }) => {
    const isValidFilename = AFP_AFFILIATION.test(filename);
    !isValidFilename &&
      onErrorSnackbar(`El nombre del archivo debe ser "Certificado_de_afiliacion_AFP.pdf"`);
    return isValidFilename;
  },
  collectorRut: ({ filename, onErrorSnackbar }) => {
    const isValidFilename = COLLECTOR_RUT_CHANGE.test(filename);
    !isValidFilename &&
      onErrorSnackbar(
        `El nombre del archivo debe ser "Poder_notarial_para_cambio_de_cobrante.pdf"`
      );
    return isValidFilename;
  },
  default: () => { /*any*/ }
};

const validateFilename = (modifiedField, filename, onErrorSnackbar) => {
  const { key, value } = extractValueAndKey(modifiedField);
  return validationRules[key]({
    transformedValue: value,
    filename,
    onErrorSnackbar
  });
};

const uploadFiles = async ({ progress, onSuccessSnackbar, file, dispatch }) => {
  progress.show();
  dispatch(setPdfFile(file));
  dispatch(setWasPdfUploaded(true));
  progress.hide();
  onSuccessSnackbar(ON_SUCCESS_TEXT);
  return true;
};

const useDataUpload = ({
  progress,
  onSuccessSnackbar,
  onErrorSnackbar,
  fileUploadInput,
  modifiedField,
  dispatch
}) => {
  return async () => {
    const file = fileUploadInput.current.files[0];
    const isValidFileName = validateFilename(modifiedField, file.name, onErrorSnackbar);
    const isValidFileSize = file.size <= MAX_FILE_SIZE;
    const [fileType] = file?.name?.split('.')?.slice(-1) || [];

    if (isValidFileName && isValidFileSize) {
      return uploadFiles({
        progress,
        onSuccessSnackbar,
        onErrorSnackbar,
        file,
        dispatch
      });
    }
    if (!isValidFileSize || fileType !== 'pdf') {
      onErrorSnackbar('El archivo adjunto no es válido');
      return false;
    }
    return false;
  };
};

export default useDataUpload;
