/* eslint-disable no-console */
/* eslint-disable import/prefer-default-export */
import {
  deleteNomenclatorBank,
  loadNomenclatorBanks,
  updateNomenclatorBank,
  createNomenclatorBank
} from '../actions';

//-----------------------------------------------------------------------------------------------------------
export const processBank = (prevBank, newBank, operation) => async (
  dispatch,
  _getState,
  { api, axiosRequest }
) => {
  let createdBank = newBank;
  if (operation === 'create') {
    const {
      data: { result, error, isError }
    } = await createBank({ axiosRequest, api, bank: createdBank });
    if (isError) {
      throw new Error(error.code);
    }
    createdBank = result;

    await dispatch(createNomenclatorBank(createdBank));
  }
  if (operation === 'delete') {
    await deleteBank({ axiosRequest, api, bank: createdBank });
    await dispatch(deleteNomenclatorBank(createdBank));
  }

  if (operation === 'update') {
    const {
      data: { error, isError }
    } = await updateBank({ axiosRequest, api, bank: createdBank });
    if (isError) {
      throw new Error(error.code);
    }

    await dispatch(updateNomenclatorBank(prevBank, createdBank));
  }
};
const createBank = async ({ axiosRequest, api, bank }) =>
  axiosRequest.post(`${api}/nomenclators/bank`, { bank });

const updateBank = async ({ axiosRequest, api, bank }) =>
  axiosRequest.put(`${api}/nomenclators/bank`, { bank });

const deleteBank = async ({ axiosRequest, api, bank }) =>
  axiosRequest.delete(`${api}/nomenclators/bank/${bank.id}`);

//-----------------------------------------------------------------------------------------------------------
export const loadBanks = () => async (dispatch, _getState, { api, axiosRequest }) => {
  const {
    data: { result: banks }
  } = await axiosRequest.get(`${api}/nomenclators/bank`).catch(err => {
    console.error(err);
    return { data: { result: [] } };
  });
  await dispatch(loadNomenclatorBanks(banks));
  return banks;
};
