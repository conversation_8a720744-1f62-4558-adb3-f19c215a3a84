import { RESET_BANK_FILE, SET_BANK_FILE, SET_IS_DOWNLOADING } from './actions';

const initialState = {
  latestFile: null,
  isDownloading: false
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case RESET_BANK_FILE:
      return {
        ...initialState
      };
    case SET_BANK_FILE:
      return {
        ...state,
        latestFile: action.data,
        isDownloading: false
      };
    case SET_IS_DOWNLOADING:
      return {
        ...state,
        isDownloading: action.data
      };
    default:
      return state;
  }
}
