/* eslint-disable import/prefer-default-export */
/* eslint-disable no-bitwise */
/* eslint-disable radix */
export const resumeStats = values => {
  const aggregate = {};

  values.forEach(pension => {
    const { pensionType } = pension;
    const pensionName = `${pensionType.charAt(0).toUpperCase()}${pensionType
      .replace(/[aáàâäãå]/gi, 'a')
      .replace(/[eéèêë]/gi, 'e')
      .replace(/[iíìîï]/gi, 'i')
      .replace(/[oóòôöõ]/gi, 'o')
      .replace(/[uúùûü]/gi, 'u')      
      .toLowerCase()
      .slice(1)}`;

    if (!aggregate[pensionName]) {
      aggregate[pensionName] = 1;
    } else {
      aggregate[pensionName] += 1;
    }
  });

  const linkedPensions = values.length;
  const pensions = values.map(v => v.basePension || 0);
  const avgPension = (pensions.reduce((acc, cur) => acc + cur, 0) / linkedPensions) >> 0;
  const maxPension = Math.max(...pensions);
  return {
    pensions: Object.entries(aggregate),
    linkedPensions,
    avgPension,
    maxPension
  };
};
