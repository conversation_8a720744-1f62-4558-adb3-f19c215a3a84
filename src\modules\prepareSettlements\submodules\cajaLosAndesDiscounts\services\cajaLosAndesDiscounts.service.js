/* eslint-disable no-console */

import {
  setCurrent<PERSON>onth<PERSON>ear,
  setWasLosAndesProcessExecuted,
  setInDaysLimitRange,
  setInNumberDaysLimitRange,
  setBaseMinimumPensionExecuted
} from '../actions';

import { axiosRequest } from '../../../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

//------------------------------------------------------------------------------------------------
export const getCurrentMonthYear = () => async dispatch => {
  const { data } = await axiosRequest
    .get(`${api}/preparesettlements/caja-los-andes-discounts/current-month-year`)
    .catch(err => {
      console.error(err);
      dispatch(setCurrentMonthYear(''));
      return {};
    });
  if (!data || !data.result || !data.result.currentMonthYear) {
    dispatch(setCurrentMonthYear(''));
  } else {
    dispatch(setCurrentMonthYear(data?.result?.currentMonthYear));
  }
};
//------------------------------------------------------------------------------------------------
export const wasLosAndesExecuted = async dispatch => {
  const { data } = await axiosRequest
    .get(`${api}/verifycronexecution/BULK_LOAD_LOS_ANDES`)
    .catch(err => {
      console.error(err);
      return { data: { result: { alreadyExecuted: false } } };
    });
  dispatch(setWasLosAndesProcessExecuted(data?.result?.alreadyExecuted));
};
//------------------------------------------------------------------------------------------------
export const isInDaysLimitRange = () => async dispatch => {
  const { data } = await axiosRequest
    .get(`${api}/preparesettlements/indaylimitrange`)
    .catch(err => {
      console.error(err);
      dispatch(setInDaysLimitRange(false));
      dispatch(setInNumberDaysLimitRange(0));
      return {};
    });
  dispatch(setInDaysLimitRange(data?.result?.isInDaysLimitRange));
  dispatch(setInNumberDaysLimitRange(data?.result?.nDays));
};
//------------------------------------------------------------------------------------------------
export const baseMinimumPensionExecuted = () => async dispatch => {
  const { data } = await axiosRequest
    .get(`${api}/verifycronexecution/CRON_BASE_MINIMUN_PENSION_WORKER`)
    .catch(err => {
      console.error(err);
      dispatch(setBaseMinimumPensionExecuted(false));
      return {};
    });
  dispatch(setBaseMinimumPensionExecuted(data?.result?.alreadyExecuted));
};
//------------------------------------------------------------------------------------------------
export const insertCajaLosAndesDiscounts = async jsonData => {
  const { data } = await axiosRequest
    .post(`${api}/preparesettlements/caja-los-andes-discounts/bulk`, jsonData)
    .catch(err => {
      console.error(err);
      return { data: {}, isError: true };
    });
  if (!data) {
    return { data: {}, isError: true };
  }
  return { data };
};
