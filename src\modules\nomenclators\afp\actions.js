//------------------------------------------------------------------------------------------------
export const CREATE_NOM_AFP = 'CREATE_NOM_AFP';
export const createNomenclatorAfp = afp => {
  return {
    type: CREATE_NOM_AFP,
    data: { afp }
  };
};
//------------------------------------------------------------------------------------------------
export const DELETE_NOM_AFP = 'DELETE_NOM_AFP';
export const deleteNomenclatorAfp = afp => {
  return {
    type: DELETE_NOM_AFP,
    data: { afp }
  };
};
//------------------------------------------------------------------------------------------------
export const UPDATE_NOM_AFP = 'UPDATE_NOM_AFP';
export const updateNomenclatorAfp = (prevAfp, newAfp) => {
  return {
    type: UPDATE_NOM_AFP,
    data: { prevAfp, newAfp }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_NOM_AFP = 'SET_NOM_AFP';
export const loadNomenclatorAfp = afp => {
  return {
    type: SET_NOM_AFP,
    data: { afp }
  };
};
