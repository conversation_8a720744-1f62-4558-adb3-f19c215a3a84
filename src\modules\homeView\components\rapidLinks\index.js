import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from '@material-ui/core';
import { Link } from 'react-router-dom';
import useOnlineStatus from '@rehooks/online-status';
import useStyles from '../styles';

const RapidLinks = ({ linkList, componentTitle }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  return (
    <div className={classes.root} title="Links rápidos">
      <Typography className={classes.componentTitle}>{componentTitle}</Typography>
      {linkList.map(({ pathname, title }) => (
        <Typography key={title}>
          {pathname && onlineStatus ? (
            <Link className={classes.link} to={pathname}>
              {title}
            </Link>
          ) : (
            title
          )}
        </Typography>
      ))}
    </div>
  );
};

RapidLinks.propTypes = {
  linkList: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  componentTitle: PropTypes.string
};

RapidLinks.defaultProps = {
  componentTitle: ''
};

export default RapidLinks;
