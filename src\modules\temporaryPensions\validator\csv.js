import moment from 'moment';

const INTEGER_POSITIVE_NEGATIVE = /^-?\d+$/;
const DEFAULT_DATE = '01-01-1900';
const ONE = 1;

const validateDate = date => {
  const isValidDate = date && moment(date, 'DD-MM-YYYY').isValid();

  return isValidDate ? moment(date, 'DD-MM-YYYY') : DEFAULT_DATE;
};

const sanitizeFamilyGroup = value => {
  if (!INTEGER_POSITIVE_NEGATIVE.test(`${value}`)) return ONE;
  return Number(value) >= ONE ? Number(value) : ONE;
};

const createJSON = values => {
  const mappedKeys = [
    'beneficiaryRut',
    'basePension',
    'pensionType',
    'validityType',
    'pensionCodeId',
    'dateOfBirth',
    'gender',
    'afpAffiliation',
    'healthAffiliation',
    'collectorBranchOffice',
    'causantRut',
    'causantNames',
    'causantLastName',
    'causantMothersLastName',
    'collectorRut',
    'collectorNames',
    'collectorLastName',
    'collectorMothersLastName',
    'beneficiaryNames',
    'beneficiaryLastName',
    'beneficiaryMothersLastName',
    'disabilityDegree',
    'disabilityType',
    'resolutionNumber',
    'resolutionDate',
    'accidentDate',
    'beneficiaryEmail',
    'paymentGateway',
    'accountNumber',
    'bank',
    'collectorAddress',
    'accidentNumber',
    'disabilityStartDate',
    'institutionalPatient',
    'transient',
    'country',
    'cun',
    'initialBasePension',
    'pensionStartDate',
    'article40',
    'healthUF',
    'collectorCommune',
    'collectorCity',
    'beneficiaryPhone',
    'familyGroup',
    'increasingInLaw19578',
    'increasingInLaw19953',
    'increasingInLaw20102',
    'basePensionWithoutIncreases',
    'heavyDuty',
    'parentRut',
    'maritalStatus',
    'otherPension',
    'regimenOtherPension',
    'startAnotherPension',
    'amountOtherPension',
    'baseIncome',
    'totalPensionAccrued',
    'indemnityDiscount',
    'strennaRetroConstitution',
    'otherLink',
    'dl1026',
    'retirement',
    'totalEstimatedDaysToPay'
  ];
  
  return mappedKeys.reduce((o, k, i) => {
    let attrValue =
      typeof values[i] === 'string' ? values[i].replace(/(\r\n|\n|\r)/gm, '') : values[i];
    if (k === 'institutionalPatient') {
      attrValue = (attrValue || '').toUpperCase() === 'SI' || false;
    }
    if (k === 'familyGroup') {
      attrValue = sanitizeFamilyGroup(attrValue);
    }

    return { ...o, [k]: attrValue };
  }, {});
};

export { createJSON, validateDate };
