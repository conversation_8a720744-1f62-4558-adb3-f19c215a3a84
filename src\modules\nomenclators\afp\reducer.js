import { CREATE_NOM_AFP, DELETE_NOM_AFP, SET_NOM_AFP, UPDATE_NOM_AFP } from './actions';

const initialState = {
  data: [],
  errors: []
}; // attr data: array of afps

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case CREATE_NOM_AFP: {
      const { afp } = action.data;
      return {
        ...state,
        data: [...state.data, afp]
      };
    }
    case UPDATE_NOM_AFP: {
      const data = [...state.data];
      data[data.indexOf(action.data.prevAfp)] = action.data.newAfp;
      return { ...state, data };
    }
    case DELETE_NOM_AFP: {
      const data = [...state.data];
      data.splice(data.indexOf(action.data.afp), 1);
      return { ...state, data };
    }
    case SET_NOM_AFP: {
      const data = [...action.data.afp];
      return { ...state, data };
    }
    default:
      return state;
  }
}
