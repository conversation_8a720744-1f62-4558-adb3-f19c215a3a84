import { SET_NOM_CAJA, UPDATE_NOM_CAJA } from './actions';

const initialState = {
  data: [],
  errors: []
}; // attr data: array of afps

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case UPDATE_NOM_CAJA: {
      const data = [...state.data];
      data[data.indexOf(action.data.prevCaja)] = action.data.newCaja;
      return { ...state, data };
    }
    case SET_NOM_CAJA: {
      const data = [...action.data.caja];
      return { ...state, data };
    }
    default:
      return state;
  }
}
