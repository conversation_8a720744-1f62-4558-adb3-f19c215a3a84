/* eslint-disable no-console */
/* eslint-disable import/prefer-default-export */
import {
  deleteNomenclatorAfp,
  updateNomenclatorAfp,
  loadNomenclatorAfp,
  createNomenclatorAfp
} from '../actions';

//-----------------------------------------------------------------------------------------------------------
export const processAfp = (prevAfp, newAfp, operation) => async (
  dispatch,
  _getState,
  { api, axiosRequest }
) => {
  let createdAfp = newAfp;
  if (operation === 'create') {
    const {
      data: { result, error, isError }
    } = await createAfp({ axiosRequest, api, afp: createdAfp });
    if (isError) {
      throw new Error(error.code);
    }
    createdAfp = result;

    await dispatch(createNomenclatorAfp(createdAfp));
  }
  if (operation === 'delete') {
    await deleteAfp({ axiosRequest, api, afp: createdAfp });
    await dispatch(deleteNomenclatorAfp(createdAfp));
  }
  if (operation === 'update') {
    const {
      data: { error, isError }
    } = await updateAfp({ axiosRequest, api, afp: createdAfp });
    if (isError) {
      throw new Error(error.code);
    }

    await dispatch(updateNomenclatorAfp(prevAfp, createdAfp));
  }
};
const createAfp = async ({ axiosRequest, api, afp }) =>
  axiosRequest.post(`${api}/nomenclators/afp`, { afp });

const updateAfp = async ({ axiosRequest, api, afp }) =>
  axiosRequest.put(`${api}/nomenclators/afp`, { afp });

const deleteAfp = async ({ axiosRequest, api, afp }) =>
  axiosRequest.delete(`${api}/nomenclators/afp/${afp.id}`);

//-----------------------------------------------------------------------------------------------------------
export const loadAfp = () => async (dispatch, _getState, { api, axiosRequest }) => {
  const {
    data: { result: afps }
  } = await axiosRequest.get(`${api}/nomenclators/afp`).catch(err => {
    console.error(err);
    return { data: { result: [] } };
  });
  await dispatch(loadNomenclatorAfp(afps));
  return afps;
};
