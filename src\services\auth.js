/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
import { status } from '../utils/http';
import errorBuilder from '../utils/errorBuilder';
import { AuthenticationService } from './security/services';
import { axiosRequest } from './axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

const authService = {
  async login() {
    try {
      let errorResponse = {};
      const clearSession = async () => {
        await AuthenticationService.logout();
        await AuthenticationService.clearCache();
        localStorage.clear();
        return { error: { message: 'Sesión expirada. Cerrando la sesión...' } };
      };

      const accountInfo = await AuthenticationService.login();
      const idToken = localStorage.getItem(`msal.${process.env.REACT_APP_AAD_CLIENT_ID}.idtoken`);

      if (!accountInfo || !idToken) await clearSession();

      const rawData = await axiosRequest
        .get(`${api}/auth/login`, { headers: { Authorization: `Bearer ${idToken}` } })
        .catch(async error => {
          if (error?.response) {
            if (error.response.status === status.UNAUTHORIZED) await clearSession();
            errorResponse = errorBuilder.build(error.response.status, error.response.data);
          }
        });

      if (!rawData || !rawData.data || rawData.status !== status.OK) {
        return errorResponse;
      }

      return rawData.data;
    } catch (error) {
      console.log(error);
      return null;
    }
  },
  async logout() {
    const rawData = await axiosRequest.get(`${api}/auth/logout`).catch(error => {
      return false;
    });

    if (!rawData || !rawData.data || rawData.status !== status.OK) {
      return false;
    }

    await AuthenticationService.logout();
    await AuthenticationService.clearCache();
    localStorage.clear();

    return true;
  }
};

export default authService;
