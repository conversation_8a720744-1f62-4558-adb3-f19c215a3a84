/* eslint-disable no-param-reassign */
/* eslint-disable import/no-unresolved */
/* eslint-disable react/prop-types */
/* eslint-disable no-underscore-dangle */
import React, { useState, useEffect } from 'react';
import moment from 'moment';
import { Grid, Typography, Button, Tooltip } from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import useOnlineStatus from '@rehooks/online-status';
import CloudDownloadIcon from '@material-ui/icons/CloudDownload';

import { useProgress } from 'components';
import useStyles from './styles';
import { datePickerStyles } from '../picker/pickerStyles';
import Picker from '../picker/picker';
import DownloadForm from './downloadForm/downloadForm';
import DownloadSettlementsPDF from './settlementPDF/download';
import DownloadPensionCertificatePDF from './pensionCertificate/download';

import usePdfDownload from '../../hooks/usePdfDownload';
import handleSettlementPDFGeneration from '../../hooks/usePDFSettlementDownload';
import handlePensionCertificatePDFGeneration from '../../hooks/usePensionCertificateDownload';

import { checkPensionerDocuments, checkCronExecution } from '../../services/queryPensions.service';

const title = 'Descargar liquidación';
const minYear = 1950;
const minDate = new Date(minYear, 0, 1);
const MonthsInAYear = 12;
const alertText = 'Ingrese rango de fechas para descargar histórico de liquidaciones en PDF';
const CronMark = 'INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNEMENT_PROCESS';
const NOT_VALID_PENSION = /no vigente/i;

const endingDateLowerThanStartingDate = 'Fecha de término debe ser igual o mayor a Fecha de inicio';
const invalidDateError = 'Fecha seleccionada no es válida';
const startingDateOutOfRangeDueToEndingDate =
  'La fecha de inicio puede ser hasta 12 meses inferior a la de término';
const outOfRangeError = 'La fecha ingresada esta fuera del rango válido';

const startingLabel = 'Fecha de inicio';
const endingLabel = 'Fecha de término';

const nameAndRegex = {
  court: {
    name: 'Dictamen_de_tribunales',
    regex: 'tribunales',
    existence: false
  },
  neurological: {
    name: 'Certificado_neurologico',
    regex: 'neurologico',
    existence: false
  },
  changeCollector: {
    name: 'Poder_notarial_para_cambio_de_cobrante',
    regex: 'notarial',
    existence: false
  },
  AFPaffiliation: {
    name: 'Certificado_de_afiliacion_AFP',
    regex: 'AFP',
    existence: false
  }
};

const startOfMonthDate = date =>
  moment(date)
    .startOf('month')
    .toDate();

const isPensionCertificateEnabled = (isFamilyAssignmentCronExecuted, pensionType) =>
  isFamilyAssignmentCronExecuted && !NOT_VALID_PENSION.test(`${pensionType}`);

const PensionerDocuments = ({ data, onErrorSnackbar }) => {
  const { beneficiaryRut, causantRut, pensionCodeId, currentDate } = data;

  const classes = useStyles();
  const onlineStatus = useOnlineStatus();

  const [availableFiles, setAvailableFiles] = useState(nameAndRegex);
  const [isCurrentlyDownloading, setIsCurrentlyDownloading] = useState(false);
  const [settlementData, setSettlementData] = useState(null);
  const [pensionCertificateData, setPensionCertificateData] = useState(null);

  const [startingDate, setStartingDate] = useState(startOfMonthDate(currentDate));
  const [endingDate, setEndingDate] = useState(startOfMonthDate(currentDate));
  const [startingDateErrors, setStartingDateErrors] = useState({});
  const [endingDateErrors, setEndingDateErrors] = useState({});
  const [maxSettlementDate, setMaxSettlementDate] = useState(startOfMonthDate(currentDate));
  const [wasFamilyAssignmentCronExecuted, setWasFamilyAssignmentCronExecuted] = useState(false);

  const disableDownload = () => !onlineStatus || isCurrentlyDownloading;
  const isStartingDateDiffLessThan12Months = (lower, upper) => {
    const monthsDiff = moment(upper).diff(moment(lower), 'months');
    return monthsDiff <= MonthsInAYear;
  };
  const isDateBetweenItsLimit = (date, lowerLimit, upperLimit) =>
    moment(date).isBetween(lowerLimit, upperLimit, 'month', []);
  const isLowerDateOverUpperDate = (lower, upper) =>
    moment(lower).diff(moment(upper), 'months') > 0;
  const isError = (errorObject = {}) => Object.keys(errorObject).some(key => errorObject[key]);
  const onClickCertificateQuote = () =>
    window.location.replace('https://www.previred.com/web/previred/');

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  useEffect(() => {
    progress.hide();
    if (currentDate) {
      setStartingDate(startOfMonthDate(currentDate));
      setEndingDate(startOfMonthDate(currentDate));
    }
    checkPensionerDocuments({ beneficiaryRut, causantRut, pensionCodeId, nameAndRegex }, setAvailableFiles);
    setMaxSettlementDate(startOfMonthDate(currentDate));
    checkCronExecution(CronMark)(setWasFamilyAssignmentCronExecuted);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const findErrorsStartingDate = () => {
    if (!isDateBetweenItsLimit(startingDate, minDate, maxSettlementDate))
      return setStartingDateErrors({ ...endingDateErrors, outOfLimits: true });
    if (!isStartingDateDiffLessThan12Months(startingDate, endingDate))
      return setStartingDateErrors({ ...startingDateErrors, outOfRangeDueEndingDate: true });
    return setStartingDateErrors({});
  };

  const findErrorsEndingDate = () => {
    if (!isDateBetweenItsLimit(endingDate, minDate, maxSettlementDate))
      return setEndingDateErrors({ ...endingDateErrors, outOfLimits: true });
    if (isLowerDateOverUpperDate(startingDate, endingDate))
      return setEndingDateErrors({ ...endingDateErrors, endingLowerThanStarting: true });
    return setEndingDateErrors({});
  };

  useEffect(() => {
    if (currentDate && startingDate && endingDate) {
      findErrorsEndingDate();
      findErrorsStartingDate();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startingDate, endingDate]);

  const errorMessageDate = (dateErrors = {}) => {
    if (dateErrors?.invalidDate) return invalidDateError;
    if (dateErrors?.outOfLimits) return outOfRangeError;
    if (dateErrors?.outOfRangeDueEndingDate) return startingDateOutOfRangeDueToEndingDate;
    if (dateErrors?.endingLowerThanStarting) return endingDateLowerThanStartingDate;

    return '';
  };

  const handleStartingDateChange = date => {
    const { _d: selectedDate, _isValid: isValidDate } = date || {};
    if (isValidDate && selectedDate) {
      setStartingDateErrors({});
      return setStartingDate(startOfMonthDate(selectedDate));
    }
    return setStartingDateErrors({ ...startingDateErrors, invalidDate: true });
  };

  const handleEndingDateChange = date => {
    const { _d: selectedDate, _isValid: isValidDate } = date || {};
    if (isValidDate && selectedDate) {
      setEndingDateErrors({});
      return setEndingDate(startOfMonthDate(selectedDate));
    }
    return setEndingDateErrors({ ...startingDateErrors, invalidDate: true });
  };

  return (
    <>
      <Grid className={classes.grid}>
        <Typography className={classes.title}>{title}</Typography>
      </Grid>
      <Grid container className={classes.grid} spacing={3}>
        <Grid item>
          <Picker
            className={classes}
            disabled={!onlineStatus}
            label={startingLabel}
            size="small"
            format="MM-YYYY"
            rightArrowIcon
            error={isError(startingDateErrors)}
            helperText={errorMessageDate(startingDateErrors)}
            allowKeyboardControl={false}
            value={startingDate}
            minDate={minDate}
            maxDate={maxSettlementDate}
            onChange={handleStartingDateChange}
            MuiStyle={datePickerStyles}
          />
        </Grid>

        <Grid item>
          <Picker
            className={classes}
            disabled={!onlineStatus}
            label={endingLabel}
            size="small"
            format="MM-YYYY"
            value={endingDate}
            rightArrowIcon
            error={isError(endingDateErrors)}
            helperText={errorMessageDate(endingDateErrors)}
            minDate={minDate}
            maxDate={maxSettlementDate}
            onChange={handleEndingDateChange}
            MuiStyle={datePickerStyles}
          />
        </Grid>

        <Grid item>
          <Tooltip title="Descargar">
            <span className={classes.spanFinalize}>            
              <Button
                variant="contained"
                size="medium"
                color="primary"
                disabled={
                  disableDownload() || isError(startingDateErrors) || isError(endingDateErrors)
                }
                onClick={handleSettlementPDFGeneration({
                  beneficiaryRut,
                  causantRut,
                  pensionCodeId,
                  lowerDate: startingDate,
                  upperDate: endingDate,
                  setIsCurrentlyDownloading,
                  progress,
                  onErrorSnackbar,
                  setSettlementData,
                  pensionerData: data
                })}
              >  
              <CloudDownloadIcon />
              </Button>                            
            </span>            
          </Tooltip>
        </Grid>

        <DownloadSettlementsPDF
          documentName={data?.pensionCodeId}
          progress={progress}
          setIsCurrentlyDownloading={setIsCurrentlyDownloading}
          settlementData={settlementData}
          setSettlementData={setSettlementData}
        />
      </Grid>
      <Grid className={classes.grid}>
        <Alert className={classes.alertBar} severity="error" color="info">
          {alertText}
        </Alert>
      </Grid>
      {/* ---------------------------------------- */}
      <Grid container className={classes.grid} spacing={5}>
        <Grid item>
          <DownloadForm
            label="Descargar"
            classes={classes}
            value="Poder notarial de cambio de cobrante"
            tooltipTitle="Descargar"
            disabled={!availableFiles.changeCollector.existence || disableDownload()}
            onClick={usePdfDownload(
              { beneficiaryRut, causantRut, pensionCodeId },
              setIsCurrentlyDownloading,
              progress,
              nameAndRegex.changeCollector.name
            )}
          />
        </Grid>
        <Grid item>
          <DownloadForm
            label="Descargar"
            classes={classes}
            value="Certificado de afiliación AFP"
            tooltipTitle="Descargar"
            disabled={!availableFiles.AFPaffiliation.existence || disableDownload()}
            onClick={usePdfDownload(
              { beneficiaryRut, causantRut, pensionCodeId },
              setIsCurrentlyDownloading,
              progress,
              nameAndRegex.AFPaffiliation.name
            )}
          />
        </Grid>
      </Grid>
      {/* ---------------------------------------- */}
      <Grid container className={classes.grid} spacing={5}>
        <Grid item>
          <DownloadForm
            label="Descargar"
            classes={classes}
            value="Certificados de neurólogos"
            tooltipTitle="Descargar"
            disabled={!availableFiles.neurological.existence || disableDownload()}
            onClick={usePdfDownload(
              { beneficiaryRut, causantRut, pensionCodeId },
              setIsCurrentlyDownloading,
              progress,
              nameAndRegex.neurological.name
            )}
          />
        </Grid>
        <Grid item>
          <DownloadForm
            label="Descargar"
            classes={classes}
            value="Certificados de tribunales"
            tooltipTitle="Descargar"
            disabled={!availableFiles.court.existence || disableDownload()}
            onClick={usePdfDownload(
              { beneficiaryRut, causantRut, pensionCodeId },
              setIsCurrentlyDownloading,
              progress,
              nameAndRegex.court.name
            )}
          />
        </Grid>
      </Grid>
      {/* ---------------------------------------- */}
      <Grid container className={classes.grid} spacing={5}>
        <Grid item>
          <DownloadForm
            label="Descargar"
            classes={classes}
            value="Certificado de pensión"
            disabled={
              disableDownload() ||
              !isPensionCertificateEnabled(wasFamilyAssignmentCronExecuted, data?.validityType)
            }
            tooltipTitle="Descargar"
            onClick={handlePensionCertificatePDFGeneration({
              beneficiaryRut,
              causantRut,
              pensionCodeId,
              setIsCurrentlyDownloading,
              progress,
              onErrorSnackbar,
              setPensionCertificateData,
              pensionerData: data
            })}
          />
          <DownloadPensionCertificatePDF
            documentName={data?.pensionCodeId}
            progress={progress}
            setIsCurrentlyDownloading={setIsCurrentlyDownloading}
            pensionCertificateData={pensionCertificateData}
            setPensionCertificateData={setPensionCertificateData}
          />
        </Grid>
        <Grid item>
          <DownloadForm
            label="Descargar"
            classes={classes}
            disabled={disableDownload()}
            value="Certificado de pago de cotizaciones"
            tooltipTitle="Descargar"
            onClick={onClickCertificateQuote}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default PensionerDocuments;
