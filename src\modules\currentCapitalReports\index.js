/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React from 'react';
import PropTypes from 'prop-types';
import { renderRoutes } from 'react-router-config';
import SnackbarManager from '../../components/SnackbarManager';
import { Page } from '../../components';
import { useSnackbar } from './hooks/useSnackbar';
import useStyles from './styles';
import { SummarizeSettlements } from './summarizeSettlement/views/SummarizeSettlements';
import { PensionsAccounting } from './pensionsAccounting/views/PensionsAccounting';
import InactivationReactivation from './views/handler';
import CurrentCapital from './views/currentCapital';

const ReportsPage = ({ route }) => {
  const classes = useStyles();
  const {
    text,
    setText,
    error,
    setError,
    successSnackbar,
    errorSnackbar,
    setSuccessSnackbar,
    setErrorSnackbar
  } = useSnackbar();

  const onSuccessSnackbar = val => {
    setText(val);
    setSuccessSnackbar(true);
    setErrorSnackbar(false);
  };

  const onErrorSnackbar = err => {
    setError(err);
    setSuccessSnackbar(false);
    setErrorSnackbar(true);
  };

  return (
    <Page className={classes.root} title="Reportería">
      {renderRoutes(route.routes, { onSuccessSnackbar, onErrorSnackbar })}
      <SnackbarManager
        handleRoute={false}
        error={error}
        text={text}
        successSnackbar={successSnackbar}
        errorSnackbar={errorSnackbar}
        setErrorSnackbar={setErrorSnackbar}
        setSuccessSnackbar={setSuccessSnackbar}
      />
    </Page>
  );
};

ReportsPage.propTypes = {
  route: PropTypes.shape({
    path: PropTypes.string,
    routes: PropTypes.array,
    component: PropTypes.func
  }).isRequired
};

export {
  ReportsPage,
  InactivationReactivation,
  SummarizeSettlements,
  CurrentCapital,
  PensionsAccounting
};
