/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React, { useEffect, Fragment } from 'react';
import useOnlineStatus from '@rehooks/online-status';
import ImportButton from 'components/ImportButton';
import { Grid, Fade, Button, Tooltip } from '@material-ui/core';
import MuiAlert from '@material-ui/lab/Alert';
import useRouter from 'utils/useRouter';
import { useSelector, useDispatch } from 'react-redux';

import { useProgress } from 'components';
import useStyles from '../styles';
import useDataUpload from '../../hooks/useDataUpload';
import {
  inactivateOrphanPension,
  wasInactivatedThisMonth,
  checkCronExecution,
  checkInactReactAvailabilityProcess
} from '../../services/regularized.service';
import {
  cleanFileError,
  cleanFamilyAssignmentResumeErrors,
  setWasReactivationCronExecuted,
  setWasInacReactAlreadyExecuted
} from '../../actions';
import { checkWritePermission } from 'utils/checkUserPermission';

const cronDependency = 'REACTIVATE_TRANSIENT_PRE_WORKER';
const cronMark = 'INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNEMENT_PROCESS';

const Alert = props => <MuiAlert elevation={6} variant="filled" {...props} />;

const renderAlert = (inDayRange, availableDay, display) => {
  if (inDayRange === undefined) return null;
  const allowMesagge = `Hasta el día hábil número ${availableDay} del mes actual, puede realizar la inactivación y reactivación de pensiones`;
  const denyMessage = `No se puede realizar la inactivación y reactivación, han pasado los primeros ${availableDay} días hábiles del mes permitidos`;

  const variant = inDayRange ? 'filled' : 'outlined';
  const severity = inDayRange ? 'info' : 'error';
  const message = inDayRange ? allowMesagge : denyMessage;

  return (
    display && (
      <div>
        <Fade in timeout={{ enter: 1000 }}>
          <Alert severity={severity} variant={variant}>
            {message}
          </Alert>
        </Fade>
      </div>
    )
  );
};

const FamilyAssigment = ({ onSuccessImport, onErrorImport, role }) => {
  const fileUploadInput = React.createRef();
  const router = useRouter();
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();
  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const hasWritePermission = checkWritePermission(role);

  const fileErrors = useSelector(store => store.regularizedPensions.fileErrors);
  const isReactivateEnable = useSelector(store => store.regularizedPensions.isReactivateEnable);
  const isEnableReactivateImport = useSelector(
    store => store.regularizedPensions.isEnableReactivateImport
  );
  const wasReactivationCronExecuted = useSelector(
    store => store.regularizedPensions.wasReactivationCronExecuted
  );
  const isReactInactAvailable = useSelector(
    store => store.regularizedPensions.isReactInactAvailable
  );
  const daysToExecuteInactReact = useSelector(
    store => store.regularizedPensions.daysToExecuteInactReact
  );
  const wasInacReactAlreadyExecuted = useSelector(
    store => store.regularizedPensions.wasInacReactAlreadyExecuted
  );
  const apiCall = useSelector(store => store.regularizedPensions.apiCall);

  const handleUploadButton = () => {
    fileUploadInput.current.value = '';
    fileUploadInput.current.click();
  };

  const verifyInactReactConditions = () => 
    wasInacReactAlreadyExecuted ||
    !wasReactivationCronExecuted ||
    !isReactInactAvailable ||
    isReactivateEnable ||
    fileErrors.length > 0 ||
    !onlineStatus;

  const getInactReactTooltipText = () => {
    const allowMesagge = 'Inactivar y reactivar pensionados';
    const alreadyDoneMesage =
      'La Inactivación/Reactivación de pensiones del mes actual ya fue realizada';
    const wasNotPerformed =
      'No se realizó la Inactivación/Reactivación de pensiones del mes actual';

    let text = allowMesagge;
    if (wasInacReactAlreadyExecuted) text = alreadyDoneMesage;
    if (!wasInacReactAlreadyExecuted && !isReactInactAvailable) text = wasNotPerformed;
    return text;
  };

  const handleInactivateOrphanPension = () =>
    dispatch(inactivateOrphanPension(onSuccessImport, onErrorImport));

  const handleFileUpload = useDataUpload(
    dispatch,
    router,
    progress,
    onSuccessImport,
    onErrorImport
  );

  useEffect(() => {
    dispatch(cleanFileError());
    dispatch(cleanFamilyAssignmentResumeErrors());
    dispatch(wasInactivatedThisMonth());
    const checkCronExec = checkCronExecution(dispatch);
    checkCronExec(cronDependency, setWasReactivationCronExecuted);
    checkCronExec(cronMark, setWasInacReactAlreadyExecuted);
    checkInactReactAvailabilityProcess(dispatch);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Fragment>
      {renderAlert(isReactInactAvailable, daysToExecuteInactReact, apiCall)}
      <Grid
        className={classes.formControl}
        container
        direction="row"
        justify="space-around"
        alignItems="center"
      >
        <Grid item>
          <ImportButton
            classes={{ formControl: classes.formControl }}
            errorMessage={fileErrors}
            onClick={handleUploadButton}
            disabled={
              !hasWritePermission ||
              !isReactInactAvailable ||
              wasInacReactAlreadyExecuted ||
              isEnableReactivateImport ||
              !onlineStatus
            }
          />
        </Grid>
        <Grid item>
          <Tooltip title={getInactReactTooltipText()} aria-label="inactReactTooltip">
            <span>
              <Button
                variant="contained"
                disabled={!hasWritePermission || verifyInactReactConditions()}
                onClick={handleInactivateOrphanPension}
              >
                Inactivar/Reactivar
              </Button>
            </span>
          </Tooltip>
        </Grid>
      </Grid>

      <input
        ref={fileUploadInput}
        type="file"
        style={{ display: 'none' }}
        id="fileUpload"
        disabled={!onlineStatus}
        onChange={v => v.target.files.length && handleFileUpload(v.target.files[0])}
        accept=".csv"
      />
    </Fragment>
  );
};

export default FamilyAssigment;
