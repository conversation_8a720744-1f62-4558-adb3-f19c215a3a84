import React from 'react';

import clsx from 'clsx';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import { AppBar } from '@material-ui/core';

const useStyles = makeStyles(() => ({
  root: {
    boxShadow: 'none'
  }
}));

const Topbar = props => {
  const { className, ...rest } = props;

  const classes = useStyles();

  return <AppBar {...rest} className={clsx(classes.root, className)} color="primary"></AppBar>;
};

Topbar.propTypes = {
  className: PropTypes.string
};

export default Topbar;
