/* eslint-disable import/no-unresolved */
import { getEncoding } from '../../../../../utils/encodingFile';

const NOT_VALID_TXT_CHARS = /[^\s\n+\-*/=@#&%$!¡¿?ºª.:;,_|><´`¨""{})'^[\]~áéíóúàèìòùãẽĩõñũỹg̃äöüëïâêîôûçğşa-z0-9�]/im;

const readFile = async file => {
  const encoding = await getEncoding(file);
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const { result } = reader;
      if (NOT_VALID_TXT_CHARS.test(result)) {
        reject(new Error('Error al leer el archivo.'));
      }

      const splittedResult = result.split('\n');
      const rows = splittedResult.filter(row => row && row.length);
      if (!rows || !rows.length) {
        reject(new Error('Archivo vacio.'));
      }

      resolve(jsonMapper(rows));
    };
    reader.onerror = () => {
      return reject(new Error('Error al leer el archivo.'));
    };
    reader.readAsText(file, encoding);
  });
};
const jsonMapper = rows => {
  const validRows = filterRows(rows);
  return validRows.map(row => ({
    rut: formatRut(getRut(row), getDV(row)),
    discount: +formatMoney(row)
  }));
};
const filterRows = rows => rows.filter(row => row.length && row.replace(/\D/, ''));
const formatMoney = row => {
  return row.substring(positionOnFile.discount.initPosition, positionOnFile.discount.endPosition);
};
const getRut = row =>
  row.substring(positionOnFile.rut.initPosition, positionOnFile.rut.endPosition).replace(/^0/, '');
const getDV = row => row.substring(positionOnFile.dv.initPosition, positionOnFile.dv.endPosition);
const formatRut = (rut, dv) => `${rut}-${dv}`;

const positionOnFile = {
  rut: {
    initPosition: 0,
    endPosition: 8
  },
  dv: {
    initPosition: 8,
    endPosition: 9
  },
  discount: {
    initPosition: 49,
    endPosition: 56
  }
};

export default readFile;
