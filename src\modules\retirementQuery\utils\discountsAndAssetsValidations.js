import moment from 'moment';

const formatter = new Intl.NumberFormat('es-CL', {
  style: 'currency',
  currency: 'CLP',
  maximumFractionDigits: 0
});

export const formatCurrency = value => {
  return formatter.format(
    String(value)
      .replace(/[^\d]/g, '')
      .slice(0, 6)
  );
};

export const normalizeValue = value =>
  String(value)
    .replace(/[^\d]/g, '')
    .slice(0, 6);

export const validateDatePicker = ({
  field,
  oppositeField,
  value,
  DATE_FORMAT,
  setDateRangeError,
  setStartDateError,
  state,
  setState,
  currentDate
}) => {
  const methods = { endDate: 'isSameOrAfter', startDate: 'isSameOrBefore' };
  const startDateIsValid = moment(value, DATE_FORMAT).isSameOrAfter(
    moment(currentDate, DATE_FORMAT)
  );
  const rangeIsValid = moment(value, DATE_FORMAT)[methods[field]](
    moment(state[oppositeField], DATE_FORMAT)
  );
  setDateRangeError(!rangeIsValid);
  field === 'startDate' && setStartDateError(!startDateIsValid);
  setState({ ...state, [field]: moment(value, DATE_FORMAT).format(DATE_FORMAT) });
};
