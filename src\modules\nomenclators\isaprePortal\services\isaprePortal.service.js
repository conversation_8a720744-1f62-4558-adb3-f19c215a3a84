/* eslint-disable no-console */
import {
  numberOfDaysToExecute,
  isIsaprePortalAvailable,
  apiCalled,
  enableIsaprePortal,
  enableIsaprePortalImport
} from '../actions';
import { status } from '../../../../utils/http';
import { axiosRequest } from '../../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

//------------------------------------------------------------------------------------------------
export const insertTemporaryIsaprePortal = async jsonData => {
  const { data, error } = await axiosRequest
    .post(`${api}/nomenclators/isapres/temporaryIsaprePortal/bulk`, jsonData)
    .catch(err => {
      console.error(err);
      return { data: {}, isError: true, error: err };
    });

  const errors = error?.response?.data?.errors;

  if (errors?.length) return { error: errors[0] };
  return data;
};

export const wasExecutedThisMonth = () => async (dispatch, _getState) => {
  const { data } = await axiosRequest
    .get(`${api}/nomenclators/isapres/temporaryIsaprePortal/was-executed`)

    .catch(err => {
      console.error(err);
      return { data: {}, isError: true };
    });

  dispatch(enableIsaprePortalImport(data.wasLoadData, data.completed));
  dispatch(enableIsaprePortal(data.wasLoadData));
};

//------------------------------------------------------------------------------------------------
export const checkIsaprePortalAvailabilityProcess = async dispatch => {
  const {
    data: { nDays, processAvailable }
  } = await axiosRequest
    .get(`${api}/getBusinessDays/isapre-portal-process`)

    .catch(err => {
      console.error(err);
      return { data: { nDays: '', processAvailable: false } };
    });

  dispatch(isIsaprePortalAvailable(processAvailable));
  dispatch(numberOfDaysToExecute(nDays));
  dispatch(apiCalled(true));
};

export const updateIsaprePensions = (onSuccessImport, onErrorImport) => async (
  dispatch,
  _getState
) => {
  const { data, isError = false } = await axiosRequest
    .post(`${api}/nomenclators/isapres/temporaryIsaprePortal/bulkUpdate`)
    .catch(err => {
      if (err.response && err.response.status === status.NOTFOUND && err.response.data) {
        const { message } = err.response.data.error;
        return {
          data: {
            error: message,
            message,
            executionCompleted: false
          },
          isError: true
        };
      }

      return {
        data: {
          error: 'Error en proceso de actualización de Afiliación de Salud',
          message: 'proceso de actualización de Afiliación de Salud fallido',
          executionCompleted: false
        },
        isError: true
      };
    });

  const { executionCompleted, error } = data;

  if (executionCompleted) {
    onSuccessImport('Proceso de actualización de Afiliación de Salud exitoso');
    dispatch(enableIsaprePortal(false));
  }
  if (isError) {
    onErrorImport(error);
  }
};
