import React from 'react';
import { Grid } from '@material-ui/core';
import { Header } from './components';
import CurrentCapitalDocuments from './components/CurrentCapitalDocuments';

// eslint-disable-next-line react/prop-types
const CurrentCapital = ({ onErrorSnackbar }) => {
  return (
    <>
      <Grid>
        <Header subtitle="Configuraciones / Reportería" title=" Reportes Capitales vigentes" />
      </Grid>
      <Grid>
        <CurrentCapitalDocuments onErrorSnackbar={onErrorSnackbar} />
      </Grid>
    </>
  );
};

export default CurrentCapital;
