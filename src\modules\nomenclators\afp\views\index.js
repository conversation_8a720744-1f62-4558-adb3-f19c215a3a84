/* eslint-disable no-console */
/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import { Grid } from '@material-ui/core';
import { HeaderContent, Page } from 'components';
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { deleteNomenclatorAfp } from '../actions';
import { loadAfp, processAfp } from '../services';
import { AfpTable } from './components';
import useStyles from './styles';

const AfpNomenclatorPage = props => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const [isLoading, setLoading] = useState(true);
  const [enabledButton, setEnabledButton] = useState(false);
  const state = {
    data: useSelector(store => store.afp.data)
  };
  const { onSuccessSnackbar, onErrorSnackbar, role } = props;

  const CONFLICT = 409;
  const handleConflict = oldData =>
    (!oldData.code || !oldData.rut) && dispatch(deleteNomenclatorAfp(oldData));

  const keyValue = { code: 'Código', rut: 'Rut' };
  const handleTranslate = key =>
    keyValue[key]
      ? `El ${keyValue[key]} que intenta guardar ya existe`
      : `El registro que intenta guardar ya existe`;
  const handleLoad = () => {
    return !isLoading
      ? state.data
      : async () => {
        return dispatch(loadAfp()).then(afps => {
          setLoading(false);
          return {
            data: afps,
            totalCount: afps.length
          };
        });
      };
  };
  const handleProcess = (okMessage, erroMessage, operation) => async (newData, oldData) => {
    try {
      if (operation === 'create' || (operation === 'update' && oldData) || operation === 'delete') {
        const result = await dispatch(processAfp(oldData, newData, operation))
          .then(() => {
            onSuccessSnackbar(okMessage);
            return true;
          })
          .catch(error => {
            let messageError;
            if (error.response.status === CONFLICT) {
              const key = Object.keys(error.response.data.error.keyValue);
              if (operation === 'update') handleConflict(oldData);
              messageError = handleTranslate(key);
              onErrorSnackbar(messageError);
              setEnabledButton(true);
            } else {
              messageError = erroMessage;
              onErrorSnackbar(messageError);
              setEnabledButton(true);
            }
            throw new Error(messageError);
          });
        if (!result) return false;
      }
      return true;
    } catch ({ message }) {
      onErrorSnackbar(message);
      setEnabledButton(true);
      throw new Error(message);
    }
  };

  return (
    <Page className={classes.root} title="AFP">
      <Grid container justify="space-between" alignItems="center" className={classes.headerContent}>
        <HeaderContent overline="Configuraciones / Mantenedores" title="AFP" />
      </Grid>
      <AfpTable
        userRole={role}
        enabledButton={enabledButton}
        data={handleLoad()}
        onCreate={handleProcess(
          'Los valores se guardaron correctamente',
          'Error en actualización de registro',
          'create'
        )}
        onUpdate={handleProcess(
          'Los valores se guardaron correctamente',
          'Error en actualización de registro',
          'update'
        )}
        onDelete={handleProcess('Registro eliminado', 'Error en eliminación de registro', 'delete')}
      />
    </Page>
  );
};

AfpNomenclatorPage.propTypes = {
  onSuccessSnackbar: PropTypes.func.isRequired,
  onErrorSnackbar: PropTypes.func.isRequired
};

// eslint-disable-next-line import/prefer-default-export
export { AfpNomenclatorPage };
