import { downloadBankFile } from '../services/bankFileServices';
import { setIsDownloading } from '../actions';

const ON_ERROR_TEXT = 'Hubo un error con la descarga del archivo';
const downloadFile = async ({ dispatch, latestFile, progress, onErrorSnackbar }) => {
  let success;
  const { virtualPath, uuid } = latestFile;

  progress.show();
  dispatch(setIsDownloading(true));

  await downloadBankFile(uuid).then(({ isError, data }) => {
    success = !isError;
    if (success) {
      const fileName = virtualPath?.split('/').pop();
      const url = window.URL.createObjectURL(data);
      const a = document.createElement('a');
      a.setAttribute('download', fileName);
      a.href = url;
      a.click();
    }
  });
  if (!success) {
    dispatch(setIsDownloading(false));
    onErrorSnackbar(`${ON_ERROR_TEXT}`);
    progress.hide();
    return false;
  }

  dispatch(setIsDownloading(false));
  progress.hide();
  return true;
};

const UseDataDownload = ({ dispatch, latestFile, progress, onErrorSnackbar }) => {
  return () => downloadFile({ dispatch, latestFile, progress, onErrorSnackbar });
};

export default UseDataDownload;
