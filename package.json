{"name": "template", "version": "0.1.0", "private": true, "author": "23people", "email": "<EMAIL>", "licence": "UNLICENSED", "scripts": {"build:dev": "env-cmd --no-override -f .env.development npm run build", "build:qa": "env-cmd --no-override -f .env.qa npm run build", "build:qa1": "env-cmd --no-override -f .env.qa1 npm run build", "build:qa2": "env-cmd --no-override -f .env.qa2 npm run build", "build:master": "npm run build", "start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:cov": "jest test --watchAll=false", "eject": "react-scripts eject"}, "jest": {"moduleDirectories": ["node_modules", "src"], "moduleNameMapper": {"\\.(css|scss|ico|svg)$": "identity-obj-proxy"}, "testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "testResultsProcessor": "jest-sonar-reporter", "collectCoverage": true, "coveragePathIgnorePatterns": ["/node_modules/"], "coverageReporters": ["json", "lcov", "text", "text-summary", "clover", "cobertura"], "reporters": ["default", ["jest-junit", {"suiteName": "Jest Tests", "suiteNameTemplate": "{filepath}", "outputDirectory": "."}]]}, "jestSonar": {"reportFile": "test-report.xml", "indent": 4}, "dependencies": {"@date-io/core": "^1.3.6", "@date-io/moment": "^1.3.6", "@fullcalendar/core": "^4.4.2", "@fullcalendar/daygrid": "^4.4.2", "@fullcalendar/interaction": "^4.4.2", "@fullcalendar/list": "^4.4.2", "@fullcalendar/react": "^4.4.2", "@fullcalendar/timegrid": "^4.4.2", "@fullcalendar/timeline": "^4.4.3", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "^4.0.0-alpha.61", "@material-ui/pickers": "^3.3.10", "@material-ui/styles": "^4.11.5", "@react-pdf/renderer": "^1.6.17", "@rehooks/online-status": "^1.1.2", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "axios": "^0.19.0", "axios-mock-adapter": "^1.21.1", "axios-retry": "^3.3.1", "bootstrap": "^5.1.3", "chart.js": "^2.9.4", "clsx": "^1.2.1", "d3": "^5.16.0", "draft-js": "^0.11.7", "env-cmd": "^10.0.1", "history": "^4.7.2", "identity-obj-proxy": "^3.0.0", "immutable": "^4.1.0", "jest-canvas-mock": "^2.4.0", "jest-junit": "^13.2.0", "jest-sonar-reporter": "^2.0.0", "js-cookie": "^2.2.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "material-table": "^1.57.2", "moment": "^2.29.4", "msal": "^1.4.16", "node-sass": "^4.14.1", "prismjs": "^1.28.0", "prop-types": "^15.8.1", "react": "^16.14.0", "react-beautiful-dnd": "^11.0.4", "react-big-calendar": "^0.22.0", "react-bootstrap": "^2.4.0", "react-chartjs-2": "^2.11.2", "react-datepicker": "^2.16.0", "react-dom": "^16.14.0", "react-dropzone": "^10.1.5", "react-export-excel": "^0.5.3", "react-helmet": "^5.2.1", "react-infinite-scroller": "^1.2.6", "react-markdown": "^4.0.8", "react-material-ui-form-validator": "^2.1.4", "react-modal": "^3.15.1", "react-paginate": "^6.5.0", "react-perfect-scrollbar": "^1.5.3", "react-redux": "^7.2.8", "react-router": "^5.3.3", "react-router-config": "^5.0.1", "react-router-dom": "^5.3.3", "react-scripts": "^3.4.4", "react-window": "^1.8.7", "redux": "^4.2.0", "redux-devtools-extension": "^2.13.9", "redux-mock-store": "^1.5.4", "redux-thunk": "^2.4.1", "serve": "^11.3.2", "universal-cookie": "^4.0.3", "uuid": "^7.0.3", "validate.js": "^0.12.0", "validator": "^13.7.0", "xlsx": "^0.16.9"}, "devDependencies": {"chai": "^4.3.6", "eslint": "^6.6.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.0.0", "eslint-config-react-app": "3.0.6", "eslint-plugin-flowtype": "3.2.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^1.6.0", "husky": "^1.3.1", "prettier": "^1.19.1", "prettier-eslint": "^9.0.2", "prettier-eslint-cli": "^4.7.1", "typescript": "^3.9.10"}, "eslintConfig": {"extends": ["airbnb", "prettier", "react-app", "plugin:prettier/recommended"], "plugins": ["prettier", "react-hooks"], "rules": {"prettier/prettier": ["error"], "linebreak-style": "off", "jsx-a11y/href-no-hash": [0], "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx"]}], "no-magic-numbers": ["error", {"enforceConst": true, "ignore": [-1, 0, 1]}], "jsx-a11y/label-has-for": [2, {"components": ["Label"], "required": {"every": ["id"]}, "allowChildren": false}], "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}}, "prettier": {"printWidth": 100, "singleQuote": true}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}