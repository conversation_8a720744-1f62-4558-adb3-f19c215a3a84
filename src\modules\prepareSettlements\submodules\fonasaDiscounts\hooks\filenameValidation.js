import { setFilenameError } from '../actions';

const FilenameValidation = async (name, currentDate, dispatch) => {
  const isValidName = true;
  const FILENAME_PATTERN = /^Pr(e|é|\u00E9|é)stamos_Salud_\d{4}$/i;
  const VALID_EXTENSION = 'txt';
  const INVALID_NAME = `Nombre del archivo incorrecto, debe ser Préstamos_Salud_${currentDate}.txt`;
  const INVALID_EXTENSION = 'Formato de Archivo Incorrecto';

  const extension = name.substr(name.lastIndexOf('.') + 1);
  const filenameWithoutExtension = name.substr(0, name.lastIndexOf('.'));
  const date = name.replace(/\D/g, '');

  if (!FILENAME_PATTERN.test(filenameWithoutExtension) || currentDate !== date) {
    dispatch(setFilenameError(INVALID_NAME));
    return !isValidName;
  }
  // eslint-disable-next-line no-else-return
  else if (extension !== VALID_EXTENSION) {
    dispatch(setFilenameError(INVALID_EXTENSION));
    return !isValidName;
  }
  return isValidName;
};

export default FilenameValidation;
