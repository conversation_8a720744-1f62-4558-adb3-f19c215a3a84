/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import SnackbarManager from 'components/SnackbarManager';
import React from 'react';
import PropTypes from 'prop-types';
import { renderRoutes } from 'react-router-config';
import { Page } from 'components';
import { useSnackbar } from './hooks/useSnackbar';
import useStyles from './views/styles';
import { SummarizeSettlements } from './views/SummarizeSettlements';

const SettlementsReport = ({ route }) => {
  const classes = useStyles();
  const {
    error,
    setError,
    successSnackbar,
    errorSnackbar,
    setSuccessSnackbar,
    setErrorSnackbar
  } = useSnackbar();

  const onError = (err = 'Error al exportar archivo') => {
    setError(err);
    setSuccessSnackbar(false);
    setErrorSnackbar(true);
  };

  return (
    <Page className={classes.root} title="Reporte Liquidaciones de Pago">
      {renderRoutes(route.routes, { onError })}
      <SnackbarManager
        handleRoute={false}
        error={error}
        successSnackbar={successSnackbar}
        errorSnackbar={errorSnackbar}
        setErrorSnackbar={setErrorSnackbar}
        setSuccessSnackbar={setSuccessSnackbar}
      />
    </Page>
  );
};

SettlementsReport.propTypes = {
  route: PropTypes.shape({
    path: PropTypes.string,
    routes: PropTypes.array,
    component: PropTypes.func
  }).isRequired
};

// eslint-disable-next-line import/prefer-default-export
export { SettlementsReport, SummarizeSettlements };
