//------------------------------------------------------------------------------------------------
export const CREATE_NOM_BANK = 'CREATE_NOM_BANK';
export const createNomenclatorBank = bank => {
  return {
    type: CREATE_NOM_BANK,
    data: { bank }
  };
};
//------------------------------------------------------------------------------------------------
export const DELETE_NOM_BANK = 'DELETE_NOM_BANK';
export const deleteNomenclatorBank = bank => {
  return {
    type: DELETE_NOM_BANK,
    data: { bank }
  };
};
//------------------------------------------------------------------------------------------------
export const UPDATE_NOM_BANK = 'UPDATE_NOM_BANK';
export const updateNomenclatorBank = (prevBank, newBank) => {
  return {
    type: UPDATE_NOM_BANK,
    data: { prevBank, newBank }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_NOM_BANK = 'SET_NOM_BANK';
export const loadNomenclatorBanks = banks => {
  return {
    type: SET_NOM_BANK,
    data: { banks }
  };
};
