import {
  SET_CURRENT_MONTH_YEAR_QUADRATURE,
  SET_CURRENT_YEAR_QUADRATURE,
  SET_FACTORS_FILENAME_ERROR,
  SET_CONCURRENCIES_FILENAME_ERROR,
  SET_FACTORS_FILE_DATA,
  SET_CONCURRENCIES_FILE_DATA,
  SET_HAS_FACTORS_FILE,
  SET_HAS_CONCURRENCIES_FILE,
  SET_FILE_ERROR,
  SET_FILE_TYPE,
  SET_IS_DOWNLOADING_QUADRATURE
} from './actions';

const initialState = {
  currentMonthYear: '',
  currentYear: '',
  factorsFilenameError: '',
  concurrenciesFilenameError: '',
  factorsFileData: [],
  concurrenciesFileData: [],
  hasFactorsFile: false,
  hasConcurrenciesFile: false,
  fileError: [],
  fileType: '',
  isCurrentlyDownloading: false
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case SET_CURRENT_MONTH_YEAR_QUADRATURE:
      return {
        ...state,
        currentMonthYear: action.data
      };
    case SET_CURRENT_YEAR_QUADRATURE:
      return {
        ...state,
        currentYear: action.data
      };
    case SET_FACTORS_FILENAME_ERROR:
      return {
        ...state,
        factorsFilenameError: action.data
      };
    case SET_CONCURRENCIES_FILENAME_ERROR:
      return {
        ...state,
        concurrenciesFilenameError: action.data
      };
    case SET_FACTORS_FILE_DATA:
      return {
        ...state,
        factorsFileData: action.data
      };
    case SET_CONCURRENCIES_FILE_DATA:
      return {
        ...state,
        concurrenciesFileData: action.data
      };
    case SET_HAS_FACTORS_FILE:
      return {
        ...state,
        hasFactorsFile: action.data
      };
    case SET_HAS_CONCURRENCIES_FILE:
      return {
        ...state,
        hasConcurrenciesFile: action.data
      };
    case SET_FILE_ERROR:
      return {
        ...state,
        fileError: action.data
      };
    case SET_FILE_TYPE:
      return {
        ...state,
        fileType: action.data
      };
    case SET_IS_DOWNLOADING_QUADRATURE:
      return {
        ...state,
        isCurrentlyDownloading: action.data
      };
    default:
      return state;
  }
}
