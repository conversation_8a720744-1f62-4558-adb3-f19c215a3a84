import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import useOnLineStatus from '@rehooks/online-status';
import moment from 'moment';
import Grid from '@material-ui/core/Grid';

import { HeaderContent } from 'components';
import { Box } from '@material-ui/core';
import useStyles from './styles';
import AssetsAndDiscountsFormulable from '../components/assetsAndDiscounts/assetsAndDiscountsFormulable';

import {
  setFormulableLabel,
  setFormulableReason,
  setFormulableAssetType,
  setFormulableAmount,
  setFormulableTypeRetention,
  setFormulableValidity,
  setFormulableAmountRetention,
  setFormulableStartDate,
  setFormulableEndDate,  
  setCollectorRetentionRut,
  setCollectorRetentionName,
  setCollectorRetentionLastName,
  setCollectorRetentionMothersLastName,
  setCollectorRetentionAddress,
  setCollectorRetentionCommune,
  setCollectorRetentionCity,
  setRetentionPaymentGateway,
  setRetentionBank,
  setRetentionAccountNumber,
  setRetentionBranchOffice,
  setRetentionVisibility,
  setDiscountsAndAssets,
  setModifiedFieldErrors
} from '../../../modules/retirementQuery/actions';

const DATE_FORMAT = 'MM-YYYY';
const currentDate = moment(new Date()).format(DATE_FORMAT);
const editTitle = 'Editar haberes y descuentos no formulables';
const addTitle = 'Agregar haber o descuento no formulable';
const initialState = {
  label: 'Haber',
  reason: '',
  amount: 0,
  endDate: currentDate,
  startDate: currentDate
};

function EditAssetsAndDiscounts(props) {
  
  const dispatch = useDispatch();
 
  const [state] = useState({ ...initialState, ...props.location.state });
  const [isEditing] = useState(state._id);
  const onlineStatus = useOnLineStatus();
  const classes = useStyles();
  let judicialRetentioncollector = {
      collectorRetentionRut: '',
      collectorRetentionName: '',
      collectorRetentionLastName: '',
      collectorRetentionMothersLastName: '',
      collectorRetentionCity: '',
      collectorRetentionAddress: '',
      collectorRetentionCommune: ''    
    }
  let judicialRetentionPaymentInfo = {
      retentionPaymentGateway :'',
      retentionBank: '',
      retentionBranchOffice: '',
      retentionAccountNumber: ''
  }
  let timeout;
  
  useEffect(() => {        
    const { discountsAndAssets, amount, endDate, assetType , label, reason, startDate, _id } = state;
    dispatch(setRetentionVisibility(false))  
    dispatch(setDiscountsAndAssets(state))    
    if (isEditing) {
      const key = 'discountsNonFormulable';
      const index = discountsAndAssets[key].findIndex(r => r._id === _id);
      if(index !== -1 && discountsAndAssets[key][index].judicialRetention){
        dispatch(setRetentionVisibility(true)) 
        dispatch(setCollectorRetentionRut(discountsAndAssets[key][index].judicialRetention.collector.rut || ''));
        dispatch(setCollectorRetentionName(discountsAndAssets[key][index].judicialRetention.collector.name || ''));
        dispatch(setCollectorRetentionLastName(discountsAndAssets[key][index].judicialRetention.collector.lastName || ''));
        dispatch(setCollectorRetentionMothersLastName(discountsAndAssets[key][index].judicialRetention.collector.mothersLastName || ''));
        dispatch(setCollectorRetentionAddress(discountsAndAssets[key][index].judicialRetention.collector.address || ''));
        dispatch(setCollectorRetentionCommune(discountsAndAssets[key][index].judicialRetention.collector.commune || ''));
        dispatch(setCollectorRetentionCity(discountsAndAssets[key][index].judicialRetention.collector.city || ''));
        dispatch(setRetentionPaymentGateway(discountsAndAssets[key][index].judicialRetention.paymentInfo.paymentGateway ||''));
        dispatch(setRetentionBank(discountsAndAssets[key][index].judicialRetention.paymentInfo.bank || ''));
        dispatch(setRetentionAccountNumber(discountsAndAssets[key][index].judicialRetention.paymentInfo.accountNumber || ''));
        dispatch(setRetentionBranchOffice(discountsAndAssets[key][index].judicialRetention.paymentInfo.branchOffice || ''));        
        dispatch(setFormulableTypeRetention(discountsAndAssets[key][index].judicialRetention.retention.type || ''));
        const validity = discountsAndAssets[key][index].judicialRetention.retention.validity ? 'Si': 'No';
        dispatch(setFormulableValidity(validity));
        const amountRetention = discountsAndAssets[key][index].judicialRetention.retention.amount || '';
        dispatch(setFormulableAmountRetention(amountRetention.toString().replace('.',',')));
        dispatch(
          setModifiedFieldErrors({
            retentionAccountNumber: false,
            retentionBank: false,
            retentionPaymentGateway: false
          })
        );

        judicialRetentioncollector.collectorRetentionRut = discountsAndAssets[key][index].judicialRetention.collector.rut || '';
        judicialRetentioncollector.collectorRetentionName = discountsAndAssets[key][index].judicialRetention.collector.name || '';
        judicialRetentioncollector.collectorRetentionLastName = discountsAndAssets[key][index].judicialRetention.collector.lastName || '';
        judicialRetentioncollector.collectorRetentionMothersLastName = discountsAndAssets[key][index].judicialRetention.collector.mothersLastName || '';
        judicialRetentioncollector.collectorRetentionAddress = discountsAndAssets[key][index].judicialRetention.collector.address || '';
        judicialRetentioncollector.collectorRetentionCommune = discountsAndAssets[key][index].judicialRetention.collector.commune || '';
        judicialRetentioncollector.collectorRetentionCity = discountsAndAssets[key][index].judicialRetention.collector.city || '';
        
        judicialRetentionPaymentInfo.retentionPaymentGateway = discountsAndAssets[key][index].judicialRetention.paymentInfo.paymentGateway ||'';
        judicialRetentionPaymentInfo.retentionBank = discountsAndAssets[key][index].judicialRetention.paymentInfo.bank || '';
        judicialRetentionPaymentInfo.retentionBranchOffice = discountsAndAssets[key][index].judicialRetention.paymentInfo.branchOffice || '';
        judicialRetentionPaymentInfo.retentionAccountNumber = discountsAndAssets[key][index].judicialRetention.paymentInfo.accountNumber || '';

      
      }
    } else{
      dispatch(setCollectorRetentionRut(''));
      dispatch(setCollectorRetentionName(''));
      dispatch(setCollectorRetentionLastName(''));
      dispatch(setCollectorRetentionMothersLastName(''));
      dispatch(setCollectorRetentionAddress(''));
      dispatch(setCollectorRetentionCommune(''));
      dispatch(setCollectorRetentionCity(''));
      dispatch(setRetentionPaymentGateway('Seleccione vía de pago'));
      dispatch(setRetentionBank(''));
      dispatch(setRetentionAccountNumber(''));
      dispatch(setRetentionBranchOffice(''));        
      dispatch(setFormulableTypeRetention(''));
      dispatch(setFormulableValidity('Si'));
      dispatch(setFormulableAmountRetention('')); 
      
    }

    dispatch(setFormulableLabel(label || ''));
    dispatch(setFormulableReason(reason || ''));
    dispatch(setFormulableAssetType(assetType || ''));  
    dispatch(setFormulableAmount(amount || 0));    
    dispatch(setFormulableStartDate(startDate || ''));    
    dispatch(setFormulableEndDate(endDate || ''));   

    return () => {
      clearTimeout(timeout);
    };    
    
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Box mb={5}>
        <HeaderContent title={isEditing ? editTitle : addTitle} />
      </Box>

      <Grid container spacing={5}>
          <Grid item className={classes.gridStyle}>
            <AssetsAndDiscountsFormulable
              values={judicialRetentioncollector}
              valuesLiquidation={judicialRetentionPaymentInfo}
              isEditing={isEditing}
              beneficiaryRut={state.discountsAndAssets.beneficiaryRut}
              causantRut={state.discountsAndAssets.causantRut}
              pensionCodeId={state.discountsAndAssets.pensionCodeId}
              editable={true}
              onlineStatus={onlineStatus}
              isReadOnly={state.readOnly}
            />
          </Grid>
      </Grid>	
    </>
  );
}

export default EditAssetsAndDiscounts;
