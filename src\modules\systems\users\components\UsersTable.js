/* eslint-disable no-underscore-dangle */
/* eslint-disable react/prop-types */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { forwardRef, useState, useEffect, useRef } from 'react';
import MaterialTable, { MTableEditRow } from 'material-table';
import { Button, Grid, Tooltip, IconButton, Typography, TextField } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import PropTypes from 'prop-types';
import { ValidatorForm } from 'react-material-ui-form-validator';

import { ArrowDownward, Clear, Check, DeleteOutline, Edit } from '@material-ui/icons';

import {
  defaultFormatter,
  emailFormatter,
  matchName,
  matchEmail,
  matchRole
} from '../validator/validField';

import SecureTextField from './secureTextField';
import SelectTextField from './SelectTextField';
import styles from './styles';
import { checkWritePermission } from '../../../../utils/checkUserPermission';

const BASIC_ROLE_REGEX = /^Visualizaci[oó]n - ACHS$/i;

const tableIcons = {
  Check: forwardRef((props, ref) => <Check {...props} ref={ref} />),
  Edit: forwardRef((props, ref) => <Edit {...props} ref={ref} />),
  Delete: forwardRef((props, ref) => <DeleteOutline {...props} ref={ref} />),
  Clear: forwardRef((props, ref) => <Clear {...props} ref={ref} />),
  SortArrow: forwardRef((props, ref) => <ArrowDownward {...props} ref={ref} />)
};

const isRowValid = ({ name, email, role }) => {
  return matchName(name) && matchEmail(email) && matchRole(role);
};

const lengthObject = { name: '70', email: 120 };
const getMaxLength = fieldName => ({ maxLength: lengthObject[fieldName] || '20' });
const editableFn = ({ fieldName, labelName, validators, errorMessages, formater, type }) => ({
  onChange,
  value
}) => (
  <SecureTextField
    name={fieldName}
    size="small"
    displayName={labelName}
    type={type}
    inputprops={getMaxLength(fieldName)}
    validators={validators}
    errorMessages={errorMessages}
    onChange={e => onChange(formater ? formater(e.target.value) : defaultFormatter(e.target.value))}
    onBlur={e => onChange(e.target.value)}
    value={value || ''}
  />
);

const SelectFn = (fieldName, labelName, options) => ({ onChange, value = '' }) => {
  return (
    <SelectTextField
      name={fieldName}
      displayName={labelName}
      size="small"
      inputprops={getMaxLength(fieldName)}
      onChange={e => onChange(e.target.value)}
      value={value}
      options={options}
    />
  );
};

const UsersTable = props => {
  const {
    data,
    roles,
    onCreate,
    onUpdate,
    onDelete,
    onSubmit,
    enabledButton,
    selectedRole,
    onRoleFilterChange,
    userRole
  } = props;

  const classes = styles();
  const onlineStatus = useOnlineStatus();
  const formRef = useRef(null);

  const [disabledAddBtn, setDisabledAddBtn] = useState(enabledButton);
  const [displayedData, setDisplayedData] = useState(data);

  const selectComponent = SelectFn('role', 'Rol', roles);
  const hasWritePermission = checkWritePermission(userRole);

  const nameComponent = editableFn({
    fieldName: 'name',
    labelName: 'Nombre',
    validators: ['required', 'matchName'],
    errorMessages: ['El nombre es requerido', 'Nombre inválido']
  });
  const emailComponent = editableFn({
    fieldName: 'email',
    labelName: 'Email',
    validators: ['required', 'matchEmail'],
    errorMessages: ['El email es obligatorio', 'Email inválido'],
    formater: emailFormatter,
    type: 'email'
  });

  useEffect(() => {
    if (!selectedRole) {
      return setDisplayedData(data);
    }
    const filteredData = data.filter(({ role }) => role === selectedRole);
    return setDisplayedData(filteredData);
  }, [selectedRole, data]);

  useEffect(() => {
    setDisabledAddBtn(enabledButton);
  }, [enabledButton]);

  return (
    <ValidatorForm onSubmit={onSubmit} ref={formRef} instantValidate className={classes.table}>
      <MaterialTable
        data={displayedData}
        options={{
          addRowPosition: 'last',
          thirdSortClick: false,
          paging: false,
          search: false,
          showTitle: false,
          actionsColumnIndex: -1,
          toolbar: true
        }}
        localization={{
          body: {
            emptyDataSourceMessage: 'No existen registros ingresados',
            editTooltip: 'Editar',
            deleteTooltip: 'Eliminar',
            editRow: {
              deleteText: '¿Está seguro que desea eliminar el elemento?',
              saveTooltip: 'Aceptar',
              cancelTooltip: 'Cancelar'
            }
          },
          header: {
            actions: 'Acciones'
          }
        }}
        columns={[
          {
            title: 'Nombre',
            field: 'name',
            sorting: false,
            editComponent: nameComponent
          },
          {
            title: 'Nombre de usuario',
            field: 'email',
            sorting: false,
            editComponent: emailComponent
          },
          {
            title: 'Rol',
            sorting: false,
            field: 'role',
            editComponent: selectComponent,
            render: ({ role }) => {
              const foundRole = roles.find(({ _id }) => role === _id);
              return foundRole?.roleName;
            }
          }
        ]}
        icons={tableIcons}
        components={{
          Action: p => {
            let { action } = p;
            if (typeof p.action === 'function') {
              action = p.action();
            }
            return (
              <Tooltip title={!action.disabled && !p.disabled ? action.tooltip : ''}>
                <IconButton
                  onClick={event => {
                    if (action.tooltip === 'Editar' || action.tooltip === 'Eliminar') {
                      setDisabledAddBtn(true);
                    }
                    action.onClick(event, p.data);
                  }}
                  color="inherit"
                  variant="contained"
                  className={classes.iconButtonStyle}
                  size={p.size}
                  disabled={!hasWritePermission || action.disabled || p.disabled || !onlineStatus}
                >
                  {action.icon.render()}
                </IconButton>
              </Tooltip>
            );
          },
          Toolbar: p => {
            return (
              <Grid container justify="space-between" className={classes.gridContainerStyle}>
                <Grid
                  container
                  item
                  className={classes.gridStyle}
                  alignItems="center"
                  justify="flex-start"
                >
                  <Typography className={classes.role}>Rol</Typography>
                  <TextField
                    select
                    disabled={disabledAddBtn || !onlineStatus}
                    value={selectedRole}
                    InputLabelProps
                    onChange={onRoleFilterChange}
                    SelectProps={{
                      native: true
                    }}
                    placeholder="Seleccione"
                    variant="outlined"
                    margin="dense"
                  >
                    <option key="" value="">
                      Todos los roles
                    </option>
                    {roles.map(opt => (
                      <option key={opt._id} value={opt._id}>
                        {opt.roleName}
                      </option>
                    ))}
                  </TextField>
                </Grid>
                <Grid container item className={classes.gridStyle} justify="flex-end">
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                      setDisabledAddBtn(true);
                      p.actions[0].onClick();
                    }}
                    disabled={!hasWritePermission || disabledAddBtn || !onlineStatus}
                  >
                    Agregar Usuario
                  </Button>
                </Grid>
              </Grid>
            );
          },
          EditRow: p => (
            <MTableEditRow
              {...p}
              onEditingCanceled={(mode, rowData) => {
                setDisabledAddBtn(false);
                return p.onEditingCanceled(mode, rowData);
              }}
              onEditingApproved={async (mode, newData, oldData) => {
                if (!isRowValid(newData)) {
                  formRef.current.submit();
                  return null;
                }

                return p.onEditingApproved(mode, newData, oldData);
              }}
            />
          )
        }}
        editable={{
          onRowAdd: newData =>
            new Promise(async (resolve, reject) => {
              if (!isRowValid(newData)) {
                setDisabledAddBtn(false);

                return reject();
              }
              try {
                await onCreate({ ...newData }, {});
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return resolve();
              }
            }),
          onRowUpdate: (newData, oldData) =>
            new Promise(async (resolve, reject) => {
              if (!isRowValid(newData)) {
                return reject();
              }
              try {
                await onUpdate(newData, oldData);
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return resolve();
              }
            }),
          onRowDelete: oldData =>
            new Promise(async (resolve, reject) => {
              try {
                const matchedRole = roles.find(role => BASIC_ROLE_REGEX.test(role.roleName));
                // eslint-disable-next-line no-param-reassign
                oldData.role = matchedRole._id;
                await onDelete(oldData);
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return reject();
              }
            })
        }}
      />
    </ValidatorForm>
  );
};

UsersTable.propTypes = {
  data: PropTypes.oneOfType([PropTypes.array, PropTypes.func]).isRequired,
  onCreate: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired
};

export default UsersTable;
