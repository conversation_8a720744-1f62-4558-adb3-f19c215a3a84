import { makeStyles } from '@material-ui/styles';

const useStyles = makeStyles(theme => ({
  root: {
    // eslint-disable-next-line no-magic-numbers
    padding: theme.spacing(3)
  },
  alertBar: {
    fontWeight: 'bold',
    color: 'gray'
  },
  calendar: {
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 0,
    maxWidth: 200,
    minWidth: 200
  },
  grid: {
    marginTop: 10,
    marginBottom: 10,
    flexDirection: 'row',
    '@mediaQuery(minWidth: 600px)': {
      flexDirection: 'column'
    }
  },
  importDownloadButton: {
    fontWeight: 'bold'
  },
  textfield: {
    width: '33%',
    minWidth: 300,
    maxWidth: 300,
    marginBottom: 0,
    '& .MuiInputBase-root.Mui-disabled': {
      color: '#212121'
    }
  },
  title: {
    color: 'gray',
    fontWeight: 'bold'
  },
  tooltip: {
    fontSize: 11,
    marginTop: 0
  }
}));

export default useStyles;
