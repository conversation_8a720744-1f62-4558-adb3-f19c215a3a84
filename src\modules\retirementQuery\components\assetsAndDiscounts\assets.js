/* eslint-disable react/prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import { useSelector, useDispatch } from 'react-redux';
import useStyles from '../pensionerDetail/style';
import createTable from '../../utils/createTable';
import { setForFamilyAssignment, setArticle40, setModifiedFieldErrors } from '../../actions';
import { CHILEAN_AMOUNT_PATTERN, formatChileanAmount } from '../../utils/formatters';
import { handleAmountChange } from '../../utils/handlers';

const MAX_AMOUNT_LENGTH = 10;

const Assets = ({ values, editable }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();

  const dispatch = useDispatch();

  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);
  const forFamilyAssignment = useSelector(store => store.queryPensions.forFamilyAssignment);
  const article40 = useSelector(store => store.queryPensions.article40);

  const rowFormation = [
    [
      {
        key: 'aps',
        name: 'Haber por APS'
      }
    ],
    [
      {
        key: 'nationalHolidaysBonus',
        name: 'Aguinaldo fiestas patrias'
      }
    ],
    [
      {
        key: 'christmasBonus',
        name: 'Aguinaldo navidad'
      }
    ],
    [
      {
        key: 'winterBonus',
        name: 'Bono invierno'
      }
    ],
    [
      {
        key: 'marriageBonus',
        name: 'Bono matrimonio'
      }
    ],
    [
      {
        key: 'rebsal',
        name: 'Haber rebaja de salud'
      }
    ],
    [
      {
        key: 'adjustedHealthExemption',
        name: 'Haber exención de salud'
      }
    ],
    [
      {
        key: 'article40',
        name: 'Artículo 40',
        type: 'text',
        toWrite: article40,
        handleInputChange: handleAmountChange,
        dispatch,
        action: setArticle40,
        actionError: setModifiedFieldErrors,
        modifiedField: article40,
        maxLength: MAX_AMOUNT_LENGTH,
        condition: true,
        validation: CHILEAN_AMOUNT_PATTERN,
        errorMessage: 'Valor incorrecto',
        modifiedFieldErrors,
        formatter: formatChileanAmount
      }
    ],
    [
      {
        key: 'article41',
        name: 'Artículo 41'
      }
    ],
    [
      {
        key: 'law19403',
        name: 'Ley 19403'
      }
    ],
    [
      {
        key: 'law19539',
        name: 'Ley 19539'
      }
    ],
    [
      {
        key: 'law19953',
        name: 'Ley 19953'
      }
    ],
    [
      {
        key: 'forFamilyAssignment',
        name: 'Haber asignación familiar',
        type: 'text',
        toWrite: forFamilyAssignment,
        handleInputChange: handleAmountChange,
        dispatch,
        action: setForFamilyAssignment,
        actionError: setModifiedFieldErrors,
        modifiedField: forFamilyAssignment,
        maxLength: 10,
        condition: true,
        validation: CHILEAN_AMOUNT_PATTERN,
        errorMessage: 'Valor incorrecto',
        modifiedFieldErrors,
        formatter: formatChileanAmount
      }
    ],
    [
      {
        key: 'taxableTotalNonFormulable',
        name: 'Haberes no formulable imponible (totales)'
      }
    ],
    [
      {
        key: 'netTotalNonFormulable',
        name: 'Haberes no formulable líquido (totales)'
      }
    ],
    [
      {
        key: 'totalAssets',
        name: 'Total haberes'
      }
    ],
    [
      {
        key: 'forBasePension',
        name: 'Monto retroactivo por pensión base'
      }
    ],
    [
      {
        key: 'forArticle40',
        name: 'Monto retroactivo por artículo 40'
      }
    ],
    [
      {
        key: 'forArticle41',
        name: 'Monto retroactivo por artículo 41'
      }
    ],
    [
      {
        key: 'forFamilyAssignmentRetroActiveAmount',
        name: 'Monto retroactivo por asignación familiar'
      }
    ],
    [
      {
        key: 'forTaxableTotalNonFormulableAssets',
        name: 'Monto retroactivo por haberes no formulables totales imponible'
      }
    ],
    [
      {
        key: 'forInstitutionalPatient',
        name: 'Monto retroactivo por paciente institucional'
      }
    ],
    [
      {
        key: 'forRejection',
        name: 'Monto retroactivo por rechazo'
      }
    ],
    [
      {
        key: 'forBonuses',
        name: 'Monto retroactivo por aguinaldos'
      }
    ],
    [
      {
        key: 'retroactiveAmountForPaycheck',
        name: 'Monto retroactivo por Vale Vista reintegrado'
      }
    ],
    [
      {
        key: 'forNetTotalNonFormulableAssets',
        name: 'Monto retroactivo por haberes no formulables totales líquido'
      }
    ],
    [
      {
        key: 'totalPensionAccrued',
        name: 'Monto devengado'
      }
    ],
    [
      {
        key: 'strennaRetroConstitution',
        name: 'Aguinaldo devengado'
      }
    ],
    [
      {
        key: 'otherLink',
        name: 'Otros Enlazar'
      }
    ]  
  ];

  return (
    <Card className={classes.cardContainer}>
      <CardHeader title=" Haberes" className={classes.cardHeader} />
      <Divider />
      <CardContent className={classes.content}>
        {createTable({
          data: values,
          editable: editable && onlineStatus,
          format: rowFormation,
          panelName: 'assets'
        })}
      </CardContent>
    </Card>
  );
};

Assets.propTypes = {
  values: PropTypes.shape({}).isRequired
};

export default Assets;
