import { makeStyles } from '@material-ui/styles';

const styles = makeStyles(theme => ({
  root: {
    // eslint-disable-next-line no-magic-numbers
    padding: theme.spacing(3)
  },
  alertBar: {
    fontWeight: 'bold',
    color: 'gray',
    width: 1000
  },
  calendar: {
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 0,
    maxWidth: 200,
    minWidth: 200
  },
  grid: {
    marginTop: 10,
    marginBottom: 10,
    marginLeft: 10
  },
  gripTop: { marginTop: 40, marginBottom: 40, marginLeft: 40, marginRight: 40 },
  gripBottom: {
    marginBottom: 40,
    marginLeft: 40,
    marginRight: 40
  },
  textfield: {
    width: '33%',
    minWidth: 300,
    maxWidth: 300,
    marginBottom: 0,
    '& .MuiInputBase-root.Mui-disabled': {
      color: '#212121'
    }
  },
  title: {
    color: 'gray',
    fontWeight: 'bold'
  },
  tooltip: {
    fontSize: 11,
    marginTop: 0
  },
  cardStyle: {
    marginTop: 20,
    marginBottom: 20,
    marginLeft: 20,
    marginRight: 20,
    width: 1000,
    height: 550
  }
}));

export default styles;
