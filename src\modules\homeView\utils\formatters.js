/* eslint-disable no-useless-escape */
import moment from 'moment';

const placesBetweenDots = 3;
const maxLength = 12;
const lengthToIncludeAfterDash = 2;

const RUT_DYNAMIC_PATTERN = /^\d{1,2}\.?\d{3}\.?\d{3}-?([0-9]{1,2}|[0-9][kK]?)$/;
const RUT_PATTERN = /^(\d{1,2})\.?(\d{3})\.?(\d{3})-([0-9kK])$/;

const formatRut = rut =>
  rut
    .replace(/[^\dkK]/g, '')
    .replace(/([0-9kK])$/, '-$1')
    .replace(/[k](?=.*[k])/gi, '')
    .replace(RUT_PATTERN, '$1.$2.$3-$4')
    .toUpperCase();

const rutWithDots = rut =>{  
  return new Intl.NumberFormat('es-CL', {  
    maximumFractionDigits: 0
  }).format(`${rut}`
      .replace(/^0+/, '')
      .replace(/\D/g, ''));  
  };

const removeEverythingAfter = (string, charToLookup) => {
  const placeOfChar = string.indexOf(charToLookup);
  if (placeOfChar === -1) return string;
  return string.substring(0, placeOfChar + lengthToIncludeAfterDash);
};

const doesRutMatch = rut => RUT_DYNAMIC_PATTERN.test(rut) || RUT_PATTERN.test(rut);

const dynamicFormatting = rut => {
  if (doesRutMatch(rut) && rut.length <= maxLength) {
    return formatRut(rut);
  }
  if (rut.length <= maxLength - placesBetweenDots - 1) return rutWithDots(rut);
  if (rut.length > maxLength) return formatRut(rut.substring(0, rut.length - 1));
  const noAfterHyphen = removeEverythingAfter(rut, '-').replace(/[^\dkK]/g, '');
  return noAfterHyphen.length >= maxLength - placesBetweenDots - 1
    ? formatRut(noAfterHyphen)
    : rutWithDots(noAfterHyphen);
};

const unformatRut = rut => rut.replace(/\./g, '');

const checkDigitValidation = rutWithDV => {
  const [rut, currentDV] = rutWithDV.replace(/\./, '').split('-');
  const module = 11;
  const multipliers = '234567'.split('').map(x => +x);
  const sumOfElements = rut
    .replace(/[^\d]/g, '')
    .split('')
    .reverse()
    .map((value, index) => +value * multipliers[index % multipliers.length])
    .reduce((accumulator, currentValue) => accumulator + currentValue);
  let actualDV = module - (sumOfElements % module);
  if (actualDV === module - 1) {
    actualDV = 'k';
    return actualDV === currentDV.toLowerCase();
  }
  if (actualDV === module) actualDV = 0;
  return actualDV === +currentDV;
};

const pensionCodeIdFormatter = codeId => codeId.replace(/\D/, '');

const isValidPensionCodeID = codeId => /^\d+$/.test(codeId);

const formatDate = date => (date ? moment(date).format('DD-MM-YYYY') : '');

const formatInput = input => input.replace(/[^0-9kK-]/g, '').replace(/^0+/g, '');

const millionFormat = num => {
  return num > 1000000 ? Intl.NumberFormat('es-CL', {
    notation: "compact",
    compactDisplay: "short"
  }).format(num) : num;  
};

export {
  formatInput,
  dynamicFormatting,
  formatDate,
  formatRut,
  pensionCodeIdFormatter,
  isValidPensionCodeID,
  unformatRut,
  checkDigitValidation,
  RUT_PATTERN,
  millionFormat
};
