/* eslint-disable no-magic-numbers */
import { makeStyles } from '@material-ui/styles';

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(3)
  },
  errorDisplay: {
    color: 'red',
    fontSize: 10
  },
  formControl: {
    marginTop: 20,
    marginRight: 20,
    marginBottom: 20,
    color: 'black',
    backgroundColor: '#00FFFFF'
  },
  finish: {
    marginLeft: '33%',
    marginTop: 40
  },
  importText: {
    fontSize: 10,
    fontWeight: '1',
    marginLeft: 10,
    color: 'Gray'
  },
  subtitle: {
    fontSize: 11,
    fontWeight: 5
  },
  textfield: {
    width: '35%',
    marginTop: 20,
    marginRight: 20,
    minWidth: 210
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15
  }
}));

export default useStyles;
