/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Grid, Typography, Card } from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { useProgress } from 'components';
import moment from 'moment';
import useStyles from './styles';

import DownloadForm from './downloadForm/downloadForm';
import UseDataDownload from './downloadForm/hooks/useDownloadReport';
import { checkCronExecution } from '../../../services/currentCapitalService';

const alertText =
  'Los reportes serán actualizados con la información al final de mes luego del cálculo de capitales vigentes';

const titleNewCurrent =
  "Pensiones vigentes (mes actual) con Capital 'Nuevo' y 'Mantiene' por Tipo de pensión y Tipo de invalidez";
const titleHoldCurrent =
  "Pensiones vigentes (en el mes anterior) con Capital 'Mantiene' y 'Salida' por Tipo de pensión y Tipo de invalidez";

const titleCurrentIn =
  'Pensionados Nuevos y Reactivados (mes actual)  por Tipo de pensión y Motivo de Reactivación';
const titleHoldCurrentOut =
  'Pensionados Salientes (Inactivos en mes actual) por Tipo de Pension y  Motivo de Inactivación';
const titleAccountability = 'Contabilización de Capitales Vigentes (mes actual)';
const reportCronName = 'ANALYSIS_OF_CURRENT_CAPITAL';

const capitalize = str => str.charAt(0).toUpperCase() + str.slice(1);

const month = moment()
  .locale('es')
  .format('MMMM');
const date = moment().format('YYYY');

const previousMonth = moment()
  .subtract(1, 'month')
  .locale('es')
  .format('MMMM');

const CurrentCapitalDocuments = ({ onErrorSnackbar }) => {
  const dispatch = useDispatch();
  const classes = useStyles();

  const [isCronExecuted, setIsCronExecuted] = useState(false);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });
  const handleRequestDownload = reportType =>
    UseDataDownload({
      dispatch,
      progress,
      onErrorSnackbar,
      reportType
    });

  useEffect(() => {
    checkCronExecution(reportCronName)(setIsCronExecuted);
  }, []);

  return (
    <>
      <Grid className={classes.grid}>
        <Alert className={classes.alertBar} severity="error" color="info">
          {alertText}
        </Alert>
      </Grid>
      <Card className={classes.cardStyle}>
        <Grid className={classes.gripTop}>
          <Grid className={classes.grid}>
            <Grid className={classes.grid}>
              <Typography className={classes.title}>{titleNewCurrent}</Typography>
            </Grid>

            <DownloadForm
              label="Descargar"
              classes={classes}
              value={`Vigentes ${date} Reporte ${capitalize(month)}`}
              tooltipTitle="Descargar"
              onClick={handleRequestDownload('report1')}
              variant="contained"
              color="primary"
              disabled={!isCronExecuted}
            />
            <Grid className={classes.grid}>
              <Typography className={classes.title}>{titleHoldCurrent}</Typography>
            </Grid>
            <DownloadForm
              label="Descargar"
              classes={classes}
              value={`Vigentes ${date} Reporte ${capitalize(previousMonth)}`}
              tooltipTitle="Descargar"
              onClick={handleRequestDownload('report2')}
              variant="contained"
              color="primary"
              disabled={!isCronExecuted}
            />
          </Grid>
        </Grid>
        <Grid className={classes.gripBottom}>
          <Grid className={classes.grid}>
            <Grid className={classes.grid}>
              <Typography className={classes.title}>{titleCurrentIn}</Typography>
            </Grid>

            <DownloadForm
              label="Descargar"
              classes={classes}
              value={`Entradas vigentes ${date} ${capitalize(month)}`}
              onClick={handleRequestDownload('report3')}
              tooltipTitle="Descargar"
              disabled={!isCronExecuted}
            />
            <Grid className={classes.grid}>
              <Typography className={classes.title}>{titleHoldCurrentOut}</Typography>
            </Grid>
            <DownloadForm
              label="Descargar"
              classes={classes}
              value={`Salidas vigentes ${date} ${capitalize(month)}`}
              onClick={handleRequestDownload('report4')}
              tooltipTitle="Descargar"
              disabled={!isCronExecuted}
            />
          </Grid>
        </Grid>
        <Grid className={classes.gripBottom}>
          <Grid className={classes.grid}>
            <Grid className={classes.grid}>
              <Typography className={classes.title}>{titleAccountability}</Typography>
            </Grid>

            <DownloadForm
              label="Descargar"
              classes={classes}
              value={`Contabilización vigentes ${date} ${capitalize(month)}`}
              onClick={handleRequestDownload('report5')}
              tooltipTitle="Descargar"
              disabled={!isCronExecuted}
            />
          </Grid>
        </Grid>
      </Card>
    </>
  );
};

export default CurrentCapitalDocuments;
