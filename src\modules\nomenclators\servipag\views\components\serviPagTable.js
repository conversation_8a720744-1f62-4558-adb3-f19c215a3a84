/* eslint-disable react/prop-types */
/* eslint-disable react/forbid-prop-types */
import { v4 as uuidv4 } from 'uuid';
import { Button, Grid, Tooltip, IconButton } from '@material-ui/core';

import { ArrowDownward, Clear, DeleteOutline, Edit, Check } from '@material-ui/icons';
import MaterialTable, { MTableToolbar, MTableEditRow } from 'material-table';
import PropTypes from 'prop-types';
import React, { forwardRef, useRef, useState, useEffect } from 'react';
import { ValidatorForm } from 'react-material-ui-form-validator';
import SecureTextField, { matchRule } from './secureTextField';
import { codeFormatter, codeMatchRule, defaultFormatter } from '../../validator/validField';
import { checkWritePermission } from 'utils/checkUserPermission';

const codeValidator = ['required', 'matchCode', 'maxStringLength:5'];
const codeErrorMessage = [
  'El valor es requerido',
  'El código es inválido',
  'El tamaño debe ser 5 caracteres'
];
const tableIcons = {
  Check: forwardRef((props, _ref) => <Check {...props} />),
  Edit: forwardRef((props, ref) => <Edit {...props} ref={ref} />),
  Delete: forwardRef((props, ref) => <DeleteOutline {...props} ref={ref} />),
  Clear: forwardRef((props, ref) => <Clear {...props} ref={ref} />),
  SortArrow: forwardRef((props, ref) => <ArrowDownward {...props} ref={ref} />)
};
const isRowValid = ({ code, name, city, address }) =>
  codeMatchRule(code) && matchRule(name) && matchRule(city) && matchRule(address);
const lengthObject = { code: '3', default: '255' };
const getMaxLength = (fieldName = 'default') => ({ maxLength: lengthObject[fieldName] || '255' });
const editableFn = (fieldName, labelName, validators, errorMessages, formater) => ({
  onChange,
  value
}) => (
  <SecureTextField
    name={fieldName}
    size="small"
    displayName={labelName}
    inputprops={getMaxLength(fieldName)}
    validators={validators}
    errorMessages={errorMessages}
    onChange={e => onChange(formater ? formater(e.target.value) : defaultFormatter(e.target.value))}
    onBlur={e => onChange(e.target.value.trim())}
    value={value || ''}
  />
);
export default function ServiPagTable(props) {
  const { data, onCreate, onUpdate, onDelete, onSubmit, enabledButton, userRole } = props;
  const [disabledAddBtn, setDisabledAddBtn] = useState(enabledButton);
  const hasWritePermission = checkWritePermission(userRole);

  useEffect(() => {
    setDisabledAddBtn(enabledButton);
  }, [enabledButton]);

  const formRef = useRef(null);
  const codeComponent = editableFn(
    'code',
    'Código',
    codeValidator,
    codeErrorMessage,
    codeFormatter
  );
  const nameComponent = editableFn('name', 'Nombre');
  const addressComponent = editableFn('address', 'Dirección');
  const cityComponent = editableFn('city', 'Ciudad');

  return (
    <ValidatorForm onSubmit={onSubmit} ref={formRef} instantValidate>
      <MaterialTable
        components={{
          Action: p => {
            let { action } = p;
            if (typeof p.action === 'function') {
              action = p.action();
            }
            return (
              <Tooltip title={!action.disabled && !p.disabled ? action.tooltip : ''}>
                <IconButton
                  onClick={event => {
                    if (action.tooltip === 'Editar' || action.tooltip === 'Eliminar') {
                      setDisabledAddBtn(true);
                    }
                    action.onClick(event, p.data);
                  }}
                  color="inherit"
                  variant="contained"
                  style={{ textTransform: 'none' }}
                  size={p.size}
                  disabled={!hasWritePermission || action.disabled || p.disabled}
                >
                  {action.icon.render()}
                </IconButton>
              </Tooltip>
            );
          },
          Toolbar: p => {
            return (
              <Grid container justify="space-between" style={{ height: '0px' }}>
                <MTableToolbar {...p} actions={[]} />
                <Grid item style={{ height: '0px', marginTop: '-60px' }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                      setDisabledAddBtn(true);
                      p.actions[0].onClick();
                    }}
                    disabled={!hasWritePermission || disabledAddBtn}
                  >
                    Agregar Sucursal
                  </Button>
                </Grid>
              </Grid>
            );
          },
          EditRow: p => (
            <MTableEditRow
              {...p}
              onEditingCanceled={(mode, rowData) => {
                setDisabledAddBtn(false);
                return p.onEditingCanceled(mode, rowData);
              }}
              onEditingApproved={async (mode, newData, oldData) => {
                if (!isRowValid(newData)) {
                  formRef.current.submit();
                  return null;
                }

                return p.onEditingApproved(mode, newData, oldData);
              }}
            />
          )
        }}
        icons={tableIcons}
        columns={[
          {
            title: 'Código',
            field: 'code',
            defaultSort: 'asc',
            editComponent: codeComponent
          },
          {
            title: 'Nombre Sucursal',
            field: 'name',
            sorting: false,
            editComponent: nameComponent
          },
          {
            title: 'Dirección',
            field: 'address',
            sorting: false,
            editComponent: addressComponent
          },
          {
            title: 'Ciudad',
            field: 'city',
            sorting: false,
            editComponent: cityComponent
          }
        ]}
        data={data}
        options={{
          addRowPosition: 'last',
          thirdSortClick: false,
          paging: false,
          search: false,
          showTitle: false,
          actionsColumnIndex: -1,
          toolbar: true
        }}
        localization={{
          body: {
            emptyDataSourceMessage: 'No existen registros ingresados',
            deleteTooltip: 'Eliminar',
            editTooltip: 'Editar',
            editRow: {
              deleteText: '¿Está seguro que desea eliminar el elemento?',
              saveTooltip: 'Aceptar',
              cancelTooltip: 'Cancelar'
            }
          },
          header: {
            actions: 'Acciones'
          }
        }}
        editable={{
          onRowAdd: newData =>
            new Promise(async (resolve, reject) => {
              if (!isRowValid(newData)) {
                setDisabledAddBtn(false);

                return reject();
              }
              try {
                await onCreate({ ...newData, id: uuidv4() }, {});
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return resolve();
              }
            }),
          onRowUpdate: (newData, oldData) =>
            new Promise(async (resolve, reject) => {
              if (!isRowValid(newData)) {
                return reject();
              }
              try {
                await onUpdate(newData, oldData);
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return resolve();
              }
            }),
          onRowDelete: oldData =>
            new Promise(async (resolve, reject) => {
              try {
                await onDelete(oldData);
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return reject();
              }
            })
        }}
      />
    </ValidatorForm>
  );
}

ServiPagTable.propTypes = {
  data: PropTypes.oneOfType([PropTypes.array, PropTypes.func]).isRequired,
  onCreate: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  onSubmit: PropTypes.func
};

ServiPagTable.defaultProps = {
  onSubmit: () => {
    /*any*/
  }
};
