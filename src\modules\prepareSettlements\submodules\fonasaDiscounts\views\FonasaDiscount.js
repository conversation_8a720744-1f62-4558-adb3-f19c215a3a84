/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React, { useState, useEffect } from 'react';
import useOnlineStatus from '@rehooks/online-status';
import HeaderContent from 'components/HeaderContent';
import { Button, TextField, Tooltip, Grid, Fade } from '@material-ui/core';
import MuiAlert from '@material-ui/lab/Alert';
import { useSelector, useDispatch } from 'react-redux';
import { useProgress } from 'components';
import ImportButton from './ImportButton';
import useStyles from './styles';
import {
  setFilenameCleanErrors,
  setFileCleanErrors,
  setDisableFileUpload,
  setCleanJson
} from '../actions';
import UseDataProcessing from '../hooks/useDataProcessing';
import UseDataUpload from '../hooks/useDataUpload';
import {
  wasFonasaExecuted,
  getMonthYear,
  isInDaysLimitRange,
  baseMinimumPensionExecuted
} from '../services/fonasaDiscounts.service';
import { checkWritePermission } from '../../../../../utils/checkUserPermission';

const TITLE = 'Fonasa';
const IMPORT = 'Importar Archivo Préstamo de Salud';
const Alert = props => <MuiAlert elevation={6} variant="filled" {...props} />;
const FonasaDiscount = ({ onSuccess, onError, role }) => {
  const dispatch = useDispatch();
  const onlineStatus = useOnlineStatus();

  const enabledMonthlyData = 'Carga masiva';
  const unabledMonthlyData = 'La carga masiva del mes ya fue realizada';

  const fileUploadInput = React.createRef();
  const classes = useStyles();

  const [file, setFile] = useState({});
  const [filename, setFilename] = useState('');

  const enableUpload = useSelector(store => store.fonasaDiscounts.enableUpload);
  const filenameErrors = useSelector(store => store.fonasaDiscounts.filenameErrors);
  const fileErrors = useSelector(store => store.fonasaDiscounts.fileErrors);
  const data = useSelector(store => store.fonasaDiscounts.file);
  const wasFonasaProcessExecuted = useSelector(
    store => store.fonasaDiscounts.wasFonasaProcessExecuted
  );
  const isInDaysRangeLimit = useSelector(store => store.fonasaDiscounts.isInDaysLimitRange);
  const isInNumberDaysRangeLimit = useSelector(store => store.fonasaDiscounts.isInNumberDaysLimitRange);
  const isUploading = useSelector(store => store.fonasaDiscounts.isUploading);
  const currentDate = useSelector(store => store.fonasaDiscounts.currentDate);
  const cronBaseMinimumPensionExecuted = useSelector(
    store => store.fonasaDiscounts.cronBaseMinimumPensionExecuted
  );

  const hasWritePermission = checkWritePermission(role);

  const actionIsNotAllowed = () =>
    !hasWritePermission ||
    !onlineStatus ||
    wasFonasaProcessExecuted ||
    isUploading ||
    !isInDaysRangeLimit ||
    !cronBaseMinimumPensionExecuted;

  const handleUploadButton = () => {
    fileUploadInput.current.value = '';
    fileUploadInput.current.click();
  };

  useEffect(() => {
    getMonthYear(dispatch);
    wasFonasaExecuted(dispatch);
    dispatch(isInDaysLimitRange());
    dispatch(baseMinimumPensionExecuted());
    dispatch(setFilenameCleanErrors());
    dispatch(setFileCleanErrors());
    dispatch(setDisableFileUpload());
    dispatch(setCleanJson());

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  useEffect(() => {
    if (filename) {
      dispatch(setFilenameCleanErrors());
      dispatch(setFileCleanErrors());
      dispatch(setDisableFileUpload());
      UseDataProcessing(filename, file, currentDate, dispatch);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [file]);

  const handleDataUpload = UseDataUpload(dispatch, progress, onSuccess, onError, data);

  const onChange = e => {
    setFile(e.target.files[0]);
    setFilename(e.target.files[0].name);
    document.getElementById('fullFilename').value = e.target.files[0].name;
  };

    const renderAlert = () => {
    if(!wasFonasaProcessExecuted && isInDaysRangeLimit && cronBaseMinimumPensionExecuted ) return null;
    const message = msjAlert();    
    return (
        <div>
          <Fade in timeout={{ enter: 1000 }}>
            <Alert severity='error' variant='outlined'>
              {message}
            </Alert>
          </Fade>
        </div>
    );
  }; 

  const msjAlert = () => {
    if(wasFonasaProcessExecuted)
      return 'Ya se realizo la carga del mes.';      
    else if(!isInDaysRangeLimit)      
      return `No se puede realizar la carga, han pasado los primeros ${isInNumberDaysRangeLimit} días hábiles del mes permitidos.` ;    
    else if(!cronBaseMinimumPensionExecuted)      
      return 'No se puede realizar la carga, no se ha ejecutado cron CRON_BASE_MINIMUN_PENSION_WORKER.';      
    else
      return '';
  };

  return (
    <>
      {renderAlert()}
      <Grid item>
        <HeaderContent title={TITLE} />
      </Grid>

      <Grid item>
        <TextField
          label={currentDate ? IMPORT : ''}
          className={!enableUpload ? classes.textfield : classes.textfieldLoaded}
          value={currentDate ? `Préstamos_Salud_${currentDate}.txt` : IMPORT}
          helperText={
            (filenameErrors.length > 0 && filenameErrors[0]) ||
            (fileErrors.length > 0 && fileErrors[0])
          }
          error={filenameErrors.length > 0 || fileErrors.length > 0}
          disabled
          id="fullFilename"
          variant="outlined"
          size="small"
        />
        <ImportButton
          classes={{ formControl: classes.formControl }}
          onClick={handleUploadButton}
          disabled={actionIsNotAllowed()}
          footer=""
          text={actionIsNotAllowed() ? '' : 'Cargar archivo'}
        />
        <input
          ref={fileUploadInput}
          type="file"
          style={{ display: 'none' }}
          id="fileUpload"
          disabled={actionIsNotAllowed()}
          onChange={onChange}
          accept=".txt"
        />
      </Grid>
      <Grid>
        <Tooltip
          className={classes.tooltip}
          title={wasFonasaProcessExecuted ? unabledMonthlyData : enabledMonthlyData}
        >
          <span className={classes.spanFinalize}>
            <Button
              variant="contained"
              className={classes.finalizar}
              color="primary"
              disabled={!enableUpload || actionIsNotAllowed()}
              onClick={handleDataUpload}
              size="medium"
            >
              FINALIZAR
            </Button>
          </span>
        </Tooltip>
      </Grid>
    </>
  );
};

export default FonasaDiscount;
