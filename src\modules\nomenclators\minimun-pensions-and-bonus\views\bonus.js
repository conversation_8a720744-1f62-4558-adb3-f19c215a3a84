/* eslint-disable no-underscore-dangle */

import React from 'react';
import PropTypes from 'prop-types';
import { Grid, FormLabel } from '@material-ui/core';
import InputTextField from './textField';

const formatNumber = number =>
  number ? `$ ${new Intl.NumberFormat(['es', 'de']).format(number)}` : '';
const Bonuses = ({ values, setState, hasError, setHasError, isEditable, bonusClass }) => {
  const winterBonus = values.find(obj => {
    return obj.label === 'Bono Invierno';
  });
  const christmasChargeBonus = values.find(obj => {
    return obj.label === 'Aguinaldo navidad por carga familiar';
  });
  const holidayChargeBonus = values.find(obj => {
    return obj.label === 'Aguinaldo fiestas patrias por carga familiar';
  });
  const holidayBonus = values.find(obj => {
    return obj.label === 'Aguinaldo de fiestas patrias';
  });
  const christmasBonus = values.find(obj => {
    return obj.label === 'Aguinaldo de navidad';
  });

  return (
    <>
      <Grid item xs={4} className={bonusClass}>
        {isEditable ? (
          <InputTextField
            id={winterBonus._id}
            name="winter-bonus"
            valueType={winterBonus.type}
            label={winterBonus.label}
            value={winterBonus.value || ''}
            handleError={setHasError}
            handleOnChange={setState}
          />
        ) : (
          <FormLabel>
            {`Bono Invierno: ${formatNumber(winterBonus ? winterBonus.value : '')}`}
          </FormLabel>
        )}
      </Grid>
      <Grid item xs={4} className={bonusClass}>
        {isEditable ? (
          <InputTextField
            id={holidayBonus._id}
            name="holiday-bonus"
            valueType={winterBonus.type}
            label={holidayBonus.label}
            value={holidayBonus.value}
            handleError={setHasError}
            handleOnChange={setState}
          />
        ) : (
          <FormLabel>
            {`Aguinaldo de fiestas patrias: ${formatNumber(
              holidayBonus ? holidayBonus.value : ''
            )}`}
          </FormLabel>
        )}
      </Grid>
      <Grid item xs={4} className={bonusClass}>
        {isEditable ? (
          <InputTextField
            id={christmasBonus._id}
            name="christmas-bonus"
            valueType={winterBonus.type}
            label={christmasBonus.label}
            value={christmasBonus.value}
            handleError={setHasError}
            handleOnChange={setState}
          />
        ) : (
          <FormLabel>
            {`Aguinaldo de navidad: ${formatNumber(christmasBonus ? christmasBonus.value : '')}`}
          </FormLabel>
        )}
      </Grid>
      <Grid item xs={4} className={bonusClass}></Grid>
      <Grid item xs={4} className={bonusClass}>
        {isEditable ? (
          <InputTextField
            id={holidayChargeBonus._id}
            name="holidayCharge-bonus"
            valueType={holidayChargeBonus.type}
            label={holidayChargeBonus.label}
            value={holidayChargeBonus.value}
            handleError={setHasError}
            handleOnChange={setState}
          />
        ) : (
          <FormLabel>
            {`Aguinaldo de fiestas patrias por carga: ${formatNumber(
              holidayChargeBonus ? holidayChargeBonus.value : ''
            )}`}
          </FormLabel>
        )}
      </Grid>
      <Grid item xs={4} className={bonusClass}>
        {isEditable ? (
          <InputTextField
            id={christmasChargeBonus._id}
            name="christmasCharge-bonus"
            valueType={christmasChargeBonus.type}
            label={christmasChargeBonus.label}
            value={christmasChargeBonus.value}
            handleError={setHasError}
            handleOnChange={setState}
          />
        ) : (
          <FormLabel>
            {`Aguinaldo de navidad por carga: ${formatNumber(
              christmasChargeBonus ? christmasChargeBonus.value : ''
            )}`}
          </FormLabel>
        )}
      </Grid>
    </>
  );
};

Bonuses.propTypes = {
  values: PropTypes.arrayOf(PropTypes.object).isRequired,
  setState: PropTypes.func.isRequired,
  hasError: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]).isRequired,
  setHasError: PropTypes.func.isRequired,
  isEditable: PropTypes.bool.isRequired,
  bonusClass: PropTypes.string
};

Bonuses.defaultProps = {
  bonusClass: ''
};

export default Bonuses;
