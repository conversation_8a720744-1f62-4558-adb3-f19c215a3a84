/* eslint-disable import/no-unresolved */
import { useState } from 'react';
import { useProgress } from 'components';
import {
  getSpecificPensioner,
  getSpecificPensionerTemmporalTable,
  businessDaysToUpdateTemporally,
  getSpecificPensionerTemporalPensionType
} from '../services/queryPensions.service';
import formatToRender from '../utils/formattersDataTab';

const UseFetchTabGeneralData = onErrorSnackbar => {
  const [settlementData, setSettlementData] = useState({});
  const [isSuccess, setSuccess] = useState(false);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const fetch = async queryData => {
    progress.show();
    const {
      updateTemporally,
      updatePensionTypeToTemporally
    } = await businessDaysToUpdateTemporally();

    const { data, isError } = await getSpecificPensioner(queryData);

    const { data: temporalData, isError: isTemporalError } = updateTemporally
      ? await getSpecificPensionerTemmporalTable(queryData)
      : {};

    const {
      data: temporalDataPensionType,
      isError: isTemporalPensionTypeError
    } = updatePensionTypeToTemporally
      ? await getSpecificPensionerTemporalPensionType(queryData)
      : {};

    if (isTemporalPensionTypeError || isTemporalError || isError || !Object.keys(data).length) {
      progress.hide();
      onErrorSnackbar();
      return;
    }

    try {
      const formattedData = formatToRender({
        ...data,
        ...temporalData,
        ...temporalDataPensionType
      });
      setSettlementData(formattedData);
      progress.hide();
      setSuccess(true);
    } catch (error) {
      console.log(error);

      progress.hide();
      onErrorSnackbar();
    }
  };
  return { fetch, settlementData, isSuccess, setSettlementData };
};

export default UseFetchTabGeneralData;
