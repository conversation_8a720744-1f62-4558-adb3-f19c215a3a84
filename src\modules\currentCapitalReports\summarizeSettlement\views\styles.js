import { makeStyles } from '@material-ui/styles';

const styles = makeStyles(theme => ({
  root: {
    // eslint-disable-next-line no-magic-numbers
    padding: theme.spacing(3)
  },
  alertBar: {
    fontWeight: 'bold',
    color: 'gray'
  },
  calendar: {
    marginTop: 20,
    marginLeft: 0,
    maxWidth: 200,
    minWidth: 200
  },
  export: {
    marginTop: -40,
    marginBottom: 20,
    backgroundColor: '#7EBD41',
    fontWeight: 'bold'
  },
  importText: {
    fontSize: 10,
    fontWeight: '1',
    marginLeft: 10,
    marginBotton: 20,
    color: 'Gray'
  },
  subtitle: {
    fontSize: 11,
    fontWeight: 15,
    marginBottom: -10,
    marginTop: 20
  },
  textfield: {
    width: '33%',
    marginRight: 10,
    minWidth: 195,
    maxWidth: 195,
    marginBottom: 0,
    marginTop: 0
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 20
  },
  tooltip: {
    fontSize: 11,
    marginTop: 50
  }
}));

export default styles;
