import { makeStyles } from '@material-ui/styles';

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(3)
  },
  Button: {
    fontWeight: 'bold',
    color: 'white',
    backgroundColor: 'green'
  },
  calendar: {
    marginTop: 20,
    marginLeft: 0,
    maxWidth: 200,
    minWidth: 200
  },
  export: {
    marginTop: -40,
    marginBottom: 20,
    backgroundColor: '#7EBD41',
    fontWeight: 'bold'
  },
  importText: {
    fontSize: 10,
    fontWeight: '1',
    marginLeft: 10,
    marginBotton: 20,
    color: 'Gray'
  },
  subtitle: {
    fontSize: 11,
    fontWeight: 15,
    marginBottom: -10,
    marginTop: 20
  },
  textfield: {
    width: '33%',
    marginRight: 20,
    minWidth: 220,
    maxWidth: 220,
    marginLeft: 10
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 20
  },
  tooltip: {
    fontSize: 11,
    marginTop: 50
  },
  info: {
    padding: theme.spacing(1),
    marginBottom: 0
  },
  infoTitle: {
    padding: theme.spacing(1),
    fontWeight: 'bold',
    marginBottom: 30
  },
  cardContainer: {
    marginBottom: 15
  },
  gridStyle: {
    flexDirection: 'row',
    '@mediaQuery(minWidth: 600px)': {
      flexDirection: 'column'
    }
  },
  editSave: {
    marginTop: -15,
    marginBottom: 15
  },
  selectField: {
    width: '250px'
  }
}));

export default useStyles;
