/* eslint-disable no-console */
/* eslint-disable import/prefer-default-export */
import {
  deleteNomenclatorMotive,
  updateNomenclatorMotive,
  loadNomenclatorMotive,
  createNomenclatorMotive
} from '../actions';

//-----------------------------------------------------------------------------------------------------------
export const processMotive = (prevMotive, newMotive, operation) => async (
  dispatch,
  _getState,
  { api, axiosRequest }
) => {
  let createdMotive = newMotive;
  if (operation === 'create') {
    const {
      data: { result, error, isError }
    } = await createMotive({ axiosRequest, api, motive: createdMotive });
    if (isError) {
      throw new Error(error.code);
    }
    createdMotive = result;

    await dispatch(createNomenclatorMotive(createdMotive));
  }
  if (operation === 'delete') {
    await deleteMotive({ axiosRequest, api, motive: createdMotive });
    await dispatch(deleteNomenclatorMotive(createdMotive));
  }
  if (operation === 'update') {
    const {
      data: { error, isError }
    } = await updateMotive({ axiosRequest, api, motive: createdMotive });
    if (isError) {
      throw new Error(error.code);
    }

    await dispatch(updateNomenclatorMotive(prevMotive, createdMotive));
  }
};
const createMotive = async ({ axiosRequest, api, motive }) =>
  axiosRequest.post(`${api}/nomenclators/motive`, { motive });

const updateMotive = async ({ axiosRequest, api, motive }) =>
  axiosRequest.put(`${api}/nomenclators/motive`, { motive });

const deleteMotive = async ({ axiosRequest, api, motive }) => {
  const { _id: id } = motive;
  return axiosRequest.delete(`${api}/nomenclators/motive/${id}`);
};

//-----------------------------------------------------------------------------------------------------------
export const loadMotive = () => async (dispatch, _getState, { api, axiosRequest }) => {
  const {
    data: { result: motives }
  } = await axiosRequest.get(`${api}/nomenclators/motive`).catch(err => {
    console.error(err);
    return { data: { result: [] } };
  });
  await dispatch(loadNomenclatorMotive(motives));
  return motives;
};
