import React, { forwardRef, useState, useEffect } from 'react';
import MaterialTable, { MTableEditRow } from 'material-table';
import { Tooltip, IconButton } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import PropTypes from 'prop-types';
import { ArrowDownward, Clear, Check  } from '@material-ui/icons';
import SlowMotionVideoIcon from '@material-ui/icons/SlowMotionVideo';
import SkipNextIcon from '@material-ui/icons/SkipNext';
import styles from './styles';
import { checkWritePermission } from '../../../../utils/checkUserPermission';

const tableIcons = {
  Check: forwardRef((props, ref) => <Check {...props} ref={ref} />),
  Edit: forwardRef((props, ref) => <SlowMotionVideoIcon {...props} ref={ref} />),
  Delete: forwardRef((props, ref) => <SkipNextIcon {...props} ref={ref} />),
  Clear: forwardRef((props, ref) => <Clear {...props} ref={ref} />),
  SortArrow: forwardRef((props, ref) => <ArrowDownward {...props} ref={ref} />)
};

const MonitorTable = props => {
  const {
    data,
    onExecuteJob,
    onSkipJob,
    enabledButton,
    selectedRole,
    userRole
  } = props;

  const classes = styles();
  const onlineStatus = useOnlineStatus();

  const [disabledAddBtn, setDisabledAddBtn] = useState(enabledButton);
  const [displayedData, setDisplayedData] = useState(data);

  const hasWritePermission = checkWritePermission(userRole);

  useEffect(() => {
    if (!selectedRole) {
      return setDisplayedData(data);
    }
    const filteredData = data.filter(({ role }) => role === selectedRole);
    return setDisplayedData(filteredData);
  }, [selectedRole, data]);

  useEffect(() => {
    setDisabledAddBtn(enabledButton);
  }, [enabledButton]);

  return (
      <MaterialTable
        data={displayedData}
        options={{
          addRowPosition: 'last',
          thirdSortClick: false,
          paging: false,
          search: false,
          showTitle: false,
          actionsColumnIndex: -1,
          toolbar: true
        }}
        localization={{
          body: {
            emptyDataSourceMessage: 'No existen registros ingresados',
            editTooltip: 'Ejecutar',
            deleteTooltip: 'Saltar',
            editRow: {
              deleteText: '¿Está seguro que desea saltar trabajo?',
              saveTooltip: 'Aceptar',
              cancelTooltip: 'Cancelar'
            },
          },
          header: {
            actions: 'Acciones'
          }
        }}
        columns={[
          {
            title: 'Proceso',
            field: 'description',
            sorting: false,
            editComponent: () => (
              <lable>¿Está seguro que desea ejecutar trabajo?</lable>
            ),
            cellStyle: {
              width: 20,
              maxWidth: 20
            },
            headerStyle: {
              width: 20,
              maxWidth: 20
            }                               
          },
          {
            title: 'Cron',
            field: 'brandExecution',
            sorting: false,
            editComponent: () => (
              <lable></lable>
            ),
            cellStyle: {
              width: 40,
              maxWidth: 40
            },
            headerStyle: {
              width: 40,
              maxWidth: 40
            }                  
          },
          {
            title: 'Estado',
            sorting: false,
            field: 'status',
            editComponent: () => (
              <lable></lable>
            ),
            cellStyle: {
              width: 10,
              maxWidth: 10
            },
            headerStyle: {
              width: 10,
              maxWidth: 10
            }                
          },
          {
            title: 'Dependencia/Error',
            field: 'cronDependency',
            sorting: false,
            editComponent: () => (
              <lable></lable>
            ),
            cellStyle: {
              width: 20,
              maxWidth: 20
            },
            headerStyle: {
              width: 20,
              maxWidth: 20
            }
          }                 
        ]}
        icons={tableIcons}
        components={{
          Action: p => {
            let { action } = p;
            if (typeof p.action === 'function') {
              action = p.action();
            }
            
            return (
              <Tooltip title={!action.disabled && !p.disabled ? action.tooltip : ''}>
                <IconButton
                  onClick={event => {
                    action.onClick(event, p.data);
                  }}
                  color="inherit"
                  variant="contained"
                  className={classes.iconButtonStyle}
                  size={p.size}
                  disabled={!hasWritePermission || action.disabled  || !p.data.runFromWeb || disabledAddBtn }
                >
                  {action.icon.render()}
                </IconButton>
              </Tooltip>
            );
          },
          EditRow: p => {
            return (              
                <MTableEditRow
                  {...p}                  
                  onEditingCanceled={(mode, rowData) => {
                    setDisabledAddBtn(false);
                    return p.onEditingCanceled(mode, rowData);
                  }}
                  onEditingApproved={async (mode, newData, oldData) => {
                    setDisabledAddBtn(true);
                    if(mode === "delete")
                      await onSkipJob(newData, oldData) 
                    else
                      await onExecuteJob(newData, oldData); 
                    return p.onEditingApproved(mode, newData, oldData);
                  }}
                  disabled={disabledAddBtn || !onlineStatus || !p.data.runFromWeb} 
                />
            );
          }
        }}
        editable={{
          onRowUpdate: (_newData, _oldData) =>
            new Promise(async (resolve, _reject) => {
              try {
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return resolve();
              }
            }), 
          onRowDelete: (newData, oldData) =>
            new Promise(async (resolve, reject) => {
              try {
                await onSkipJob(newData, oldData);                
                setDisabledAddBtn(false);
                return resolve();
              } catch (error) {
                setDisabledAddBtn(false);
                return reject();
              }
            })
        }} 
      />
  );
};

MonitorTable.propTypes = {
  data: PropTypes.oneOfType([PropTypes.array, PropTypes.func]).isRequired,
  onExecuteJob: PropTypes.func.isRequired,
  onSkipJob: PropTypes.func.isRequired
};

export default MonitorTable;
