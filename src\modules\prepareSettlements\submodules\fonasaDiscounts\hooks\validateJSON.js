const checkDigitValidation = rutWithDV => {
  const rut = rutWithDV.split('-')[0];
  const currentDV = rutWithDV.split('-')[1];
  const module = 11;
  const multipliers = '234567'.split('').map(x => +x);
  const sumOfElements = rut
    .replace(/[^\d]/g, '')
    .split('')
    .reverse()
    .map((value, index) => +value * multipliers[index % multipliers.length])
    .reduce((accumulator, currentValue) => accumulator + currentValue);
  let actualDV = module - (sumOfElements % module);
  if (actualDV === module - 1) {
    actualDV = 'k';
    return actualDV === currentDV.toLowerCase();
  }
  if (actualDV === module) actualDV = 0;
  return actualDV === +currentDV;
};

const areValidDiscounts = data => data.map(x => !isNaN(+x.discount)).reduce((a, i) => a && i, true);
const areValidRuts = data =>
  data.map(x => checkDigitValidation(x.rut)).reduce((a, i) => a && i, true);

const validateJSON = data => {
  return new Promise((resolve, reject) => {
    if (data.length && areValidDiscounts(data) && areValidRuts(data)) resolve();
    reject(new Error('Contenido Invalido'));
  });
};

export default validateJSON;
