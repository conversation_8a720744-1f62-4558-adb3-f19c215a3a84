/* eslint-disable no-underscore-dangle */
import { TextField } from '@material-ui/core';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import validator from 'validator';
import numbUtils from '../../../../utils/numberOperations';

const MIN_NUMBER_ALLOWED = 1;
const MAX_NUMBER_ALLOWED = 1000000;

const isValidInt = val =>
  validator.isInt(`${val}`, {
    min: MIN_NUMBER_ALLOWED,
    max: MAX_NUMBER_ALLOWED,
    allow_leading_zeroes: false
  });

const isValidDecimal = val => {
  const stringValue = `${val}`;

  if (!validator.isDecimal(stringValue, { decimal_digits: '0,2', locale: 'es-ES' })) return false;

  const fixedValue = numbUtils.roundValue(stringValue.replace(',', '.'));
  if (fixedValue < MIN_NUMBER_ALLOWED || fixedValue > MAX_NUMBER_ALLOWED) return false;

  return true;
};

const INVALID_VALUE = 'Valor inválido';
const REQUIRED_VALUE = 'El campo es obligatorio';

const isEmptyOrInvalidValue = (hasError, value) => {
  let text = '';

  if (hasError) {
    text = INVALID_VALUE;
  }
  if (!value) {
    text = REQUIRED_VALUE;
  }

  return text;
};

const hasInputError = (valueType, targetVal) => {
  let isInvalid = false;

  if (!targetVal) {
    isInvalid = true;
  }

  if (
    (valueType !== 'pension' && targetVal && !isValidInt(targetVal)) ||
    (valueType === 'pension' && targetVal && !isValidDecimal(targetVal)) ||
    // eslint-disable-next-line no-magic-numbers
    (valueType === 'bonus' && (targetVal < 0 || targetVal > 99999))
  ) {
    isInvalid = true;
  }

  return isInvalid;
};

const handleOnErrorValues = (valueType, old, valueIsObject, id, name, value, setHasError) => {
  const isInvalid = hasInputError(valueType, value);
  setHasError(isInvalid);
  const replaced = valueIsObject
    ? { [id]: { ...old[id], [name]: isInvalid } }
    : { [id]: isInvalid };

  return { ...old, ...replaced };
};
const handleOnChangeValues = (valueType, old, valueIsObject, id, name, value) => {
  return old.map(v => {
    let replaced = { ...v };
    if (v._id === id) {
      replaced = valueIsObject
        ? { ...v, value: { ...v.value, [name]: value || '' } }
        : { ...v, value: value || '' };
    }
    return replaced;
  });
};

const InputTextField = ({
  id,
  name,
  value,
  valueType,
  label,
  handleError,
  handleOnChange,
  fullWidth,
  variant,
  size,
  type,
  isRequired,
  valueIsObject
}) => {
  const [hasError, setHasError] = useState(false);
  useEffect(() => {
    if (isRequired) {
      handleError(old =>
        handleOnErrorValues(valueType, old, valueIsObject, id, name, value, setHasError)
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <TextField
      id={`${id}-${name}`}
      label={label}
      helperText={isEmptyOrInvalidValue(hasError, value)}
      type={type}
      value={value || ''}
      variant={variant}
      size={size}
      required
      error={hasError}
      fullWidth={fullWidth}
      autoComplete="off"
      onChange={({ target }) => {
        if (
          !target.value ||
          (valueType !== 'pension' && isValidInt(target.value)) ||
          (valueType === 'pension' && isValidDecimal(target.value))
        ) {
          handleError(old =>
            handleOnErrorValues(valueType, old, valueIsObject, id, name, target.value, setHasError)
          );
          handleOnChange(old =>
            handleOnChangeValues(valueType, old, valueIsObject, id, name, target.value)
          );
        }
      }}
    />
  );
};

InputTextField.propTypes = {
  id: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  value: PropTypes.string,
  valueType: PropTypes.string,
  label: PropTypes.string,
  handleOnChange: PropTypes.func.isRequired,
  handleError: PropTypes.func.isRequired,
  fullWidth: PropTypes.bool,
  type: PropTypes.string,
  variant: PropTypes.string,
  size: PropTypes.string,
  valueIsObject: PropTypes.bool,
  isRequired: PropTypes.bool
};

InputTextField.defaultProps = {
  value: '',
  valueType: '',
  label: null,
  fullWidth: true,
  type: 'text',
  variant: 'outlined',
  size: 'small',
  valueIsObject: false,
  isRequired: true
};

export default InputTextField;
