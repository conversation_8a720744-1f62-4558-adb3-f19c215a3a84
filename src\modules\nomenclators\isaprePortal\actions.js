//------------------------------------------------------------------------------------------------
export const SET_ISAPRE_PORTAL_FILE_ERROR = 'SET_ISAPRE_PORTAL_FILE_ERROR';
export const setFileError = error => {
  return {
    type: SET_ISAPRE_PORTAL_FILE_ERROR,
    data: { fileError: error }
  };
};
//------------------------------------------------------------------------------------------------
export const CLEAN_ISAPRE_PORTAL_FILE_ERROR = 'CLEAN_ISAPRE_PORTAL_FILE_ERROR';
export const cleanFileError = () => {
  return {
    type: CLEAN_ISAPRE_PORTAL_FILE_ERROR
  };
};
//------------------------------------------------------------------------------------------------
export const SET_ISAPRE_PORTAL_RESUME_ERROR = 'SET_ISAPRE_PORTAL_RESUME_ERROR';
export const updateFileDataError = ({ warnings, errors }) => {
  return {
    type: SET_ISAPRE_PORTAL_RESUME_ERROR,
    data: { errors, warnings, isError: errors.length > 0 }
  };
};
//------------------------------------------------------------------------------------------------
export const CLEAN_ISAPRE_PORTAL_RESUME_ERROR = 'CLEAN_ISAPRE_PORTAL_RESUME_ERROR';
export const cleanIsaprePortalResumeErrors = _results => {
  return {
    type: CLEAN_ISAPRE_PORTAL_RESUME_ERROR
  };
};
//------------------------------------------------------------------------------------------------
export const ENABLED_ISAPRE_PORTAL = 'ENABLED_ISAPRE_PORTAL';
export const enableIsaprePortal = isEnabled => {
  return {
    type: ENABLED_ISAPRE_PORTAL,
    data: { isIsaprePortalEnable: isEnabled }
  };
};
//------------------------------------------------------------------------------------------------
export const ENABLED_ISAPRE_PORTAL_IMPORT = 'ENABLED_ISAPRE_PORTAL_IMPORT';
export const enableIsaprePortalImport = (isEnabled, completed) => {
  return {
    type: ENABLED_ISAPRE_PORTAL_IMPORT,
    data: { isIsaprePortalImport: isEnabled && completed }
  };
};
//------------------------------------------------------------------------------------------------
export const ISAPRE_PORTAL_PROCESS_AVAILABLE = 'ISAPRE_PORTAL_PROCESS_AVAILABLE';
export const isIsaprePortalAvailable = isAvailable => {
  return {
    type: ISAPRE_PORTAL_PROCESS_AVAILABLE,
    data: isAvailable
  };
};

//------------------------------------------------------------------------------------------------
export const DAYS_TO_EXECUTE_ISAPRE_PORTAL_PROCESS = 'DAYS_TO_EXECUTE_ISAPRE_PORTAL_PROCESS';
export const numberOfDaysToExecute = isInRange => {
  return {
    type: DAYS_TO_EXECUTE_ISAPRE_PORTAL_PROCESS,
    data: isInRange
  };
};
//------------------------------------------------------------------------------------------------
export const WAS_API_CALLED = 'WAS_API_CALLED';
export const apiCalled = wasCalled => {
  return {
    type: WAS_API_CALLED,
    data: wasCalled
  };
};
