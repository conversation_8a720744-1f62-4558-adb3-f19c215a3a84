/* eslint-disable import/no-unresolved */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import useOnlineStatus from '@rehooks/online-status';
import { Button, TextField, Grid, Tooltip, Fade } from '@material-ui/core';
import MuiAlert from '@material-ui/lab/Alert';
import HeaderContent from 'components/HeaderContent';
import useRouter from 'utils/useRouter';
import { useSelector, useDispatch } from 'react-redux';
import { useProgress } from 'components';
import ImportButton from '../../../components/ImportButton';
import {
  useDataLoadMemberships,
  useDataLoadCredits,
  useDataLoadAnotherDiscounts,
  useDataUpload
} from '../hooks';
import {
  getCurrentMonthYear,
  wasLosAndesExecuted,
  isInDaysLimitRange,
  baseMinimumPensionExecuted
} from '../services/cajaLosAndesDiscounts.service';

import Snackbar from '@material-ui/core/Snackbar';
import useStyles from './styles';
import { cleanFileMemberships, 
  cleanFileCredits, 
  cleanFileAnotherDiscounts,
  setSnackbarMessageError,
  setOpenSnackbarError } from '../actions';
import { checkWritePermission } from '../../../../../utils/checkUserPermission';
const Alert = props => <MuiAlert elevation={6} variant="filled" {...props} />;
const EXTENSION = '.txt';

const CajaLosAndesDiscountsPage = ({ onSuccess, onError, role }) => {
  const onlineStatus = useOnlineStatus();
  const fileInputMemberships = React.createRef();
  const fileInputCredits = React.createRef();
  const fileInputAnotherDiscounts = React.createRef();
  const classes = useStyles();
  const router = useRouter();
  const dispatch = useDispatch();

  const [acceptedFilenameRegexMemberships, setAcceptedFilenameRegexMemberships] = useState();
  const [acceptedFilenameRegexCredits, setAcceptedFilenameRegexCredits] = useState();
  const [
    acceptedFilenameRegexAnotherDiscounts,
    setAcceptedFilenameRegexAnotherDiscounts
  ] = useState();
  const [acceptedFilenameMemberships, setAcceptedFilenameMemberships] = useState('');
  const [acceptedFilenameCredits, setAcceptedFilenameCredits] = useState('');
  const [acceptedFilenameAnotherDiscounts, setAcceptedFilenameAnotherDiscounts] = useState('');

  const isInProgress = useSelector(store => store.progress.isInProgress);
  const currentMonthYear = useSelector(store => store.cajaLosAndesDiscounts.currentMonthYear);
    
  const messageError = useSelector(store => store.cajaLosAndesDiscounts.messageError);
  const openSnackbarError = useSelector(store => store.cajaLosAndesDiscounts.openSnackbarError);

  const wasLosAndesProcessExecuted = useSelector(
    store => store.cajaLosAndesDiscounts.wasLosAndesProcessExecuted
  );
  const isInDaysRangeLimit = useSelector(store => store.cajaLosAndesDiscounts.isInDaysLimitRange);
  const isInNumberDaysRangeLimit = useSelector(store => store.cajaLosAndesDiscounts.isInNumberDaysLimitRange);
  const cronBaseMinimumPensionExecuted = useSelector(
    store => store.cajaLosAndesDiscounts.cronBaseMinimumPensionExecuted
  );
  const fileMembershipsErrors = useSelector(
    store => store.cajaLosAndesDiscounts.fileMembershipsErrors
  );
  const fileCreditsErrors = useSelector(store => store.cajaLosAndesDiscounts.fileCreditsErrors);
  const fileAnotherDiscountsErrors = useSelector(
    store => store.cajaLosAndesDiscounts.fileAnotherDiscountsErrors
  );
  const fileMembershipsName = useSelector(store => store.cajaLosAndesDiscounts.fileMembershipsName);
  const fileCreditsName = useSelector(store => store.cajaLosAndesDiscounts.fileCreditsName);
  const fileAnotherDiscountsName = useSelector(
    store => store.cajaLosAndesDiscounts.fileAnotherDiscountsName
  );
  const fileMembershipsData = useSelector(store => store.cajaLosAndesDiscounts.fileMembershipsData);
  const fileCreditsData = useSelector(store => store.cajaLosAndesDiscounts.fileCreditsData);
  const fileAnotherDiscountsData = useSelector(
    store => store.cajaLosAndesDiscounts.fileAnotherDiscountsData
  );

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const handleFileLoadMemberships = useDataLoadMemberships(
    dispatch,
    acceptedFilenameRegexMemberships,
    acceptedFilenameMemberships
  );
  const handleFileLoadCredits = useDataLoadCredits(
    dispatch,
    acceptedFilenameRegexCredits,
    acceptedFilenameCredits
  );
  const handleFileLoadAnotherDiscounts = useDataLoadAnotherDiscounts(
    dispatch,
    acceptedFilenameRegexAnotherDiscounts,
    acceptedFilenameAnotherDiscounts
  );

  const setOpenSnackbar = () => {
    dispatch(setSnackbarMessageError(''));
    dispatch(setOpenSnackbarError(false));
	}; 

  const hasWritePermission = checkWritePermission(role);

  useEffect(() => {
    progress.show();
    dispatch(cleanFileMemberships());
    dispatch(cleanFileCredits());
    dispatch(cleanFileAnotherDiscounts());
    dispatch(getCurrentMonthYear());
    dispatch(isInDaysLimitRange());
    dispatch(baseMinimumPensionExecuted());
    wasLosAndesExecuted(dispatch);
    progress.hide();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (currentMonthYear) {
      setAcceptedFilenameRegexMemberships(
        new RegExp(`afiliaciones_losandes_${currentMonthYear}`, 'i')
      );
      setAcceptedFilenameRegexCredits(
        new RegExp(`cr(e|é|e\u0301)ditos_losandes_${currentMonthYear}`, 'i')
      );
      setAcceptedFilenameRegexAnotherDiscounts(
        new RegExp(`otrosdescuentos_losandes_${currentMonthYear}`, 'i')
      );
      setAcceptedFilenameMemberships(`Afiliaciones_LosAndes_${currentMonthYear}${EXTENSION}`);
      setAcceptedFilenameCredits(`Créditos_LosAndes_${currentMonthYear}${EXTENSION}`);
      setAcceptedFilenameAnotherDiscounts(
        `OtrosDescuentos_LosAndes_${currentMonthYear}${EXTENSION}`
      );
    }
  }, [currentMonthYear]);

 
  const handleDataUpload = useDataUpload(
    dispatch,
    router,
    progress,
    onSuccess,
    onError,
    fileMembershipsData,
    fileCreditsData,
    fileAnotherDiscountsData
  );

  const allFilesAreUploaded = () => {
    return (
      fileMembershipsData.length > 0 &&
      fileMembershipsErrors.length === 0 &&
      fileCreditsData.length > 0 &&
      fileCreditsErrors.length === 0 &&
      fileAnotherDiscountsData.length > 0 &&
      fileAnotherDiscountsErrors.length === 0
    );
  };

  const handleClickButtonMemberships = () => {
    fileInputMemberships.current.value = '';
    fileInputMemberships.current.click();
  };

  const handleClickButtonCredits = () => {
    fileInputCredits.current.value = '';
    fileInputCredits.current.click();
  };

  const handleClickButtonAnotherDiscounts = () => {
    fileInputAnotherDiscounts.current.value = '';
    fileInputAnotherDiscounts.current.click();
  };

  const renderAlert = () => {
    if(currentMonthYear && !wasLosAndesProcessExecuted && isInDaysRangeLimit && cronBaseMinimumPensionExecuted ) return null;
    const message = msjAlert();    
    return (
        <div>
          <Fade in timeout={{ enter: 1000 }}>
            <Alert severity='error' variant='outlined'>
              {message}
            </Alert>
          </Fade>
        </div>
    );
  }; 
  
  const msjAlert = () => {
    if(wasLosAndesProcessExecuted)
      return 'Ya se realizo la carga del mes';      
    else if(!isInDaysRangeLimit)      
      return `No se puede realizar la carga, han pasado los primeros ${isInNumberDaysRangeLimit} días hábiles del mes permitidos`;      
    else if(!cronBaseMinimumPensionExecuted)      
      return 'No se puede realizar la carga, no se ha ejecutado cron CRON_BASE_MINIMUN_PENSION_WORKER';      
    else
      return '';
  }

  const buttonIsDisabled = () =>
    !hasWritePermission ||
    !onlineStatus ||
    isInProgress ||
    !currentMonthYear ||
    wasLosAndesProcessExecuted ||
    !isInDaysRangeLimit ||
    !cronBaseMinimumPensionExecuted;

  return (
    <>
      {renderAlert()}
      <Snackbar open={openSnackbarError} autoHideDuration={5000} onClose={() => setOpenSnackbar()}>
        <Alert onClose={() => setOpenSnackbar()} severity='error'>          
          <div className={classes.tituloalert}>Solicitud no fue procesada:</div>
          <div className={classes.divalert} dangerouslySetInnerHTML={{ __html: messageError }} />
        </Alert>
      </Snackbar>
      <Grid>
        <HeaderContent title="Caja Los Andes" />
      </Grid>

      <Grid item>
        <TextField
          label="Importar archivo 1%"
          className={fileMembershipsName ? classes.textfieldLoaded : classes.textfield}
          value={fileMembershipsName || acceptedFilenameMemberships}
          helperText={fileMembershipsErrors.length > 0 && fileMembershipsErrors[0]}
          error={fileMembershipsErrors.length > 0}
          variant="outlined"
          size="small"
          disabled
        />
        <ImportButton
          classes={{ formControl: classes.formControl }}
          onClick={handleClickButtonMemberships}
          disabled={buttonIsDisabled()}
          footer=""
          text={buttonIsDisabled() ? '' : 'Cargar archivo'}
        />
        <input
          ref={fileInputMemberships}
          type="file"
          style={{ display: 'none' }}
          id="fileInputMemberships"
          disabled={buttonIsDisabled()}
          onChange={v => v.target.files.length && handleFileLoadMemberships(v.target.files[0])}
          accept={EXTENSION}
        />
      </Grid>
      <Grid item>
        <TextField
          className={fileCreditsName ? classes.textfieldLoaded : classes.textfield}
          label="Importar archivo de créditos sociales"
          value={fileCreditsName || acceptedFilenameCredits}
          helperText={fileCreditsErrors.length > 0 && fileCreditsErrors[0]}
          error={fileCreditsErrors.length > 0}
          variant="outlined"
          size="small"
          disabled
        />
        <ImportButton
          classes={{ formControl: classes.formControl }}
          onClick={handleClickButtonCredits}
          disabled={buttonIsDisabled()}
          footer=""
          text={buttonIsDisabled() ? '' : 'Cargar archivo'}
        />
        <input
          ref={fileInputCredits}
          type="file"
          style={{ display: 'none' }}
          id="fileInputCredits"
          disabled={buttonIsDisabled()}
          onChange={v => v.target.files.length && handleFileLoadCredits(v.target.files[0])}
          accept={EXTENSION}
        />
      </Grid>
      <Grid item>
        <TextField
          label="Importar archivo otros descuentos"
          className={fileAnotherDiscountsName ? classes.textfieldLoaded : classes.textfield}
          value={fileAnotherDiscountsName || acceptedFilenameAnotherDiscounts}
          helperText={fileAnotherDiscountsErrors.length > 0 && fileAnotherDiscountsErrors[0]}
          error={fileAnotherDiscountsErrors.length > 0}
          variant="outlined"
          size="small"
          disabled
        />
        <ImportButton
          classes={{ formControl: classes.formControl }}
          onClick={handleClickButtonAnotherDiscounts}
          disabled={buttonIsDisabled()}
          footer=""
          text={buttonIsDisabled() ? '' : 'Cargar archivo'}
        />
        <input
          ref={fileInputAnotherDiscounts}
          type="file"
          style={{ display: 'none' }}
          id="fileInputAnotherDiscounts"
          disabled={buttonIsDisabled()}
          onChange={v => v.target.files.length && handleFileLoadAnotherDiscounts(v.target.files[0])}
          accept={EXTENSION}
        />
      </Grid>
      <Grid>
        <Tooltip
          title={
            !wasLosAndesProcessExecuted
              ? 'Carga Masiva'
              : 'La carga masiva del mes ya fue realizada'
          }
          aria-label="Carga Masiva"
        >
          <span className={classes.spanFinalize}>
            <Button
              variant="contained"
              color="primary"
              className={classes.finish}
              disabled={buttonIsDisabled() || !allFilesAreUploaded()}
              onClick={handleDataUpload}
              size="medium"
            >
              FINALIZAR
            </Button>
          </span>
        </Tooltip>
      </Grid>
    </>
  );
};

export default CajaLosAndesDiscountsPage;
