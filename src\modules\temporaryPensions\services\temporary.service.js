/* eslint-disable no-console */
import { axiosRequest } from '../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

//------------------------------------------------------------------------------------------------
export const fetchTemporaryPensions = async () => {
  const { data } = await axiosRequest.get(`${api}/temporaryPensioners`).catch(err => {
    console.error(err);
    return { data: [] };
  });
  return data;
};

//------------------------------------------------------------------------------------------------
export const validateTemporaryPensionsToInsert = async (inputData = '') => {
  const { data } = await axiosRequest
    .post(`${api}/pensions/validatePensionsToInsert`, inputData)
    .catch(err => {
      console.error(err);
      return { data: { results: [] } };
    });
  if (data.results) {
    return data.results.flat();
  }
  return [];
};

//------------------------------------------------------------------------------------------------
export const insertTemporaryPensioners = jsonData =>
  axiosRequest
    .post(`${api}/temporarypensioners/bulk`, jsonData)
    .catch(error => ({ data: {}, isError: true, error }));

//------------------------------------------------------------------------------------------------
export const checkCronExecution = cronName => async setIsCronExecuted => {
  const {
    data: {
      result: { alreadyExecuted }
    }
  } = await axiosRequest
    .get(`${api}/verifycronexecution/${cronName}`)

    .catch(err => {
      console.error(err);
      return { data: { result: { alreadyExecuted: false, error: '' } } };
    });
  setIsCronExecuted(alreadyExecuted);
};
