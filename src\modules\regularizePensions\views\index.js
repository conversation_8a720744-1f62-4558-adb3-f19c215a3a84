/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React, { Suspense } from 'react';
import { renderRoutes } from 'react-router-config';
import { HeaderContent, Page } from 'components';
import { LinearProgress } from '@material-ui/core';
import SnackbarManager from 'components/SnackbarManager';

import { FamilyAssignment, FamilyAssignmentResume } from './family-assignment';
import useStyles from './styles';
import { useSnackbar } from '../hooks/useSnackbar';
import { Orphanhood, OrphanhoodResume} from './orphanhood';

const RegularizedPensionsPage = props => {
  // eslint-disable-next-line react/prop-types
  const { route } = props;
  const classes = useStyles();
  const {
    text,
    setText,
    error,
    setError,
    successImportSnackbar,
    errorImportSnackbar,
    setSuccessImportSnackbar,
    setErrorImportSnackbar
  } = useSnackbar();

  const onSuccessImport = (val = 'El archivo se cargó correctamente') => {
    setText(val);
    setSuccessImportSnackbar(true);
    setErrorImportSnackbar(false);
  };

  const onErrorImport = (err = 'Error al importar archivo') => {
    setError(err);
    setSuccessImportSnackbar(false);
    setErrorImportSnackbar(true);
  };

  return (
    <Page className={classes.root} title="Inactivar y Reactivar Pensiones">
      <HeaderContent title="Inactivar y Reactivar Pensiones" />
      <Suspense fallback={<LinearProgress />}>
        {renderRoutes(route.routes, { onSuccessImport, onErrorImport })}
      </Suspense>
      <SnackbarManager
        handleRoute={false}
        error={error}
        text={text}
        successSnackbar={successImportSnackbar}
        errorSnackbar={errorImportSnackbar}
        setErrorSnackbar={setErrorImportSnackbar}
        setSuccessSnackbar={setSuccessImportSnackbar}
      />
    </Page>
  );
};

// eslint-disable-next-line import/prefer-default-export
export { RegularizedPensionsPage, FamilyAssignment, FamilyAssignmentResume, Orphanhood, OrphanhoodResume };
