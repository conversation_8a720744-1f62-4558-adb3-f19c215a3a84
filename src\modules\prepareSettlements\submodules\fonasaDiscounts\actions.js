//------------------------------------------------------------------------------------------------
export const SET_FONASA_INVALID_FILENAME = 'SET_FONASA_INVALID_FILENAME';
export const setFilenameError = error => {
  return {
    type: SET_FONASA_INVALID_FILENAME,
    data: error
  };
};
// ------------------------------------------------------------------------------------------------
export const CLEAN_FONASA_FILENAME_ERRORS = 'CLEAN_FONASA_FILENAME_ERRORS';
export const setFilenameCleanErrors = () => {
  return {
    type: CLEAN_FONASA_FILENAME_ERRORS
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_FONASA_FILE_ERROR = 'SET_FONASA_FILE_ERROR';
export const setFileError = error => {
  return {
    type: SET_FONASA_FILE_ERROR,
    data: error
  };
};
// ------------------------------------------------------------------------------------------------
export const CLEAN_FONASA_FILE_ERRORS = 'CLEAN_FONASA_FILE_ERRORS';
export const setFileCleanErrors = () => {
  return {
    type: CLEAN_FONASA_FILE_ERRORS
  };
};
// ------------------------------------------------------------------------------------------------
export const ENABLE_FILE_UPLOAD = 'ENABLE_FILE_UPLOAD';
export const setFileUpload = () => {
  return {
    type: ENABLE_FILE_UPLOAD,
    data: true
  };
};
// ------------------------------------------------------------------------------------------------
export const DISABLE_FILE_UPLOAD = 'DISABLE_FILE_UPLOAD';
export const setDisableFileUpload = () => {
  return {
    type: DISABLE_FILE_UPLOAD,
    data: false
  };
};
// ------------------------------------------------------------------------------------------------
export const STORE_JSON = 'STORE_JSON';
export const setStoreJson = json => {
  return {
    type: STORE_JSON,
    data: json
  };
};
// ------------------------------------------------------------------------------------------------
export const CLEAN_JSON = 'CLEAN_JSON';
export const setCleanJson = () => {
  return {
    type: CLEAN_JSON
  };
};
// ------------------------------------------------------------------------------------------------
export const WAS_FONASA_EXECUTED = 'WAS_FONASA_EXECUTED';
export const setWasFonasaProcessExecuted = bool => {
  return {
    type: WAS_FONASA_EXECUTED,
    data: bool
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_IN_DAYS_LIMIT_RANGE = 'SET_IN_DAYS_LIMIT_RANGE';
export const setInDaysLimitRange = isInDaysLimitRange => {
  return {
    type: SET_IN_DAYS_LIMIT_RANGE,
    data: { isInDaysLimitRange }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_IN_NUMBER_DAYS_LIMIT_RANGE = 'SET_IN_NUMBER_DAYS_LIMIT_RANGE';
export const setInNumberDaysLimitRange = isInNumberDaysLimitRange => {
  return {
    type: SET_IN_NUMBER_DAYS_LIMIT_RANGE,
    data: { isInNumberDaysLimitRange }
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_CRON_BASE_MINIMUM_PENSION_EXECUTED = 'SET_CRON_BASE_MINIMUM_PENSION_EXECUTED';
export const setBaseMinimumPensionExecuted = cronBaseMinimumPensionExecuted => {
  return {
    type: SET_CRON_BASE_MINIMUM_PENSION_EXECUTED,
    data: { cronBaseMinimumPensionExecuted }
  };
};
// ------------------------------------------------------------------------------------------------
export const IS_UPLOADING = 'IS_UPLOADING';
export const isDataUploading = status => {
  return {
    type: IS_UPLOADING,
    data: status
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_CURRENT_DATE = 'SET_CURRENT_DATE';
export const setCurrentDate = date => {
  return {
    type: SET_CURRENT_DATE,
    data: date
  };
};
