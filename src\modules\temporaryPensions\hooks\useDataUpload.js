import { readFile, validateFile } from './csvFile';
import { createJSON, validateDate } from '../validator/csv';

import {
  setFileError,
  temporaryFileDataError,
  temporaryPensions,
  cleanFileError,
  setHasScientificNotationError,
  setFileInfo
} from '../actions';

import {
  validateTemporaryPensionsToInsert,
  insertTemporaryPensioners
} from '../services/temporary.service';

const RUT_PATTERN = /^(\d{1,2})\.?(\d{3})\.?(\d{3})-([0-9kK])$/;
const MIN_RUT_LENGTH_WITHOUT_DOTS = 8;
const SCIENTIFIC_NOTATION_ERROR = 'Archivo contiene notación científica';
// eslint-disable-next-line no-magic-numbers
const parseNumberValue = number => Math.round((+number + Number.EPSILON) * 100) / 100;
const defaultNumberValue = number => {
  const value = !isNaN(Number(number)) ? Number(number) : 0;
  return parseInt(value);

}
const parseNumberDL = number => {
  let valueDL = Math.round((+number + Number.EPSILON) * 100) / 100;  
  if(valueDL < 0 ) {     
    return 0;
  }
  if(valueDL > 99999999) {
    return 1;
  }
  return valueDL;
};

const isLengthBelowAdmitted = (rut = '') =>
  rut.replace(/^0+/, '').replace(/[^\dk]/gi, '').length < MIN_RUT_LENGTH_WITHOUT_DOTS;

const formatRut = (rut = '') => {
  if (isLengthBelowAdmitted(rut)) return '';
  return rut
    .replace(/^0+/, '')
    .replace(/[^\dkK]/g, '')
    .replace(/([0-9kK])$/, '-$1')
    .replace(/[k](?=.*[k])/gi, '')
    .replace(RUT_PATTERN, '$1$2$3-$4')
    .toUpperCase();
};

const DEFAULT_TRUNCATE_POSITION = 10;
const truncateValue = (value, position = DEFAULT_TRUNCATE_POSITION) =>
  `${value}`.substring(0, position);

const parseBoolean = value => {
  return value === 'S' ? true : false;
}

const mapToTemporaryPensioner = data => {
  const {
    collectorRut,
    beneficiaryRut,
    beneficiaryNames,
    beneficiaryEmail,
    beneficiaryPhone,
    beneficiaryLastName,
    beneficiaryMothersLastName,
    collectorAddress,
    collectorLastName,
    collectorNames,
    basePension,
    country,
    causantNames,
    causantLastName,
    causantMothersLastName,
    bank,
    transient,
    healthAffiliation,
    cun,
    initialBasePension,
    dateOfBirth,
    gender,
    afpAffiliation,
    paymentGateway,
    causantRut,
    accountNumber,
    pensionType,
    disabilityDegree,
    disabilityType,
    resolutionNumber,
    accidentNumber,
    resolutionDate,
    disabilityStartDate,
    accidentDate,
    pensionCodeId,
    institutionalPatient,
    collectorMothersLastName,
    validityType,
    pensionStartDate,
    article40,
    healthUF,
    collectorCommune,
    collectorBranchOffice,
    collectorCity,
    familyGroup,
    increasingInLaw19578,
    increasingInLaw19953,
    increasingInLaw20102,
    basePensionWithoutIncreases,
    heavyDuty,
    parentRut,
    maritalStatus,
    otherPension,
    regimenOtherPension,
    startAnotherPension,
    amountOtherPension,
    baseIncome,
    totalPensionAccrued,
    indemnityDiscount,
    strennaRetroConstitution,
    otherLink,
    dl1026,
    retirement,
    totalEstimatedDaysToPay  
  } = data;

  return {
    basePension: parseNumberValue(basePension),
    country,
    transient,
    cun,
    initialBasePension: parseNumberValue(initialBasePension),
    dateOfBirth: validateDate(dateOfBirth),
    gender,
    afpAffiliation,
    healthAffiliation,
    paymentInfo: {
      paymentGateway,
      accountNumber,
      bank,
      branchOffice: collectorBranchOffice
    },
    causant: {
      rut: formatRut(causantRut),
      name: causantNames,
      lastName: causantLastName,
      mothersLastName: causantMothersLastName
    },
    collector: {
      rut: formatRut(collectorRut),
      name: collectorNames,
      lastName: collectorLastName,
      mothersLastName: collectorMothersLastName,
      address: collectorAddress,
      commune: collectorCommune,
      city: collectorCity
    },
    beneficiary: {
      rut: country.toUpperCase() === 'CHI' ? formatRut(beneficiaryRut) : beneficiaryRut,
      name: beneficiaryNames,
      lastName: beneficiaryLastName,
      mothersLastName: beneficiaryMothersLastName,
      email: beneficiaryEmail,
      phone: beneficiaryPhone || ''
    },
    validityType,
    pensionType,
    disabilityDegree,
    disabilityType,
    resolutionNumber: truncateValue(resolutionNumber),
    accidentNumber: truncateValue(accidentNumber),
    resolutionDate: validateDate(resolutionDate),
    disabilityStartDate: validateDate(disabilityStartDate),
    accidentDate: validateDate(accidentDate),
    pensionCodeId: truncateValue(pensionCodeId),
    institutionalPatient,
    pensionStartDate: validateDate(pensionStartDate),
    article40: parseNumberValue(article40),
    discounts: {
      healthUF: parseNumberValue(healthUF)
    },
    familyGroup: Number(familyGroup) >= 1 ? Number(familyGroup) : 1,
    increasingInLaw19578: parseNumberValue(increasingInLaw19578),
    increasingInLaw19953: parseNumberValue(increasingInLaw19953),
    increasingInLaw20102: parseNumberValue(increasingInLaw20102),
    basePensionWithoutIncreases: parseNumberValue(basePensionWithoutIncreases),
    heavyDuty: heavyDuty.trim(),
    parentRUT: formatRut(parentRut),
    maritalStatus: maritalStatus.trim(),
    otherPension: otherPension.trim(),
    regimenOtherPension: parseNumberValue(regimenOtherPension),
    startAnotherPension: validateDate(startAnotherPension),
    amountOtherPension: parseNumberValue(amountOtherPension),
    baseIncome: parseNumberValue(baseIncome),
    retroactiveConstitution: {
      totalPensionAccrued: defaultNumberValue(totalPensionAccrued),
      indemnityDiscount: defaultNumberValue(indemnityDiscount),       
      strennaRetroConstitution: defaultNumberValue(strennaRetroConstitution),
      otherLink: defaultNumberValue(otherLink)
    },
    dl1026: parseNumberDL(dl1026),
    retirement : parseBoolean(retirement),
    totalEstimatedDaysToPay: parseNumberValue(totalEstimatedDaysToPay)       
  };
};

const useDataUpload = (dispatch, router, progress, setErrorLinkedSnackbar) => async file => {
  dispatch(cleanFileError());
  if (validateFile(file, err => dispatch(setFileError(err)))) {
    let json = [];
    const data = await readFile(file, createJSON, SCIENTIFIC_NOTATION_ERROR).catch(err => {
      if (err?.message === SCIENTIFIC_NOTATION_ERROR) {
        dispatch(setHasScientificNotationError(true));
        return [];
      }
      dispatch(setFileError(err.message));
      return [];
    });

    if (data.length > 0) {
      progress.show();
      dispatch(cleanFileError());
      dispatch(setHasScientificNotationError(false));
      
      const rowsWithErrors = await validateTemporaryPensionsToInsert(data);

      const errors = rowsWithErrors.filter(({ type }) => type === 'error');
      const warnings = rowsWithErrors.filter(({ type }) => type === 'warning');

      dispatch(temporaryFileDataError({ errors, warnings }));

      if (errors.length === 0) {
        json = data.map(item => mapToTemporaryPensioner(item));
        const { isError } = await insertTemporaryPensioners(json);
        if (!isError) {
          await dispatch(temporaryPensions(json));
        } else {
          setErrorLinkedSnackbar(true);
        }
        dispatch(setFileInfo(file));
      }
      progress.hide();
      router.history.push('/nuevas-pensiones/resumen');
    }
  }
};
export default useDataUpload;
