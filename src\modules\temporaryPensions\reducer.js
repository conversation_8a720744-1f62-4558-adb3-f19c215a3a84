import {
  SET_TEMPORARY_PENSIONS,
  SET_TEMPORARY_FILEDATA_ERROR,
  SET_TEMPORARY_PENSIONS_FILE_ERROR,
  CLEAN_TEMPORARY_PENSIONS_FILE_ERROR,
  C<PERSON>AN_TEMPORARY_FILEDATA_ERROR,
  SET_HAS_SCIENTIFIC_NOTATION_ERROR,
  SET_TEMPORARY_PENSIONS_FILE_INFO,
  CLEAN_TEMPORARY_PENSIONS_FILE
} from './actions';

const initialState = {
  data: [],
  fileName: null,
  isError: false,
  fileErrors: [],
  errors: [],
  warnings: [],
  hasScientificNotationError: false
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case SET_TEMPORARY_PENSIONS_FILE_ERROR:
      return {
        ...state,
        fileErrors: [...state.fileErrors, action.data.fileError]
      };
    case SET_TEMPORARY_PENSIONS:
      return {
        ...state,
        data: [...action.data.temporaryPensions],
        fileName: action.data.temporaryPensions.length ? state.fileName : null
      };
    case SET_TEMPORARY_FILEDATA_ERROR:
      return {
        ...state,
        isError: action.data.isError,
        errors: action.data.errors,
        warnings: action.data.warnings
      };
    case CLEAN_TEMPORARY_FILEDATA_ERROR:
      return {
        ...state,
        isError: false,
        errors: [],
        warnings: []
      };
    case CLEAN_TEMPORARY_PENSIONS_FILE_ERROR:
      return {
        ...state,
        fileErrors: []
      };
    case SET_HAS_SCIENTIFIC_NOTATION_ERROR:
      return {
        ...state,
        hasScientificNotationError: action.data
      };
    case SET_TEMPORARY_PENSIONS_FILE_INFO:
      return {
        ...state,
        fileName: action.data.fileName
      };
    case CLEAN_TEMPORARY_PENSIONS_FILE:
      return {
        ...initialState
      };

    default:
      return state;
  }
}
