import moment from 'moment';
import { readFile, validateFile } from './csv';
import { setOrphanhoodFileError, updateOrphanhoodFileDataError, cleanOrphanhoodFileError, orphanhoodReactivate } from '../actions';
import {
  insertTemporaryOrphanhood,
  wasOrphanhoodInactivatedThisMonth
} from '../services/regularized.service';

const DEFAULT_DATE = '01-01-1900';
function compare(a, b) {
  if (a > b) return 1;
  if (b > a) return -1;
  return 0;
}

const datamodelSchema = [  
  { name:'pensionId',  required: true, order: 1 },
  { name:'causantRut', type: 'rut', required: true, order: 2},
  { name:'causantDv', type: 'dv', required: true, order: 3},
  { name:'collectorRut', type: 'rut', required: false, order: 4},
  { name:'collectorDv', type: 'dv', required: true, order: 5},
  { name:'beneficiaryRut', type: 'rut', required: true, order: 6},
  { name:'beneficiaryDv', type: 'dv', required: true, order: 7},
  { name:'startDate',type: 'date', required: false, order: 8},
  { name:'endDate', type: 'date', required: false, order: 9},
  { name:'pensionBase', required: false, order: 10},
  { name:'state', required: false, order: 11},  
  { name:'validity', required: true, order: 12},
  { name:'dayOfBirth', type: 'date', required:false, order: 13},
  { name:'age', required:false, order: 14},
  { name:'motherRut',type: 'rut', required:false, order: 15},
  { name:'motherDv',type: 'rut', required:false, order: 16},
  { name:'familyGroup', required:false, order: 17}

];
const attrs = datamodelSchema.sort(compare).map(o => o.name);
const dateAttrs = datamodelSchema.filter(o => o.type === 'date').map(o => o.name);
const requiredAttrs = datamodelSchema.filter(o => o.required).map(o => o.name);

const createJSON = values => {
  return attrs.reduce((o, k, i) => {
    let attrValue = values[i] ? `${values[i]}` : null;
    if (dateAttrs.includes(k)) {
      const dateValue = moment(attrValue, 'YYYYMMDD').isValid() ? attrValue : DEFAULT_DATE;
      attrValue = moment(dateValue);
    }
    return { ...o, [k]: attrValue };
  }, {});
};
const isEmpty = str => !str || !str.trim();

const validateRow = (row, index) => {
  const errors = [];
  Object.entries(row)
    .filter(([key, values]) => requiredAttrs.includes(key) && isEmpty(values))
    .forEach(() =>
      errors.push({
        row: index + 1,
        message: `Existen campos obligatorios vacíos`,
        type: 'error'
      })
    );
  return errors;
};

const processData = async ({
  data,
  router,
  dispatch,
  progress,
  onSuccessImport,
  onErrorImport
}) => {
  const errors = data.map((row, i) => validateRow(row, i)).flat();
  dispatch(cleanOrphanhoodFileError());
  dispatch(updateOrphanhoodFileDataError({ errors }));  
  if (errors.length === 0) {
    progress.show();
    const response = await insertTemporaryOrphanhood(data);
    if (!response.isError) {
      onSuccessImport();
    } else {
      onErrorImport();
    }
    dispatch(wasOrphanhoodInactivatedThisMonth());
    dispatch(orphanhoodReactivate(!response.isError));
    progress.hide();
  } else {    
    dispatch(orphanhoodReactivate(false));
    router.history.push('/regularizar-pensiones/inactivar-reactivar/orfandad/resumen');
  }
};

const useDataUpload = (dispatch, router, progress, onSuccessImport, onErrorImport) => {  
    return async file => {
        dispatch(cleanOrphanhoodFileError());
        if (validateFile(file, err => dispatch(setOrphanhoodFileError(err)))) {
        const data = await readFile(file, createJSON).catch(() => {
            dispatch(setOrphanhoodFileError('Error al leer archivo'));
            dispatch(orphanhoodReactivate(false));
            return [];
        });

        if (data.length > 0) {
            await processData({ data, router, dispatch, progress, onSuccessImport, onErrorImport });
        }
      }
    };
};
export default useDataUpload;