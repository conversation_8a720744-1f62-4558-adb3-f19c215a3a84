/* eslint-disable import/no-unresolved */
/* eslint-disable react-hooks/rules-of-hooks */
import moment from 'moment';

import { readFile, validateFile } from './csv';
import { setFileError, updateFileDataError, cleanFileError } from '../actions';
import {
  insertTemporaryIsaprePortal,
  wasExecutedThisMonth
} from '../services/isaprePortal.service';

const DEFAULT_DATE = '01-01-1900';
const EMPTY_FILE_MESSAGE = 'Archivo sin data';
function compare(a, b) {
  if (a > b) return 1;
  if (b > a) return -1;
  return 0;
}
const datamodelSchema = [
  { name: 'folioFun', order: 1 },
  { name: 'notificationType', order: 2 },
  { name: 'funType', order: 3 },
  { name: 'emissionDate', type: 'date', order: 4 },
  { name: 'requestDate', type: 'date', order: 5 },
  { name: 'bussinesId', order: 6 },
  { name: 'bussinesName', order: 7 },
  { name: 'affiliateRut', required: true, order: 8 },
  { name: 'affiliateLastname', order: 9 },
  { name: 'affiliateSecondLastname', order: 10 },
  { name: 'affiliateName', order: 11 },
  { name: 'typeWorker', order: 12 },
  { name: 'totalDiscount', required: true, order: 13 },
  { name: 'monthDiscount', order: 14 },
  { name: 'detailAmountPesos', order: 15 },
  { name: 'detailAmountUF', order: 16 },
  { name: 'detailAmountPercentage', order: 17 },
  { name: 'detailAmountGES', order: 18 },
  { name: 'compensationOtherPesos', order: 19 },
  { name: 'healthContractValue', order: 20 },
  { name: 'chargeDate', order: 20, type: 'date' },
  { name: 'isapre', order: 21 },
  { name: 'isapreId', order: 22, required: true }
];
const attrs = datamodelSchema.sort(compare).map(o => o.name);
const dateAttrs = datamodelSchema.filter(o => o.type === 'date').map(o => o.name);
const requiredAttrs = datamodelSchema.filter(o => o.required).map(o => o.name);

const createJSON = values => {
  return attrs.reduce((o, k, i) => {
    let attrValue = values[i] ? `${values[i]}` : null;
    if (dateAttrs.includes(k)) {
      const dateValue = moment(attrValue, 'DD-MM-YYYY').isValid() ? attrValue : DEFAULT_DATE;
      attrValue = moment(dateValue, 'DD-MM-YYYY');
    }
    return { ...o, [k]: attrValue };
  }, {});
};
const isEmpty = str => !str || !str.trim();

const validateRow = (row, index) => {  
  return Object.entries(row)
    .filter(([key, values]) => requiredAttrs.includes(key) && isEmpty(values))
    .map(() => { 
      return {
        row: index + 1,
        message: `Existen campos obligatorios vacíos`,
        type: 'error'
      }
    });  
};

const processData = async ({
  data,
  router,
  dispatch,
  progress,
  onSuccessImport,
  onErrorImport
}) => {
  const errors = data.map((row, i) => validateRow(row, i)).flat();
  dispatch(cleanFileError());
  dispatch(updateFileDataError({ errors }));

  if (errors.length === 0) {
    progress.show();
    const { error } = await insertTemporaryIsaprePortal(data);
    if (error) {
      onErrorImport(error.msg);
    } else {
      onSuccessImport();
    }

    dispatch(wasExecutedThisMonth());
    progress.hide();
  } else {
    progress.hide();
    router.history.push('/mantenedores/isapres/portal/resumen');
  }
};

const useDataUpload = (
  dispatch,
  router,
  progress,
  onSuccessImport,
  onErrorImport
) => async file => {
  dispatch(cleanFileError());
  if (validateFile(file, err => dispatch(setFileError(err)))) {
    const data = await readFile(file, createJSON).catch(() => {
      dispatch(setFileError(EMPTY_FILE_MESSAGE));
      return [];
    });

    if (data.length > 0) {
      progress.show();
      dispatch(cleanFileError());
      await processData({ data, router, dispatch, progress, onSuccessImport, onErrorImport });
    }
  }
};
export default useDataUpload;
