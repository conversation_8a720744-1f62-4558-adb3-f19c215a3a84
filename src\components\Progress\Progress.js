import React from 'react';
import PropTypes from 'prop-types';

import { useSelector, useDispatch } from 'react-redux';
import { LinearProgress } from '@material-ui/core';

const Progress = ({ selector }) => {
  const isInProgress = useSelector(selector);

  return isInProgress ? <LinearProgress /> : null;
};

const useProgress = (
  { showAction, hideAction } = {
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  }
) => {
  const dispatch = useDispatch();
  return {
    show: () => {
      dispatch({ ...showAction });
    },
    hide: () => {
      dispatch({ ...hideAction });
    }
  };
};
export { Progress, useProgress };

Progress.propTypes = {
  selector: PropTypes.func
};
Progress.defaultProps = {
  selector: store => store.progress && store.progress.isInProgress
};
