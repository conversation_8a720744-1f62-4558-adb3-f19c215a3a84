import { createTheme } from '@material-ui/core/styles';

// eslint-disable-next-line import/prefer-default-export
export const datePickerStyles = createTheme({
  overrides: {
    MuiPickersToolbar: {
      toolbar: {
        height: 20,
        backgroundColor: '#7EBD41'
      },
      toolbarLandscape: {
        maxWidth: 'inherit'
      }
    },
    MuiTypography: {
      h4: {
        fontSize: '14px'
      },
      subtitle1: {
        fontSize: 14
      }
    },
    MuiPickersMonth: {
      root: {
        height: 20
      },
      monthSelected: {
        color: '#7EBD41'
      }
    },
    MuiPickersMonthSelection: {
      container: {
        width: 200,
        maxWidth: 200,
        alignContent: 'space-evenly'
      }
    },
    MuiPickersYear: {
      root: {
        height: '20px',
        flex: '1 0 33.33%'
      },
      yearSelected: {
        color: '#7EBD41',
        marginTop: 5,
        marginBottom: 0
      },
      yearDisabled: {
        marginTop: 5
      }
    },
    MuiPickersYearSelection: {
      container: {
        width: 200,
        maxWidth: 200,
        height: 150,
        display: 'webkit-box',
        flexWrap: 'wrap',
        alignContent: 'space-evenly'
      }
    },
    MuiPickersBasePicker: {
      pickerView: {
        maxWidth: 200,
        MuiPickersYearSelectionh: 200,
        minHeight: 150,
        flexDirection: 'row'
      },
      container: {
        display: 'center'
      },
      containerLandscape: {
        flexDirection: 'column'
      }
    },
    MuiPickersToolbarText: {
      toolbarBtnSelected: {
        fontSize: 20
      }
    },
    MuiFormHelperText: {
      contained: {
        marginLeft: '0px',
        whiteSpace: 'inherit',
        textOverflow: 'ellipsis',
        maxWidth: 200
      }
    },
    MuiSvgIcon: {
      root: {
        width: '1em',
        height: '1em'
      }
    },
    MuiInputBase: {
      root: {
        fontSize: '15px',
        color: 'black',
        '&$disabled': {
          color: 'black',
          borderColor: 'black'
        }
      },
      input: {
        color: 'black',
        borderColor: 'black',
        '&$disabled': {
          color: 'black',
          borderColor: 'black'
        },
        minWidth: '80px'
      }
    },
    MuiFormLabel: {
      root: {
        fontSize: '16px',
        color: 'black',
        '&$disabled': {
          color: 'black',
          borderColor: 'black'
        }
      }
    },
    MuiPopover: {
      paper: {
        marginLeft: '2%'
      }
    },
    MuiOutlinedInput: {
      root: {
        '&$disabled': {
          color: 'black',
          borderColor: 'black'
        }
      },
      notchedOutline: {
        '&$disabled': {
          color: 'black',
          borderColor: 'black'
        }
      },
      input: {
        '&$disabled': {
          color: 'black',
          borderColor: 'black'
        }
      }
    }
  }
});
