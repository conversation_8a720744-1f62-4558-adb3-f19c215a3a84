import { render } from '@testing-library/react';
import { Provider } from 'react-redux'
import configureStore from 'redux-mock-store'
import App from './../App';

describe('testing App', () =>
{
    const initialState = {output:10, progress: { isInProgress: true }}
    const mockStore = configureStore()
    let store

    test('rendering', () =>
    {
        store = mockStore(initialState)
        render(<Provider store={store}><App /></Provider>);
    });
});
