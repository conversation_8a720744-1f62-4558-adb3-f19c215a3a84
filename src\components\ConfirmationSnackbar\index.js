import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Button from '@material-ui/core/Button';
import Snackbar from '@material-ui/core/Snackbar';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';

const useStyles = makeStyles(theme => ({
  close: {
    // eslint-disable-next-line no-magic-numbers
    padding: theme.spacing(0.5)
  },
  btnCancel: {
    color: 'lightgreen'
  },
  btnConfirm: {
    color: 'pink'
  }
}));

export default function ConfirmationSnackbar(props) {
  const { messageInfo, open, setOpen, onConfirm, confirmBtnText, cancelBtnText } = props;

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') return;
    setOpen(false);
  };

  const handleExited = () => {
    setOpen(false);
  };

  const classes = useStyles();
  return (
    <div>
      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left'
        }}
        open={open}
        onClose={handleClose}
        onExited={handleExited}
        message={messageInfo}
        action={
          <React.Fragment>
            <Button className={classes.btnConfirm} size="small" onClick={onConfirm}>
              {confirmBtnText || 'Aceptar'}
            </Button>
            <Button className={classes.btnCancel} size="small" onClick={handleClose}>
              {cancelBtnText || 'Cancelar'}
            </Button>
            <IconButton
              aria-label="close"
              color="inherit"
              className={classes.close}
              onClick={handleClose}
            >
              <CloseIcon />
            </IconButton>
          </React.Fragment>
        }
      />
    </div>
  );
}
