/* eslint-disable react/prop-types */
import React, { Fragment } from 'react';
import { Grid } from '@material-ui/core';

import Header from '../../components/Header';

import RolesTable from '../components/RolesTable';
import listOfRoles from '../utils/listOfRoles';

import styles from './styles';

const TITLE = 'Roles y Permisos';
const SUBTITLE = 'Configuraciones / Sistemas';

const PermissionsAndRoles = ({ _onSuccessSnackbar, _onErrorSnackbar }) => {
  const classes = styles();

  return (
    <Fragment>
      <Grid className={classes.root}>
        <Header title={TITLE} subtitle={SUBTITLE} />
        <Grid className={classes.table}>
          <RolesTable data={listOfRoles} />
        </Grid>
      </Grid>
    </Fragment>
  );
};

export default PermissionsAndRoles;
