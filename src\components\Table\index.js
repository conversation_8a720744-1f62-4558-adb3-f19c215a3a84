/* eslint-disable react/forbid-prop-types */
import React from 'react';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import PerfectScrollbar from 'react-perfect-scrollbar';
import { makeStyles } from '@material-ui/styles';
import {
  Card,
  CardContent,
  TableContainer,
  Table,
  TableBody,
  TableHead,
  TableRow,
  Paper
} from '@material-ui/core';

const useStyles = makeStyles(theme => ({
  root: {},
  content: {
    padding: 0,
    paddingBottom: '0px!important'
  },
  inner: {
    minWidth: 700
  },
  nameCell: {
    display: 'flex',
    alignItems: 'center'
  },
  avatar: {
    height: 42,
    width: 42,
    marginRight: theme.spacing(1)
  },
  actions: {
    padding: theme.spacing(1),
    justifyContent: 'flex-end'
  }
}));

const Results = props => {
  const { className, renderHeader, renderTable, data, ...rest } = props;

  const classes = useStyles();

  return (
    <div {...rest} className={clsx(classes.root, className)}>
      <Card>
        <CardContent className={classes.content}>
          <PerfectScrollbar>
            <TableContainer component={Paper}>
              <Table className={classes.inner}>
                <TableHead>
                  <TableRow>{renderHeader()}</TableRow>
                </TableHead>
                <TableBody>{renderTable()}</TableBody>
              </Table>
            </TableContainer>
          </PerfectScrollbar>
        </CardContent>
      </Card>
    </div>
  );
};

Results.propTypes = {
  className: PropTypes.string,
  data: PropTypes.array,
  renderHeader: PropTypes.func,
  renderTable: PropTypes.func
};

Results.defaultProps = {
  className: '',
  data: [],
  renderHeader: () => [],
  renderTable: () => []
};

export default Results;
