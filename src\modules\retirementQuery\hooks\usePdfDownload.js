/* eslint-disable no-console */
import { getPensionerDocuments } from '../services/queryPensions.service';

const getPdf = async (
  { beneficiaryRut, causantRut, pensionCodeId },
  setIsCurrentlyDownloading,
  progress,
  documentName
) => {
  let success;
  progress.show();
  setIsCurrentlyDownloading(true);
  await getPensionerDocuments({ beneficiaryRut, causantRut, pensionCodeId, documentName })
    .then(({ isError, data, fileName }) => {
      success = !isError;
      if (success) {
        const url = window.URL.createObjectURL(data);
        const a = document.createElement('a');
        a.setAttribute('download', fileName);
        a.href = url;
        a.click();
      }
    })
    .catch(err => console.log(err));
  progress.hide();
  setIsCurrentlyDownloading(false);
};

const usePdfDownload = (
  { beneficiaryRut, causantRut, pensionCodeId },
  setIsCurrentlyDownloading,
  progress,
  documentName
) => {
  return () =>
    getPdf({ beneficiaryRut, causantRut, pensionCodeId }, setIsCurrentlyDownloading, progress, documentName);
};

export default usePdfDownload;
