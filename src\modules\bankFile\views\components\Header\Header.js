import React from 'react';
import { Typography } from '@material-ui/core';
import PropTypes from 'prop-types';
import { HeaderContent } from '../../../../../components';
import useStyles from './styles';

const Header = ({ subtitle, title }) => {
  const classes = useStyles();

  return (
    <>
      <Typography className={classes.subtitle} color="textSecondary">
        {subtitle}
      </Typography>
      <HeaderContent title={title} />
    </>
  );
};

Header.propTypes = {
  subtitle: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired
};

export default Header;
