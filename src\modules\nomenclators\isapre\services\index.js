/* eslint-disable no-console */
/* eslint-disable import/prefer-default-export */
import {
  deleteNomenclatorIsapre,
  loadNomenclatorIsapre,
  updateNomenclatorIsapre,
  createNomenclatorIsapre
} from '../actions';

//-----------------------------------------------------------------------------------------------------------
export const processIsapre = (prevIsapre, newIsapre, operation) => async (
  dispatch,
  _getState,
  { api, axiosRequest }
) => {
  let createdIsapre = newIsapre;
  if (operation === 'create') {
    const {
      data: { result, error, isError }
    } = await createIsapre({ axiosRequest, api, isapre: createdIsapre });
    if (isError) {
      throw new Error(error.code);
    }
    createdIsapre = result;

    await dispatch(createNomenclatorIsapre(createdIsapre));
  }
  if (operation === 'delete') {
    await deleteIsapre({ axiosRequest, api, isapre: createdIsapre });
    await dispatch(deleteNomenclatorIsapre(createdIsapre));
  }

  if (operation === 'update') {
    const {
      data: { error, isError }
    } = await updateIsapre({ axiosRequest, api, isapre: createdIsapre });
    if (isError) {
      throw new Error(error.code);
    }

    await dispatch(updateNomenclatorIsapre(prevIsapre, createdIsapre));
  }
};
const createIsapre = async ({ axiosRequest, api, isapre }) =>
  axiosRequest.post(`${api}/nomenclators/isapres`, { isapre });

const updateIsapre = async ({ axiosRequest, api, isapre }) =>
  axiosRequest.put(`${api}/nomenclators/isapres`, { isapre });

const deleteIsapre = async ({ axiosRequest, api, isapre }) =>
  axiosRequest.delete(`${api}/nomenclators/isapres/${isapre.id}`);

//-----------------------------------------------------------------------------------------------------------
export const loadIsapres = () => async (dispatch, _getState, { api, axiosRequest }) => {
  const {
    data: { result: isapres }
  } = await axiosRequest.get(`${api}/nomenclators/isapres`).catch(err => {
    console.error(err);
    return { data: { result: [] } };
  });
  await dispatch(loadNomenclatorIsapre(isapres));
  return isapres;
};
