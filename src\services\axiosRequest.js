/* eslint-disable no-console */
import axios from 'axios';

const axiosRequest = axios.create({
  'Content-Type': 'application/json; charset=utf-8',
  timeout: 300000,
  crossdomain: true
});

const excelRequest = axios.create({
  'content-type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'content-disposition': 'attachment',
  responseType: 'blob'
});

const pdfRequest = axios.create({
  'content-type': 'application/pdf',
  'content-disposition': 'attachment',
  responseType: 'blob'
});

const fileRequest = axios.create({
  'content-type': '*/*',
  'content-disposition': 'attachment',
  responseType: 'blob'
});

const axiosCSV = axios.create({
  'content-type': 'text/csv',
  'content-disposition': 'attachment',
  responseType: 'blob'
});

const axiosTxt = axios.create({
  'content-type': 'text/plain',
  'content-disposition': 'attachment',
  responseType: 'blob'
});


axiosRequest.interceptors.response.use(
  response => response,
  error => {
    if (error?.response?.data?.message?.toLowerCase()?.includes('token')) {
      localStorage.removeItem('pec.token');
      localStorage.removeItem('msal.idtoken');
      window.location.href = '/';
    }
    throw error;
  }
);

axiosRequest.interceptors.request.use(
  config => {
    const loggedUser = JSON.parse(localStorage.getItem('pec.token'));
    if (loggedUser && loggedUser.token) {
      config.headers.Authorization = loggedUser.token;
    }

    return config;
  },
  err => {
    return Promise.reject(err);
  }
);
excelRequest.interceptors.response.use(
  response => response,
  error => {
    if (error?.response?.data?.message?.toLowerCase().includes('token')) {
      localStorage.removeItem('pec.token');
      window.location.href = '/';
    }
    throw error;
  }
);

excelRequest.interceptors.request.use(
  config => {
    const loggedUser = JSON.parse(localStorage.getItem('pec.token'));

    if (loggedUser?.token) {
      config.headers.Authorization = loggedUser.token;
    }

    return config;
  },
  err => {
    return Promise.reject(err);
  }
);

pdfRequest.interceptors.response.use(
  response => response,
  error => {
    if (error?.response?.data?.message?.toLowerCase().includes('token')) {
      localStorage.removeItem('pec.token');
      window.location.href = '/';
    }
    throw error;
  }
);

pdfRequest.interceptors.request.use(
  config => {
    const loggedUser = JSON.parse(localStorage.getItem('pec.token'));

    if (loggedUser?.token) {
      config.headers.Authorization = loggedUser.token;
    }

    return config;
  },
  err => {
    return Promise.reject(err);
  }
);

fileRequest.interceptors.response.use(
  response => response,
  error => {
    if (error?.response?.data?.message?.toLowerCase().includes('token')) {
      localStorage.removeItem('pec.token');
      window.location.href = '/';
    }
    throw error;
  }
);

fileRequest.interceptors.request.use(
  config => {
    const loggedUser = JSON.parse(localStorage.getItem('pec.token'));

    if (loggedUser?.token) {
      config.headers.Authorization = loggedUser.token;
    }

    return config;
  },
  err => {
    return Promise.reject(err);
  }
);

axiosCSV.interceptors.response.use(
  response => response,
  error => {
    if (error?.response?.data?.message?.toLowerCase().includes('token')) {
      localStorage.removeItem('pec.token');
      window.location.href = '/';
    }
    throw error;
  }
);

axiosCSV.interceptors.request.use(
  config => {
    const loggedUser = JSON.parse(localStorage.getItem('pec.token'));

    if (loggedUser?.token) {
      config.headers.Authorization = loggedUser.token;
    }

    return config;
  },
  err => {
    return Promise.reject(err);
  }
);

axiosTxt.interceptors.response.use(
  response => response,
  error => {
    if (error?.response?.data?.message?.toLowerCase().includes('token')) {
      localStorage.removeItem('pec.token');
      window.location.href = '/';
    }
    throw error;
  }
);

axiosTxt.interceptors.request.use(
  config => {
    const loggedUser = JSON.parse(localStorage.getItem('pec.token'));
    if (loggedUser?.token) {
      config.headers.Authorization = loggedUser.token;
    }

    return config;
  },
  err => {
    return Promise.reject(err);
  }
);

export { axiosRequest, excelRequest, pdfRequest, axiosCSV, axiosTxt, fileRequest };
