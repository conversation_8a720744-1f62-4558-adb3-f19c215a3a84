/* eslint-disable react/prop-types */
// add propTypes later
import React from 'react';
import { Button, Grid, Tooltip, TextField } from '@material-ui/core';
import { withStyles } from '@material-ui/core/styles';
import CloudDownloadIcon from '@material-ui/icons/CloudDownload';
import useOnlineStatus from '@rehooks/online-status';
import { generalClasses as styles } from './styles';

const DownloadForm = ({
  label,
  classes,
  value,
  helperText,
  errors,
  tooltipTitle,
  disabled,
  onClick
}) => {
  const onlineStatus = useOnlineStatus();
  return (
    <>
      <Grid container spacing={2}>
        <Grid item>
          <TextField
            label={label}
            className={classes.textfield}
            value={value}
            helperText={helperText}
            error={errors}
            disabled
            variant="outlined"
            size="small"
          />
        </Grid>
        <Grid item>
          <Tooltip title={tooltipTitle}>
            <span className={classes.spanFinalize}>
              <Button
                variant="contained"
                className={classes.importDownloadButton}
                color="primary"
                disabled={!onlineStatus || disabled}
                onClick={onClick}
                size="medium"
              >
                <CloudDownloadIcon />
              </Button>
            </span>
          </Tooltip>
        </Grid>
      </Grid>
    </>
  );
};

export default withStyles(styles)(DownloadForm);
