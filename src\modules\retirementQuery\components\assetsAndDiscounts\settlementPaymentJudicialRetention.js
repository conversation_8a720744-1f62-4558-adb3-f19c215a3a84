/* eslint-disable no-param-reassign */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unused-prop-types */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import { useSelector, useDispatch } from 'react-redux';
import useStyles from '../pensionerDetail/style';
import createTable from '../../utils/createTable';
import {
  setRetentionPaymentGateway,
  setRetentionBank,
  setRetentionAccountNumber,
  setRetentionBranchOffice,
  setModifiedFieldErrors,
  setBankBranchOfficeList,
  setServipagBranchOfficeList,
} from '../../actions';
import genericHandle from '../../utils/genericHandler';

import {
  paymentGatewayList,
  bankOnlyRelatedOptions,
  bankRelatedPaymentGateway,
  voucherBankRelatedOptions,
  branchOfficeRelatedFields,
  voucherServipagRelatedOptions
} from '../../utils/paymentGatewayList';
import bankList from '../../utils/bankList';

import { NUMERIC_ONLY, numericOnlySanitizer } from '../../utils/formatters';

import { generateAndUploadBankFileExecuted,
          loadBankBranchOffices,
          loadServipagBranchOffices
      } from '../../services/queryPensions.service';

const bcoChile = /Banco de Chile/i;

const handleAccountChange = (
  e,
  {
    key,
    dispatch,
    action,
    actionError,
    modifiedFieldErrors,
    formatter,
    validation,
    isRequired,
    maxLength
  }
) => {
  const { value } = e.target;

  const formattedWord = formatter(value) || '';

  const isValidFormatAndLength =
    formattedWord.length <= maxLength && validation.test(formattedWord);

  const isCorrect = isRequired
    ? isValidFormatAndLength
    : formattedWord.length === 0 || isValidFormatAndLength;
  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));
  dispatch(action(formattedWord));
};

const handlePaymentGatewayAndBankChange = (
  e,
  { dispatch, action, actionError, modifiedFieldErrors, key, isRequired, options }
) => {

  const { value } = e.target;

  dispatch(setRetentionBank(bankList[0]));
  dispatch(setRetentionAccountNumber('')); 

  const isCorrect = isRequired ? value !== options[0] : true;
  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));
  dispatch(action(value));  
};


const SettlementPaymentJudicialRetention = ({ values, editable, readOnly }) => {

  const classes = useStyles();
  const dispatch = useDispatch();
  
  values.bankBranchOfficeList = '-'
  values.retentionAccountNumber = '-'
  values.retentionBank = '-'

  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);

  const bankBranchOfficeList = useSelector(store => store.queryPensions.bankBranchOfficeList);
  const servipagBranchOfficeList = useSelector(
    store => store.queryPensions.servipagBranchOfficeList
  );

  const retentionPaymentGateway = useSelector(store => store.queryPensions.retentionPaymentGateway);
  let retentionBank = useSelector(store => store.queryPensions.retentionBank);
  const retentionBranchOffice = useSelector(store => store.queryPensions.retentionBranchOffice);
  const retentionAccountNumber = useSelector(store => store.queryPensions.retentionAccountNumber);

  const isCorrectOptionSelected = (
    selectedValue = bankRelatedPaymentGateway[0],
    optionsToActivate
  ) => {
    return optionsToActivate.includes(selectedValue);
  };

  const branchOfficeOptions = (selectedValue = bankRelatedPaymentGateway[0]) => {
    return selectedValue === 'SERVIPAG' ? servipagBranchOfficeList : bankBranchOfficeList;
  };

  const [conditionBank, setConditionBank] = useState(false);
  const [conditionAccountNumber, setConditionAccountNumber] = useState(true);

  useEffect(() => {
    loadBankBranchOffices(dispatch, setBankBranchOfficeList);
    loadServipagBranchOffices(dispatch, setServipagBranchOfficeList);
  
    dispatch(generateAndUploadBankFileExecuted());
  }, []);

  const rowFormation = [
    [
      {
        key: 'retentionPaymentGateway',
        name: 'Vía de pago',
        type: 'select',
        options: paymentGatewayList,
        handleInputChange: handlePaymentGatewayAndBankChange,
        selectedValue: retentionPaymentGateway,
        condition: true,
        isRequired: true,
        errorMessage: 'Debe seleccionar una opción',
        dispatch,
        action: setRetentionPaymentGateway,
        actionError: setModifiedFieldErrors,
        modifiedField: retentionPaymentGateway,
        modifiedFieldErrors
      },
      {
        key: 'retentionBank',
        name: 'Banco',
        type: 'select',
        options: bankList,
        handleInputChange: handlePaymentGatewayAndBankChange,
        selectedValue: retentionBank,
        isRequired: true,
        errorMessage: 'Debe seleccionar una opción',
        condition: conditionBank,
        dispatch,
        action: setRetentionBank,
        actionError: setModifiedFieldErrors,
        modifiedField: retentionBank,
        modifiedFieldErrors
      }
    ],
    [
      {
        key: 'retentionBranchOffice',
        name: 'Sucursal',
        type: 'select',
        options: branchOfficeOptions(retentionPaymentGateway),
        handleInputChange: genericHandle,
        selectedValue: retentionBranchOffice,
        isRequired: false,
        condition: isCorrectOptionSelected(retentionPaymentGateway, branchOfficeRelatedFields),
        dispatch,
        action: setRetentionBranchOffice,
        actionError: setModifiedFieldErrors,
        modifiedField: retentionBranchOffice,
        modifiedFieldErrors
      },
      {
        key: 'retentionAccountNumber',
        name: 'Número de cuenta',
        type: 'text',
        toWrite: retentionAccountNumber,
        handleInputChange: handleAccountChange,
        condition: conditionAccountNumber,
        validation: NUMERIC_ONLY,
        formatter: numericOnlySanitizer,
        isRequired: conditionAccountNumber,
        errorMessage: 'El valor es inválido',
        dispatch,
        action: setRetentionAccountNumber,
        modifiedField: retentionAccountNumber,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 30,
        editable: false
      }      
    ]
  ];

  useEffect(() => {
   setConditionAccountNumber(true);   

    if(bcoChile.test(retentionPaymentGateway) || ((/vista/i).test(retentionPaymentGateway)))
      {
        dispatch(setRetentionBank(bankList[1]));
        dispatch(setRetentionBranchOffice(bankBranchOfficeList[0]));
        setConditionAccountNumber(((/vista/i).test(retentionPaymentGateway)) ? false : true);
        setConditionBank(true);
        dispatch(
          setModifiedFieldErrors({
            ...modifiedFieldErrors,
            retentionBank: false
          })
        );  
        return;
      } 
 
    if (retentionPaymentGateway && retentionPaymentGateway !== paymentGatewayList[0]) {
      setConditionBank(isCorrectOptionSelected(retentionPaymentGateway, bankOnlyRelatedOptions));
      setConditionAccountNumber(isCorrectOptionSelected(retentionPaymentGateway, bankRelatedPaymentGateway));
      if((retentionPaymentGateway === paymentGatewayList[2] || retentionPaymentGateway === paymentGatewayList[3]) 
        &&  retentionBank === bankList[0] ){
          dispatch(
            setModifiedFieldErrors({
              ...modifiedFieldErrors,
              retentionBank: true
            })
          );      
        }      
           
    } else {
      setConditionAccountNumber(false);
      setConditionBank(false);
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          retentionAccountNumber: true,
          retentionBank: true,
          retentionPaymentGateway: true
        })
      );
    }
  

    if (editable && voucherBankRelatedOptions.includes(retentionPaymentGateway)) {
      dispatch(setRetentionBranchOffice(bankBranchOfficeList[0]));
      return;
    }
    if (editable && voucherServipagRelatedOptions.includes(retentionPaymentGateway)){
      dispatch(setRetentionBranchOffice(servipagBranchOfficeList[0]));       
      setConditionAccountNumber(false);  
      setConditionBank(false)
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          retentionAccountNumber: false,
          retentionBank: false,
          retentionPaymentGateway: false
        })
      );  
    }
  }, [retentionPaymentGateway]);

  useEffect(() => {
    if (conditionAccountNumber && !retentionAccountNumber)
      dispatch(setModifiedFieldErrors({ ...modifiedFieldErrors, retentionAccountNumber: true }));
  }, [conditionAccountNumber]);

  useEffect(() => {
    if (retentionPaymentGateway === paymentGatewayList[0]) {
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          retentionPaymentGateway: true,
          retentionAccountNumber: true,
          retentionBank: true
        })
      );
      return;
    }
    if (conditionAccountNumber && !retentionAccountNumber) {
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          retentionAccountNumber: true
        })
      );
      return;
    }
    if (conditionBank && retentionBank === bankList[0]) {
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          retentionBank: true
        })
      );
    }    
  }, [editable]);

  return (
    <>
    <form>
    
      <Card className={classes.cardContainer}>
        <CardHeader title="Pago liquidación" className={classes.cardHeader} />
        <Divider />
        <fieldset disabled={readOnly}>
        <CardContent className={classes.content}>
          {createTable({
            data: values,
            format: rowFormation,
            editable: true,
            panelName: 'settlementPaymentJudicialRetention'
          })}
        </CardContent>
        </fieldset>
      </Card>
      
    </form>
    </>
  );
};

SettlementPaymentJudicialRetention.propTypes = {
  values: PropTypes.shape({}).isRequired,
  editable: PropTypes.bool.isRequired
};

export default SettlementPaymentJudicialRetention;
