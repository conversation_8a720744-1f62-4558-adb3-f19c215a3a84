import { makeStyles } from '@material-ui/styles';

export const FORM_WIDTH = '100%';

const useStyles = makeStyles(theme => ({
  textfield: {
    width: '33%',
    minWidth: 300,
    maxWidth: 300,
    marginBottom: 0,
    '& .MuiInputBase-root.Mui-disabled': {
      color: '#212121'
    }
  },
  importDownloadButton: {
    fontWeight: 'bold'
  },
  importButton: {
    height: '38px'
  },
  formControl: {
    margin: 0,
    border: 0,
    display: 'inline-flex',
    padding: 0,
    'align-items': 'center',
    position: 'relative',
    'min-width': 0,

    'flex-direction': 'column',
    'vertical-align': 0,
    '& p': {
      textAlign: 'center'
    }
  }
}));

export default useStyles;
