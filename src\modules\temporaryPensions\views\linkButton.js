import React from 'react';
import useOnlineStatus from '@rehooks/online-status';
import PropTypes from 'prop-types';
import { Button, Tooltip } from '@material-ui/core';

const LinkButton = ({ disabledLink, linkText, data, handleLinkPensions }) => {
  const onlineStatus = useOnlineStatus();
  return (
    <Tooltip title={linkText}>
      <span>
        <Button
          variant="contained"
          color="primary"
          disabled={!onlineStatus || disabledLink || data.length === 0}
          onClick={handleLinkPensions}
        >
          Enlazar
        </Button>
      </span>
    </Tooltip>
  );
};

LinkButton.propTypes = {
  disabledLink: PropTypes.bool,
  linkText: PropTypes.string,
  handleLinkPensions: PropTypes.func.isRequired,
  data: PropTypes.arrayOf(PropTypes.any)
};
LinkButton.defaultProps = {
  linkText: '',
  disabledLink: true,
  data: []
};

export default LinkButton;
