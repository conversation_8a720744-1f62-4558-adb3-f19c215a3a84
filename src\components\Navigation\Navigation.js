/* eslint-disable react/forbid-prop-types */
/* eslint-disable import/no-unresolved */
import React, { useContext } from 'react';
import { matchPath } from 'react-router-dom';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import { List, Typography } from '@material-ui/core';
import useRouter from 'utils/useRouter';
import { NavigationListItem } from './components';
import { AppContext } from '../../provider/app';

const NO_READ_REGEX = /NoRead/i;
const DEFAULT_SPACING = 3;

const useStyles = makeStyles(theme => ({
  root: {
    marginBottom: theme.spacing(DEFAULT_SPACING)
  }
}));

const NavigationList = props => {
  const { pages, ...rest } = props;
  const { loggedUser } = useContext(AppContext);
  const { userViews } = loggedUser;

  return (
    <List>
      {pages.reduce((items, page) => {
        const finder = view =>
          view.module.match(new RegExp(page.module, 'i')) && !NO_READ_REGEX.test(view.permission);
        const pageModuleInUserViews = userViews.find(finder);
        const pageIsDisabledForUser = !pageModuleInUserViews;
        const unauthorizedViews = userViews.filter(view => NO_READ_REGEX.test(view.permission));
        const viewIsDisabled = !!unauthorizedViews.find(view =>
          view.view.match(new RegExp(page.title, 'i'))
        );
        return reduceChildRoutes({
          items,
          page: { ...page, pageIsDisabledForUser, viewIsDisabled },
          ...rest
        });
      }, [])}
    </List>
  );
};

NavigationList.propTypes = {
  depth: PropTypes.number,
  pages: PropTypes.array
};
NavigationList.defaultProps = {
  depth: 0,
  pages: []
};

const reduceChildRoutes = props => {
  const { router, items, page, depth } = props;

  if (page.children) {
    const open = matchPath(router.location.pathname, {
      path: page.href,
      exact: false
    });

    items.push(
      <NavigationListItem
        depth={depth}
        icon={page.icon}
        key={page.title}
        label={page.label}
        open={Boolean(open)}
        title={page.title}
        isDisabled={page.pageIsDisabledForUser || page.viewIsDisabled}
      >
        <NavigationList depth={depth + 1} pages={page.children} router={router} />
      </NavigationListItem>
    );
  } else {
    items.push(
      <NavigationListItem
        depth={depth}
        href={page.href}
        icon={page.icon}
        key={page.title}
        label={page.label}
        title={page.title}
        isDisabled={page.pageIsDisabledForUser || page.viewIsDisabled}
      />
    );
  }

  return items;
};

const Navigation = props => {
  const { title, pages, className, component: Component, ...rest } = props;

  const classes = useStyles();
  const router = useRouter();

  return (
    <Component {...rest} className={clsx(classes.root, className)}>
      {title && <Typography variant="overline">{title}</Typography>}
      <NavigationList depth={0} pages={pages} router={router} />
    </Component>
  );
};

Navigation.propTypes = {
  className: PropTypes.string,
  component: PropTypes.any,
  pages: PropTypes.array,
  title: PropTypes.string
};

Navigation.defaultProps = {
  component: 'nav',
  pages: [],
  title: '',
  className: ''
};

export default Navigation;
