import moment from 'moment';
import { currentlyDownloadingReports } from '../actions';
import { queryTimeInterval } from '../services/summarizeSettlements.service';

const ON_ERROR_TEXT = 'Hubo un error con la descarga del reporte';
const downloadFiles = async (
  dispatch,
  progress,
  onErrorSnackbar,
  startingDate,
  endingDate,
  currentDate
) => {
  let success;
  const date = moment(currentDate).format('DDMMYYYY');
  const fileName = `Reporte Liquidación ${date}`;
  progress.show();
  dispatch(currentlyDownloadingReports(true));
  await queryTimeInterval(startingDate, endingDate).then(({ isError, data }) => {
    success = !isError;
    if (success) {
      const url = window.URL.createObjectURL(data);
      const a = document.createElement('a');
      a.setAttribute('download', `${fileName}.xlsx`);
      a.href = url;
      a.click();
    }
  });
  if (!success) {
    dispatch(currentlyDownloadingReports(false));
    onErrorSnackbar(`${ON_ERROR_TEXT}`);
    progress.hide();
    return false;
  }
  dispatch(currentlyDownloadingReports(false));
  progress.hide();
  return true;
};

const useDataDownload = (
  dispatch,
  _router,
  progress,
  onErrorSnackbar,
  startingDate,
  endingDate,
  currentDate
) => {
  return () =>
    downloadFiles(dispatch, progress, onErrorSnackbar, startingDate, endingDate, currentDate);
};

export default useDataDownload;
