//------------------------------------------------------------------------------------------------
export const WAS_DATA_SEARCHED = 'WAS_DATA_SEARCHED';
export const setWasDataSearched = state => {
  return {
    type: WAS_DATA_SEARCHED,
    data: state
  };
};
//------------------------------------------------------------------------------------------------
export const SET_PENSIONS_DATA = 'SET_PENSIONS_DATA';
export const setPensionData = jsonData => {
  return {
    type: SET_PENSIONS_DATA,
    data: jsonData
  };
};
//------------------------------------------------------------------------------------------------
export const SET_WAS_A_FIELD_MODIFIED = 'SET_WAS_A_FIELD_MODIFIED';
export const setWasModifiedAFieldThatRequiresAFile = wasModified => {
  return {
    type: SET_WAS_A_FIELD_MODIFIED,
    data: wasModified
  };
};
//------------------------------------------------------------------------------------------------
export const SET_MODIFIED_FIELD = 'SET_MODIFIED_FIELD';
export const setModifiedField = fieldObj => {
  return {
    type: SET_MODIFIED_FIELD,
    data: fieldObj
  };
};
//------------------------------------------------------------------------------------------------
export const SET_PENSIONER_PDF_FILE = 'SET_PENSIONER_PDF_FILE';
export const setPdfFile = pdfFile => {
  return {
    type: SET_PENSIONER_PDF_FILE,
    data: pdfFile
  };
};
//------------------------------------------------------------------------------------------------
export const SET_WAS_PDF_UPLOADED = 'SET_WAS_PDF_UPLOADED';
export const setWasPdfUploaded = wasUploaded => {
  return {
    type: SET_WAS_PDF_UPLOADED,
    data: wasUploaded
  };
};
//------------------------------------------------------------------------------------------------
export const SET_AFP_LIST = 'SET_AFP_LIST';
export const setAFPList = afpList => {
  return {
    type: SET_AFP_LIST,
    data: afpList
  };
};
//------------------------------------------------------------------------------------------------
export const SET_BANKBRANCH_LIST = 'SET_BANKBRANCH_LIST';
export const setBankBranchOfficeList = bankList => {
  return {
    type: SET_BANKBRANCH_LIST,
    data: bankList
  };
};
//------------------------------------------------------------------------------------------------
export const SET_SERVIPAG_LIST = 'SET_SERVIPAG_LIST';
export const setServipagBranchOfficeList = servipagList => {
  return {
    type: SET_SERVIPAG_LIST,
    data: servipagList
  };
};
//------------------------------------------------------------------------------------------------
export const SET_DATA_SUCCESSFULLY_UPDATED = 'SET_DATA_SUCCESSFULLY_UPDATED';
export const setDataSuccessfullyUpdated = obj => {
  return {
    type: SET_DATA_SUCCESSFULLY_UPDATED,
    data: obj
  };
};
//------------------------------------------------------------------------------------------------
export const SET_MODIFIED_FIELD_ERRORS = 'SET_MODIFIED_FIELD_ERRORS';
export const setModifiedFieldErrors = obj => {
  return {
    type: SET_MODIFIED_FIELD_ERRORS,
    data: obj
  };
};
//------------------------------------------------------------------------------------------------
export const SET_WAS_INFO_LOADED = 'SET_WAS_INFO_LOADED';
export const setWasInfoLoaded = value => {
  return {
    type: SET_WAS_INFO_LOADED,
    data: value
  };
};
// FIELDS THAT CAN CHANGE FOR A PENSIONER DOWN BELOW
//------------------------------------------------------------------------------------------------
export const SET_INSTITUTIONAL_PATIENT = 'SET_INSTITUTIONAL_PATIENT';
export const setInstitutionalPatient = value => {
  return {
    type: SET_INSTITUTIONAL_PATIENT,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_AFP_AFFILIATION = 'SET_AFP_AFFILIATION';
export const setAfpAffiliation = value => {
  return {
    type: SET_AFP_AFFILIATION,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_PENSION_TYPE = 'SET_PENSION_TYPE';
export const setPensionType = value => {
  return {
    type: SET_PENSION_TYPE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_PENSION_TYPE_DUE_TO_CHARGES = 'SET_PENSION_TYPE_DUE_TO_CHARGES';
export const setPensionTypeDueToCharges = value => {
  return {
    type: SET_PENSION_TYPE_DUE_TO_CHARGES,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_BENEFICIARY_EMAIL = 'SET_BENEFICIARY_EMAIL';
export const setBeneficiaryEmail = value => {
  return {
    type: SET_BENEFICIARY_EMAIL,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_PAYMENT_GATEWAY = 'SET_PAYMENT_GATEWAY';
export const setPaymentGateway = value => {
  return {
    type: SET_PAYMENT_GATEWAY,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_BANK = 'SET_BANK';
export const setBank = value => {
  return {
    type: SET_BANK,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_ACCOUNT_NUMBER = 'SET_ACCOUNT_NUMBER';
export const setAccountNumber = value => {
  return {
    type: SET_ACCOUNT_NUMBER,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_BRANCH_OFFICE = 'SET_BRANCH_OFFICE';
export const setBranchOffice = value => {
  return {
    type: SET_BRANCH_OFFICE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_RUT = 'SET_COLLECTOR_RUT';
export const setCollectorRut = value => {
  return {
    type: SET_COLLECTOR_RUT,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_ADDRESS = 'SET_COLLECTOR_ADDRESS';
export const setCollectorAddress = value => {
  return {
    type: SET_COLLECTOR_ADDRESS,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_COMMUNE = 'SET_COLLECTOR_COMMUNE';
export const setCollectorCommune = value => {
  return {
    type: SET_COLLECTOR_COMMUNE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_NAME = 'SET_COLLECTOR_NAME';
export const setCollectorName = value => {
  return {
    type: SET_COLLECTOR_NAME,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_LASTNAME = 'SET_COLLECTOR_LASTNAME';
export const setCollectorLastName = value => {
  return {
    type: SET_COLLECTOR_LASTNAME,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_CITY = 'SET_COLLECTOR_CITY';
export const setCollectorCity = value => {
  return {
    type: SET_COLLECTOR_CITY,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_MOTHERS_LASTNAME = 'SET_COLLECTOR_MOTHERS_LASTNAME';
export const setCollectorMothersLastName = value => {
  return {
    type: SET_COLLECTOR_MOTHERS_LASTNAME,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_BENEFICIARY_PHONE = 'SET_BENEFICIARY_PHONE';
export const setBeneficiaryPhone = value => {
  return {
    type: SET_BENEFICIARY_PHONE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CUN = 'SET_CUN';
export const setCun = value => {
  return {
    type: SET_CUN,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_GENDER = 'SET_GENDER';
export const setGender = value => {
  return {
    type: SET_GENDER,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COUNTRY = 'SET_COUNTRY';
export const setCountry = value => {
  return {
    type: SET_COUNTRY,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_ACCIDENT_DATE = 'SET_ACCIDENT_DATE';
export const setAccidentDate = value => {
  return {
    type: SET_ACCIDENT_DATE,
    data: value
  };
};
export const SET_IS_FIRST_CHANGE_IN_COLLECTOR_RUT = 'SET_IS_FIRST_CHANGE_IN_COLLECTOR_RUT';
export const setIsFirstChangeInCollectorRut = bool => {
  return {
    type: SET_IS_FIRST_CHANGE_IN_COLLECTOR_RUT,
    data: bool
  };
};
//------------------------------------------------------------------------------------------------
export const SET_AGREES_TO_CHANGE_COLLECTOR_RUT = 'SET_AGREES_TO_CHANGE_COLLECTOR_RUT';
export const setAgreesToChangeCollectorRut = bool => {
  return {
    type: SET_AGREES_TO_CHANGE_COLLECTOR_RUT,
    data: bool
  };
};
//------------------------------------------------------------------------------------------------
export const SET_FOR_FAMILY_ASSIGNMENT = 'SET_FOR_FAMILY_ASSIGNMENT';
export const setForFamilyAssignment = value => {
  return {
    type: SET_FOR_FAMILY_ASSIGNMENT,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_ARTICLE_40 = 'SET_ARTICLE_40';
export const setArticle40 = value => {
  return {
    type: SET_ARTICLE_40,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_HEALTH_LOAN = 'SET_HEALTH_LOAN';
export const setHealthLoan = value => {
  return {
    type: SET_HEALTH_LOAN,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_BANK_REJECTED = 'SET_BANK_REJECTED';
export const setBankRejected = value => {
  return {
    type: SET_BANK_REJECTED,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_PAYCHECK_REFUNDED = 'SET_PAYCHECK_REFUNDED';
export const setPaycheckRefunded = value => {
  return {
    type: SET_PAYCHECK_REFUNDED,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_DEPENDENCY_EXECUTED = 'SET_DEPENDENCY_EXECUTED';
export const setDependencyExecuted = value => {
  return {
    type: SET_DEPENDENCY_EXECUTED,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_VALIDITY_TYPE = 'SET_VALIDITY_TYPE';
export const setValidityType = value => {
  return {
    type: SET_VALIDITY_TYPE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_INACTIVATION_REASON = 'SET_INACTIVATION_REASON';
export const setInactivationReason = value => {
  return {
    type: SET_INACTIVATION_REASON,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_END_DATE_OF_VALIDITY = 'SET_END_DATE_OF_VALIDITY';
export const setEndDateOfValidity = value => {
  return {
    type: SET_END_DATE_OF_VALIDITY,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_END_DATE_OF_THEORICAL_VALIDITY = 'SET_END_DATE_OF_THEORICAL_VALIDITY';
export const setEndDateOfTheoricalValidity = value => {
  return {
    type: SET_END_DATE_OF_THEORICAL_VALIDITY,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_INACTIVATION_DATE = 'SET_INACTIVATION_DATE';
export const setInactivationDate = value => {
  return {
    type: SET_INACTIVATION_DATE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_EVALUATION_DATE = 'SET_EVALUATION_DATE';
export const setEvaluationDate = value => {
  return {
    type: SET_EVALUATION_DATE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_REACTIVATION_DATE = 'SET_REACTIVATION_DATE';
export const setReactivationDate = value => {
  return {
    type: SET_REACTIVATION_DATE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_REACTIVATE_MANUALLY = 'SET_REACTIVATE_MANUALLY';
export const setReactivateManually = bool => {
  return {
    type: SET_REACTIVATE_MANUALLY,
    data: bool
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CURRENT_DATE = 'SET_CURRENT_DATE';
export const setCurrentDate = date => {
  return {
    type: SET_CURRENT_DATE,
    data: date
  };
};
//------------------------------------------------------------------------------------------------
export const SET_IS_INACTIVATION_REASON_CHANGE = 'SET_IS_INACTIVATION_REASON_CHANGE';
export const setIsInactivationReasonChange = date => {
  return {
    type: SET_IS_INACTIVATION_REASON_CHANGE,
    data: date
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_RETENTION_RUT = 'SET_COLLECTOR_RETENTION_RUT';
export const setCollectorRetentionRut = value => {
  return {
    type: SET_COLLECTOR_RETENTION_RUT,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_RETENTION_ADDRESS = 'SET_COLLECTOR_RETENTION_ADDRESS';
export const setCollectorRetentionAddress = value => {
  return {
    type: SET_COLLECTOR_RETENTION_ADDRESS,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_RETENTION_COMMUNE = 'SET_COLLECTOR_RETENTION_COMMUNE';
export const setCollectorRetentionCommune = value => {
  return {
    type: SET_COLLECTOR_RETENTION_COMMUNE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_RETENTION_NAME = 'SET_COLLECTOR_RETENTION_NAME';
export const setCollectorRetentionName = value => {
  return {
    type: SET_COLLECTOR_RETENTION_NAME,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_RETENTION_LASTNAME = 'SET_COLLECTOR_RETENTION_LASTNAME';
export const setCollectorRetentionLastName = value => {
  return {
    type: SET_COLLECTOR_RETENTION_LASTNAME,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_RETENTION_CITY = 'SET_COLLECTOR_RETENTION_CITY';
export const setCollectorRetentionCity = value => {
  return {
    type: SET_COLLECTOR_RETENTION_CITY,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_COLLECTOR_RETENTION_MOTHERS_LASTNAME = 'SET_COLLECTOR_RETENTION_MOTHERS_LASTNAME';
export const setCollectorRetentionMothersLastName = value => {
  return {
    type: SET_COLLECTOR_RETENTION_MOTHERS_LASTNAME,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_RETENTION_PAYMENT_GATEWAY = 'SET_RETENTION_PAYMENT_GATEWAY';
export const setRetentionPaymentGateway = value => {
  return {
    type: SET_RETENTION_PAYMENT_GATEWAY,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_RETENTION_BANK = 'SET_RETENTION_BANK';
export const setRetentionBank = value => {
  return {
    type: SET_RETENTION_BANK,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_RETENTION_ACCOUNT_NUMBER = 'SET_RETENTION_ACCOUNT_NUMBER';
export const setRetentionAccountNumber = value => {
  return {
    type: SET_RETENTION_ACCOUNT_NUMBER,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_RETENTION_BRANCH_OFFICE = 'SET_RETENTION_BRANCH_OFFICE';
export const setRetentionBranchOffice = value => {
  return {
    type: SET_RETENTION_BRANCH_OFFICE,
    data: value
  };
}; 
//------------------------------------------------------------------------------------------------
export const SET_FORMULABLE_LABEL = 'SET_FORMULABLE_LABEL';
export const setFormulableLabel = value => {
  return {
    type: SET_FORMULABLE_LABEL,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_FORMULABLE_ASSET_TYPE = 'SET_FORMULABLE_ASSET_TYPE';
export const setFormulableAssetType = value => {
  return {
    type: SET_FORMULABLE_ASSET_TYPE,
    data: value
  };
};

//------------------------------------------------------------------------------------------------
export const SET_FORMULABLE_AMOUNT = 'SET_FORMULABLE_AMOUNT';
export const setFormulableAmount = value => {
  return {
    type: SET_FORMULABLE_AMOUNT,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_FORMULABLE_REASON = 'SET_FORMULABLE_REASON';
export const setFormulableReason = value => {
  return {
    type: SET_FORMULABLE_REASON,
    data: value
  };
};

//------------------------------------------------------------------------------------------------
export const SET_FORMULABLE_TYPE_RETENTION = 'SET_FORMULABLE_TYPE_RETENTION';
export const setFormulableTypeRetention = value => {
  return {
    type: SET_FORMULABLE_TYPE_RETENTION,
    data: value
  };
};

//------------------------------------------------------------------------------------------------
export const SET_FORMULABLE_VALIDITY = 'SET_FORMULABLE_VALIDITY';
export const setFormulableValidity = value => {
  return {
    type: SET_FORMULABLE_VALIDITY,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_FORMULABLE_AMOUNT_RETENTION = 'SET_FORMULABLE_AMOUNT_RETENTION';
export const setFormulableAmountRetention = value => {
  return {
    type: SET_FORMULABLE_AMOUNT_RETENTION,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_FORMULABLE_START_DATE = 'SET_FORMULABLE_START_DATE';
export const setFormulableStartDate = value => {
  return {
    type: SET_FORMULABLE_START_DATE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_FORMULABLE_END_DATE = 'SET_FORMULABLE_END_DATE';
export const setFormulableEndDate = value => {
  return {
    type: SET_FORMULABLE_END_DATE,
    data: value
  };
};
//------------------------------------------------------------------------------------------------
export const SET_RETENCION_VISIBILITY = 'SET_RETENCION_VISIBILITY';
export const setRetentionVisibility = value => {
  return {
    type: SET_RETENCION_VISIBILITY,
    data: value
  };
};

//------------------------------------------------------------------------------------------------
export const SET_RETENCION_DISCOUNT_AND_ASSETS = 'SET_RETENCION_DISCOUNT_AND_ASSETS';
export const setDiscountsAndAssets  = value => {
  return {
    type: SET_RETENCION_DISCOUNT_AND_ASSETS,
    data: value
  };
};
export const SET_BASEPENSION = 'SET_BASEPENSION';
export const setBasePension = value => {
  return {
    type: SET_BASEPENSION,
    data: value
  };
};

export const SET_PAYMENT_GATEWAY_OLD = 'SET_PAYMENT_GATEWAY_OLD';
export const setPaymentGatewayOld = value => {
  return {
    type: SET_PAYMENT_GATEWAY_OLD,
    data: value
  };
};

export const ATTACH_NEUROLOGICAL_CERTIFICATE = 'ATTACH_NEUROLOGICAL_CERTIFICATE';
export const setAttachNeurologicalCertificate = value => {
  return {
    type: ATTACH_NEUROLOGICAL_CERTIFICATE,
    data: value
  };
};