/* eslint-disable no-param-reassign */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable import/no-unresolved */
/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Grid, Button, Fade } from '@material-ui/core';
import MuiAlert from '@material-ui/lab/Alert';
import useOnlineStatus from '@rehooks/online-status';
import { useSelector, useDispatch } from 'react-redux';

import { useProgress } from 'components';
import moment from 'moment';
import AlertInfo from '../assetsAndDiscounts/alertAssetsAndDiscounts';
import BeneficiaryCard from './beneficiary';
import CausantCard from './causant';
import CollectorCard from './collector';
import PensionCard from './pension';
import SettlementPaymentCard from './settlementPayment';
import useStyles from './style';
import DownloadPDF from '../pensionerDocuments/settlementPDF/download';
import Modal from 'react-bootstrap/Modal';
import 'bootstrap/dist/css/bootstrap.min.css';

import {
  businessDaysToUpdateTemporally,
  updatePensionerDataToPensionModel,
  updatePensionerDataToTemporalModel,
  updatePensionTypeToTemporalModel,
  uploadPdfFile,
  loadAFPList,
  loadBankBranchOffices,
  loadServipagBranchOffices
} from '../../services/queryPensions.service';

import {
  setWasModifiedAFieldThatRequiresAFile,
  setModifiedField,
  setWasPdfUploaded,
  setPdfFile,
  setDataSuccessfullyUpdated,
  setModifiedFieldErrors,
  setAFPList,
  setBankBranchOfficeList,
  setServipagBranchOfficeList,
  setBeneficiaryEmail,
  setPaymentGateway,
  setBank,
  setAccountNumber,
  setBranchOffice,
  setCollectorRut,
  setCollectorName,
  setCollectorLastName,
  setCollectorMothersLastName,
  setCollectorAddress,
  setCollectorCommune,
  setCollectorCity,
  setInstitutionalPatient,
  setAfpAffiliation,
  setPensionType,
  setWasInfoLoaded,
  setBeneficiaryPhone,
  setCun,
  setCountry,
  setAccidentDate,
  setAgreesToChangeCollectorRut,
  setIsFirstChangeInCollectorRut,
  setPensionTypeDueToCharges,
  setBankRejected,
  setPaycheckRefunded,
  setValidityType,
  setInactivationReason,
  setEndDateOfValidity,
  setEndDateOfTheoricalValidity,
  setEvaluationDate,
  setReactivateManually,
  setReactivationDate,
  setInactivationDate,
  setGender,
  setBasePension,
  setPaymentGatewayOld,
  setAttachNeurologicalCertificate
} from '../../actions';

import { paymentGatewayList } from '../../utils/paymentGatewayList';
import bankList from '../../utils/bankList';
import { transformData } from '../../utils/postFormatters';
import { checkWritePermission } from '../../../../utils/checkUserPermission';

const Alert = props => {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
};

const OVER_13_BUSINESS_DAY_MSG =
  'Las modificaciones realizadas a partir de hoy y hasta final del mes, se verán reflejadas el día 1 del próximo mes';
const OVER_7_BUSINESS_DAY_MSG =
  'Las modificaciones realizadas sobre el cambio de tipo de pensión, a partir de hoy y hasta final del mes, serán actualizadas en el sistema el día 1 del próximo mes';
const settlementPaymentAlertText =
  'Las modificaciones del Rechazo por el Banco y/o Vale Vista Integrado solo están disponibles para cambios desde el envío de la nómina bancaria hasta el último día del mes';

const filterOutPensionType = ({
  pensionType,
  ChangeOfPensionTypeDueToCharges,
  ...otherFields
} = {}) => {
  return { ...otherFields };
};
const alertModifiedField = (is7BusinessDay, is13BusinessDay) => {
  return (
    (is7BusinessDay || is13BusinessDay) && (
      <Fade in timeout={{ enter: 2000 }}>
        <Alert severity="info" variant="filled">
          {is13BusinessDay ? OVER_13_BUSINESS_DAY_MSG : OVER_7_BUSINESS_DAY_MSG}
        </Alert>
      </Fade>
    )
  );
};
const dateFormatter = date => {
  if (!date) return undefined;
  return moment(date, 'DD-MM-YYYY').format('DD-MM-YYYY');
};

const moneyFormatter = new Intl.NumberFormat('es-CL', {
  style: 'currency',
  currency: 'CLP',
  maximumFractionDigits: 2
});

const PensionerDetailTab = ({ data, setData, onErrorSnackbar, onSuccessSnackbar, userRole }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();
  const hasWritePermission = checkWritePermission(userRole);

  const [isEditable, setIsEditable] = useState(false);
  const [updateToTemporalModel, setUpdateToTemporalModel] = useState(false);
  const [updatePensionTypeToTemporary, setUpdatePensionTypeToTemporary] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [temporaryChanges, setTemporaryChanges] = useState({});

  const { beneficiaryRut, causantRut, pensionCodeId } = data;

  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);
  const successfulFileUpload = useSelector(store => store.queryPensions.successfulFileUpload);
  const pdfFile = useSelector(store => store.queryPensions.pensionerPdfFile);
  const wasModifiedAFieldThatRequiresAFile = useSelector(
    store => store.queryPensions.wasModifiedAFieldThatRequiresAFile
  );
  const dataSuccessfullyUpdated = useSelector(store => store.queryPensions.dataSuccessfullyUpdated);

  const beneficiaryEmail = useSelector(store => store.queryPensions.beneficiaryEmail);
  const beneficiaryPhone = useSelector(store => store.queryPensions.beneficiaryPhone);
  const cun = useSelector(store => store.queryPensions.cun);
  const basePension = useSelector(store => store.queryPensions.basePension);
  const gender = useSelector(store => store.queryPensions.gender);
  const country = useSelector(store => store.queryPensions.country);

  const [basePensionOriginal, setBasePensionOriginal] = useState(basePension);
  const [openModal, setOpenModal] = useState(true);

  const paymentGateway = useSelector(store => store.queryPensions.paymentGateway);
  const bank = useSelector(store => store.queryPensions.bank);
  const branchOffice = useSelector(store => store.queryPensions.branchOffice);
  const accountNumber = useSelector(store => store.queryPensions.accountNumber);
  const bankRejected = useSelector(store => store.queryPensions.bankRejected);
  const paycheckRefunded = useSelector(store => store.queryPensions.paycheckRefunded);

  const collectorRut = useSelector(store => store.queryPensions.collectorRut);
  const collectorName = useSelector(store => store.queryPensions.collectorName);
  const collectorLastName = useSelector(store => store.queryPensions.collectorLastName);
  const collectorMothersLastName = useSelector(
    store => store.queryPensions.collectorMothersLastName
  );
  const collectorAddress = useSelector(store => store.queryPensions.collectorAddress);
  const collectorCommune = useSelector(store => store.queryPensions.collectorCommune);
  const collectorCity = useSelector(store => store.queryPensions.collectorCity);

  const accidentDate = useSelector(store => store.queryPensions.accidentDate);
  const institutionalPatient = useSelector(store => store.queryPensions.institutionalPatient);
  const afpAffiliation = useSelector(store => store.queryPensions.afpAffiliation);
  const pensionType = useSelector(store => store.queryPensions.pensionType);
  const ChangeOfPensionTypeDueToCharges = useSelector(
    store => store.queryPensions.ChangeOfPensionTypeDueToCharges
  );
  const attachNeurologicalCertificate = useSelector(
    store => store.queryPensions.attachNeurologicalCertificate
  );
  const wasInfoLoaded = useSelector(store => store.queryPensions.wasInfoLoaded);
  const validityType = useSelector(store => store.queryPensions.validityType);
  const inactivationReason = useSelector(store => store.queryPensions.inactivationReason);
  const inactivationDate = useSelector(store => store.queryPensions.inactivationDate);
  const endDateOfValidity = useSelector(store => store.queryPensions.endDateOfValidity);
  const endDateOfTheoricalValidity = useSelector(
    store => store.queryPensions.endDateOfTheoricalValidity
  );
  const evaluationDate = useSelector(store => store.queryPensions.evaluationDate);
  const currentDate = useSelector(store => store.queryPensions.currentDate);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const [show, setShow] = useState(false);
  const handleClose = () => {
    setShow(false);
    setOpenModal(true);
  };

  const cancelEdit = async () => {
    setBasePensionOriginal(0);
    await Promise.all([dispatch(setModifiedFieldErrors({})), dispatch(setPdfFile({}))]);
    dispatch(setWasPdfUploaded(false));
    dispatch(setAgreesToChangeCollectorRut(false));
    dispatch(setIsFirstChangeInCollectorRut(true));
    dispatch(setModifiedField({}));
    dispatch(setWasModifiedAFieldThatRequiresAFile(false));
    dispatch(setAttachNeurologicalCertificate(false));
    setIsEditable(false);
    if (!data.paymentGateway || !paymentGatewayList.includes(data.paymentGateway)) {
      setInitialStateNoPaymentGateway();
      setFieldsNonRelatedToPaymentGateway();
      return;
    }
    setInitialStatePaymentGateway();
    setFieldsNonRelatedToPaymentGateway();
  };

  const moneySanitizer = (amount = '') => `${amount}`.replace('$', '').replaceAll('.', '');

  const moneySanitizerComa = (amount = '') => `${amount}`.replace(',', '.');

  const changeEditable = () => {
    if (basePensionOriginal === 0) {
      setBasePensionOriginal(data.basePension);
    }
    dispatch(setBasePension(moneySanitizer(data.basePension)));
    setIsEditable(true);
  };

  const saveModal = () => {
    save();
  };

  const save = async () => {
    const currentChanges = transformData({
      previousData: data,
      currentData: temporaryChanges,
      currentDate
    });
    if (
      moneySanitizer(currentChanges.basePension) !== moneySanitizer(data.basePension) &&
      openModal &&
      currentChanges.basePension
    ) {
      setOpenModal(false);
      setShow(true);
      return;
    }
    const isARealChangeChange = !!Object.keys(currentChanges).length;
    const dataWithoutPensionType = filterOutPensionType(currentChanges);
    const isAChangeDifferentFromPensionType = !!Object.keys(dataWithoutPensionType).length;

    const didPensionTypeChanged = currentChanges?.pensionType;

    let updateInfosuccess;
    let ftpError = false;

    const updateEverythingToPensionerModel = !updatePensionTypeToTemporary;
    const updateOnlyPensionTypeToTemporalModel =
      updatePensionTypeToTemporary && !updateToTemporalModel;
    const updateBothPensionTypeAndOtherChangesToTemporalModel = updateToTemporalModel;

    dispatch(setDataSuccessfullyUpdated(false));
    dispatch(setAgreesToChangeCollectorRut(false));
    dispatch(setIsFirstChangeInCollectorRut(true));

    setIsUploading(true);
    setIsEditable(false);
    progress.show();

    if (
      wasModifiedAFieldThatRequiresAFile &&
      !(
        paymentGateway === paymentGatewayList[8] &&
        pdfFile &&
        pdfFile.name === 'Dictamen_de_tribunales.pdf'
      )
    ) {
      const { isError: isftpError } = await uploadPdfFile({
        pdfFile,
        beneficiaryRut,
        causantRut,
        pensionCodeId
      });
      ftpError = isftpError;
    }
    if (updateEverythingToPensionerModel && !ftpError) {
      const { isError } = isARealChangeChange
        ? await updatePensionerDataToPensionModel(currentChanges)
        : {};
      updateInfosuccess = !isError;
    }

    if (updateOnlyPensionTypeToTemporalModel && !ftpError) {
      const { isError } = isAChangeDifferentFromPensionType
        ? await updatePensionerDataToPensionModel(dataWithoutPensionType)
        : {};
      const { isError: isErrorPensionType } = didPensionTypeChanged
        ? await updatePensionTypeToTemporalModel(data, pensionType, ChangeOfPensionTypeDueToCharges)
        : {};
      updateInfosuccess = !isError && !isErrorPensionType;
    }

    if (updateBothPensionTypeAndOtherChangesToTemporalModel && !ftpError) {
      const { isError } = isAChangeDifferentFromPensionType
        ? await updatePensionerDataToTemporalModel(dataWithoutPensionType)
        : {};
      const { isError: isErrorPensionType } = didPensionTypeChanged
        ? await updatePensionTypeToTemporalModel(data, pensionType, ChangeOfPensionTypeDueToCharges)
        : {};
      updateInfosuccess = !isError && !isErrorPensionType;
    }

    if (updateInfosuccess) {
      setTemporaryChanges({
        ...temporaryChanges,
        ...currentChanges,
        gender,
        country,
        accidentDate: dateFormatter(accidentDate),
        endDateOfValidity: dateFormatter(endDateOfValidity),
        endDateOfTheoricalValidity: dateFormatter(endDateOfTheoricalValidity),
        basePension: moneyFormatter.format(moneySanitizerComa(basePension))
      });
      setIsEditable(false);
      dispatch(setWasPdfUploaded(false));
      dispatch(setWasModifiedAFieldThatRequiresAFile(false));
      dispatch(setPaymentGatewayOld(paymentGateway));
      progress.hide();
      setIsUploading(false);
      dispatch(setDataSuccessfullyUpdated(true));
      setOpenModal(true);
      setShow(false);
      dispatch(setAttachNeurologicalCertificate(false));
      return onSuccessSnackbar('Los cambios realizados se guardaron correctamente');
    }
    setIsUploading(false);
    setIsEditable(true);
    setShow(false);
    progress.hide();
    dispatch(setDataSuccessfullyUpdated(false));

    return onErrorSnackbar('Los cambios realizados no se guardaron correctamente');
  };

  const setFieldsNonRelatedToPaymentGateway = () => {
    dispatch(setBeneficiaryPhone(data.beneficiaryPhone));
    dispatch(setBeneficiaryEmail(data.beneficiaryEmail));
    dispatch(setCun(data.cun));
    dispatch(setGender(data.gender));
    dispatch(setCountry(data.country));
    dispatch(setAccidentDate(data.accidentDate));

    dispatch(setCollectorRut(data.collectorRut));
    dispatch(setCollectorName(data.collectorName));
    dispatch(setCollectorLastName(data.collectorLastName));
    dispatch(setCollectorMothersLastName(data.collectorMothersLastName));
    dispatch(setCollectorAddress(data.collectorAddress));
    dispatch(setCollectorCommune(data.collectorCommune));
    dispatch(setCollectorCity(data.collectorCity));

    dispatch(setPensionType(data.pensionType));
    dispatch(setInstitutionalPatient(data.institutionalPatient));
    dispatch(setAfpAffiliation(data.afpAffiliation));
    dispatch(setPensionTypeDueToCharges(data.ChangeOfPensionTypeDueToCharges));
    dispatch(setValidityType(data.validityType));
    dispatch(setInactivationReason(data.inactivationReason));
    dispatch(setInactivationDate(data.inactivationDate));
    dispatch(setEndDateOfValidity(data.endDateOfValidity));
    dispatch(setEndDateOfTheoricalValidity(data.endDateOfTheoricalValidity));
    dispatch(setEvaluationDate(data.evaluationDate));
    dispatch(setReactivateManually(data.reactivateManually));
    dispatch(setReactivationDate(data.reactivationDate));
    dispatch(setBasePension(data.basePension));
  };

  const setInitialStateNoPaymentGateway = () => {
    dispatch(setPaymentGateway(paymentGatewayList[0]));
    dispatch(setBank(bankList[0]));
    dispatch(setAccountNumber(''));
    dispatch(setBranchOffice(data.branchOffice));
    dispatch(
      setModifiedFieldErrors({
        ...modifiedFieldErrors,
        accountNumber: true,
        bank: true,
        paymentGateway: true
      })
    );
    dispatch(setBankRejected(data.bankRejected));
    dispatch(setPaycheckRefunded(data.paycheckRefunded));
  };

  const setInitialStatePaymentGateway = () => {
    dispatch(setPaymentGateway(data.paymentGateway));
    dispatch(setBank(data.bank));
    dispatch(setAccountNumber(data.accountNumber));
    dispatch(setBranchOffice(data.branchOffice));
    dispatch(setBankRejected(data.bankRejected));
    dispatch(setPaycheckRefunded(data.paycheckRefunded));
  };

  useEffect(() => {
    progress.hide();
    businessDaysToUpdateTemporally(setUpdateToTemporalModel, setUpdatePensionTypeToTemporary);
    loadAFPList(dispatch, setAFPList);
    loadBankBranchOffices(dispatch, setBankBranchOfficeList);
    loadServipagBranchOffices(dispatch, setServipagBranchOfficeList);
    dispatch(setWasPdfUploaded(false));
    dispatch(setPdfFile({}));
    dispatch(setWasModifiedAFieldThatRequiresAFile(false));
    setIsEditable(false);
    dispatch(setDataSuccessfullyUpdated(false));

    if (!data.paymentGateway || !paymentGatewayList.includes(data.paymentGateway)) {
      setInitialStateNoPaymentGateway();
      setFieldsNonRelatedToPaymentGateway();
      dispatch(setWasInfoLoaded(true));
      return;
    }
    setInitialStatePaymentGateway();
    setFieldsNonRelatedToPaymentGateway();
    dispatch(setWasInfoLoaded(true));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (dataSuccessfullyUpdated) {
      setData({ ...data, ...temporaryChanges, pensionType, ChangeOfPensionTypeDueToCharges });
    }
  }, [dataSuccessfullyUpdated]);

  useEffect(() => {
    setTemporaryChanges({
      beneficiaryEmail,
      collectorRut,
      collectorName,
      collectorLastName,
      collectorMothersLastName,
      collectorAddress,
      collectorCommune,
      collectorCity,
      paymentGateway,
      bank,
      branchOffice,
      accountNumber,
      institutionalPatient,
      afpAffiliation,
      pensionType,
      ChangeOfPensionTypeDueToCharges,
      beneficiaryPhone,
      cun,
      gender,
      country,
      accidentDate,
      bankRejected,
      paycheckRefunded,
      validityType,
      inactivationReason,
      endDateOfValidity,
      endDateOfTheoricalValidity,
      inactivationDate,
      evaluationDate,
      basePension
    });
  }, [
    beneficiaryEmail,
    collectorRut,
    collectorName,
    collectorLastName,
    collectorMothersLastName,
    collectorAddress,
    collectorCommune,
    collectorCity,
    paymentGateway,
    bank,
    branchOffice,
    accountNumber,
    institutionalPatient,
    afpAffiliation,
    pensionType,
    ChangeOfPensionTypeDueToCharges,
    beneficiaryPhone,
    cun,
    gender,
    country,
    accidentDate,
    bankRejected,
    paycheckRefunded,
    validityType,
    inactivationDate,
    inactivationReason,
    endDateOfValidity,
    endDateOfTheoricalValidity,
    evaluationDate,
    basePension
  ]);

  const disableSave = () => {
    if (Object.keys(modifiedFieldErrors).some(key => modifiedFieldErrors[key])) return true;
    if (
      (institutionalPatient !== data.institutionalPatient ||
        afpAffiliation !== data.afpAffiliation ||
        collectorRut !== data.collectorRut ||
        (/Si/i.test(institutionalPatient) && attachNeurologicalCertificate)) &&
      !successfulFileUpload
    )
      return true;
    return false;
  };

  return (
    wasInfoLoaded && (
      <div style={{ overflowX: 'scroll' }}>
        <>
          <Modal show={show} onHide={handleClose} backdrop="static" keyboard={false}>
            <Modal.Header closeButton>
              <Modal.Title>Modificación de "Pensión Base"</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              ¿Estás seguro que quieres cambiar la pensión base de{' '}
              <strong> {basePensionOriginal}</strong> a{' '}
              <strong>{moneyFormatter.format(moneySanitizerComa(basePension))}</strong> ?
            </Modal.Body>
            <Modal.Footer>
              <Grid item>
                <Button className={classes.cancelButton} onClick={handleClose}>
                  Cancelar
                </Button>
              </Grid>
              <Grid item>
                <Button className={classes.acceptButton} onClick={saveModal}>
                  Aceptar
                </Button>
              </Grid>
            </Modal.Footer>
          </Modal>
        </>
        <Grid container spacing={1} className={classes.gridStyle} justify="flex-end">
          <Grid item>
            <Button
              className={classes.editSave}
              variant="contained"
              onClick={cancelEdit}
              size="small"
              color="primary"
              disabled={!hasWritePermission || !onlineStatus || !isEditable || isUploading}
            >
              Cancelar
            </Button>
          </Grid>
          <Grid item>
            <Button
              className={classes.editSave}
              variant="contained"
              onClick={changeEditable}
              size="small"
              color="primary"
              disabled={!hasWritePermission || !onlineStatus || isEditable || isUploading}
            >
              Editar
            </Button>
          </Grid>
          <Grid item>
            <Button
              disabled={!hasWritePermission || !isEditable || disableSave() || isUploading}
              className={classes.editSave}
              onClick={save}
              variant="contained"
              size="small"
              color="primary"
            >
              Guardar
            </Button>
          </Grid>
        </Grid>
        <DownloadPDF />
        <Grid>{alertModifiedField(updatePensionTypeToTemporary, updateToTemporalModel)}</Grid>
        <Grid container spacing={2}>
          <Grid item className={classes.gridStyle}>
            <BeneficiaryCard values={data} editable={isEditable} />
          </Grid>
          <Grid item className={classes.gridStyle}>
            <AlertInfo
              alertClass={classes.alertBar}
              severity="error"
              color="info"
              alertText={settlementPaymentAlertText}
            />
            <SettlementPaymentCard values={data} editable={isEditable} />
          </Grid>
        </Grid>
        <Grid container spacing={2}>
          <Grid item className={classes.gridStyle}>
            <CausantCard values={data} editable={isEditable} />
          </Grid>
          <Grid item className={classes.gridStyle}>
            <CollectorCard
              values={data}
              editable={isEditable}
              onErrorSnackbar={onErrorSnackbar}
              onSuccessSnackbar={onSuccessSnackbar}
            />
          </Grid>
        </Grid>
        <Grid container>
          <Grid item className={classes.gridStyle}>
            <PensionCard
              values={data}
              editable={isEditable}
              onErrorSnackbar={onErrorSnackbar}
              onSuccessSnackbar={onSuccessSnackbar}
              isAfter7BusinessDay={updatePensionTypeToTemporary}
            />
          </Grid>
        </Grid>
      </div>
    )
  );
};

PensionerDetailTab.propTypes = {
  data: PropTypes.shape({}).isRequired
};

export default PensionerDetailTab;
