/* eslint-disable no-useless-escape */
import moment from 'moment';

const placesBetweenDots = 3;
const maxLength = 12;
const lengthToIncludeAfterDash = 2;

const RUT_DYNAMIC_PATTERN = /^\d{1,2}\.?\d{3}\.?\d{3}-?([0-9]{1,2}|[0-9][kK]?)$/;
const RUT_PATTERN = /^(\d{1,2})\.?(\d{3})\.?(\d{3})-([0-9kK])$/;
const ALPHANUMERIC_DIACRITICS_PUNCTUATIONMARK = /^([0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûçğş',´`^¨~\-\.]+\s?)+$/i;
const LETTERS_DIACRITICS_PUNTUATIONMARK = /^([a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûçğş',´`^¨~\-\.]+\s?)+$/i;
const LETTERS_DIACRITICS = /^[a-zA-Z\u00C0-\u00FF]*$/;
const EMAIL_PATTERN = /(^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$)/i;
const NUMERIC_ONLY = /^[0-9]*$/i;
const PHONE_REGEX = /^\d{9}$/i;
const ALPHANUMERIC = /^[a-z0-9]+$/i;
const CHILEAN_AMOUNT_PATTERN = /^\d+(,\d{1,2})?$/;

const formatChileanAmount = chileanAmount =>
  chileanAmount
    .replace(/\$/g, '')
    .replace(/^0{2,}/, '0')
    .replace(/[^\d,]/g, '')
    .replace(/[,](?=.*[,])/gi, '')
    .replace(/(\d+)(,\d{1,2})?\d*/, '$1$2');

const alphaNumericSanitizer = (value = '') => value.replace(/[^a-z0-9]/gi, '');

const alphaNumericPunctuationMarkSanitizer = (value = '') =>
  value
    .replace(/^\s|[^0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûçğş',´`^¨~\-\.\s]/gi, '')
    .replace(/\s+/g, ' ');
const letterDiacriticsPunctuationMarkSanitizer = (value = '') =>
  value
    .replace(/^\s|[^a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûçğş',´`^¨~\-\.\s]/gi, '')
    .replace(/\s+/g, ' ');
const letterDiacriticsSanitizer = (value = '') =>
  value.replace(/^\s|[^a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûçğş´`^¨\s]/gi, '').replace(/\s+/g, ' ');

const emailFormatter = (value = '') =>
  value.replace(/^\s|[^a-z0-9@-_\.]/gi, '').replace(/\s+/g, ' ');

const numericOnlySanitizer = (value = '') => value.replace(/[^0-9]/g, '');

const formatRut = rut =>
  rut
    .replace(/[^\dkK]/g, '')
    .replace(/([0-9kK])$/, '-$1')
    .replace(/[k](?=.*[k])/gi, '')
    .replace(RUT_PATTERN, '$1.$2.$3-$4')
    .toUpperCase();

const rutWithDots = rut =>{  
  return new Intl.NumberFormat('es-CL', {  
    maximumFractionDigits: 0
  }).format(`${rut}`
      .replace(/^0+/, '')
      .replace(/\D/g, ''));  
  };

const removeEverythingAfter = (string, charToLookup) => {
  const placeOfChar = string.indexOf(charToLookup);
  if (placeOfChar === -1) return string;
  return string.substring(0, placeOfChar + lengthToIncludeAfterDash);
};

const doesRutMatch = rut => RUT_DYNAMIC_PATTERN.test(rut) || RUT_PATTERN.test(rut);

const dynamicFormatting = rut => {
  if (doesRutMatch(rut) && rut.length <= maxLength) {
    return formatRut(rut);
  }
  if (rut.length <= maxLength - placesBetweenDots - 1) return rutWithDots(rut);
  if (rut.length > maxLength) return formatRut(rut.substring(0, rut.length - 1));
  const noAfterHyphen = removeEverythingAfter(rut, '-').replace(/[^\dkK]/g, '');
  return noAfterHyphen.length >= maxLength - placesBetweenDots - 1
    ? formatRut(noAfterHyphen)
    : rutWithDots(noAfterHyphen);
};

const unformatRut = rut => rut.replace(/\./g, '');

const checkDigitValidation = rutWithDV => {
  const [rut, currentDV] = rutWithDV.replace(/\./, '').split('-');
  const module = 11;
  const multipliers = '234567'.split('').map(x => +x);
  const sumOfElements = rut
    .replace(/[^\d]/g, '')
    .split('')
    .reverse()
    .map((value, index) => +value * multipliers[index % multipliers.length])
    .reduce((accumulator, currentValue) => accumulator + currentValue);
  let actualDV = module - (sumOfElements % module);
  if (actualDV === module - 1) {
    actualDV = 'k';
    return actualDV === currentDV.toLowerCase();
  }
  if (actualDV === module) actualDV = 0;
  return actualDV === +currentDV;
};

const pensionCodeIdFormatter = codeId => codeId.replace(/\D/, '');

const isValidPensionCodeID = codeId => /^\d+$/.test(codeId);

const formatDate = date => (date ? moment(date).format('DD-MM-YYYY') : '');

const formatInput = input => input.replace(/[^0-9kK-]/g, '').replace(/^0+/g, '');

const checkRutAndDV = rut => RUT_PATTERN.test(rut) && checkDigitValidation(rut);

export {
  formatInput,
  dynamicFormatting,
  formatDate,
  formatRut,
  pensionCodeIdFormatter,
  isValidPensionCodeID,
  unformatRut,
  checkDigitValidation,
  checkRutAndDV,
  RUT_PATTERN,
  ALPHANUMERIC_DIACRITICS_PUNCTUATIONMARK,
  LETTERS_DIACRITICS_PUNTUATIONMARK,
  LETTERS_DIACRITICS,
  alphaNumericPunctuationMarkSanitizer,
  letterDiacriticsPunctuationMarkSanitizer,
  letterDiacriticsSanitizer,
  EMAIL_PATTERN,
  emailFormatter,
  NUMERIC_ONLY,
  numericOnlySanitizer,
  PHONE_REGEX,
  ALPHANUMERIC,
  alphaNumericSanitizer,
  CHILEAN_AMOUNT_PATTERN,
  formatChileanAmount
};
