//------------------------------------------------------------------------------------------------
export const LINK_PENSIONS = 'LINK_PENSIONS';
export const linkPensions = isEnabled => {
  return {
    type: LINK_PENSIONS,
    data: { isLinked: isEnabled }
  };
};

//------------------------------------------------------------------------------------------------
export const GET_BENEFICIARIES_PENSIONS = 'GET_BENEFICIARIES_PENSIONS';
export const existingBeneficiaries = results => {
  return {
    type: GET_BENEFICIARIES_PENSIONS,
    data: { pensions: results || [] }
  };
};

//------------------------------------------------------------------------------------------------
export const IS_PENSIONS_AVAILABLE = 'IS_AVAILABLE_PENSIONS';
export const isPensionsAvailable = ({ isLinked, actionIsAllowed, inDayRange, totalDays }) => {
  return {
    type: IS_PENSIONS_AVAILABLE,
    data: { isLinked, actionIsAllowed, inDayRange, TOTAL_AVAILABLE_DAY: totalDays }
  };
};
