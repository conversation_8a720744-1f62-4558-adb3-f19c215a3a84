import React from 'react';
import PropTypes from 'prop-types';
import { Ty<PERSON><PERSON>, CardHead<PERSON>, <PERSON>, Button } from '@material-ui/core';
import useRouter from '../../../../../utils/useRouter';
import { useDispatch } from 'react-redux';
import useStyles from '../styles';
import ResumeError from './resumeError';
import { cleanFileError } from '../../actions';

const Resume = props => {
  const router = useRouter();
  const { fileDataError, isError, linkProps } = props;
  const classes = useStyles();
  const dispatch = useDispatch();

  const handleOnClick = () => {
    dispatch(cleanFileError());
    router.history.push('/mantenedores/asignacion-aguinaldos');
  };

  const handleResume = err =>
    err && (
      <>
        <div className={classes.principalErrors}>
          <Typography>
            <b>Existen errores en el archivo que impidieron su importación</b>
          </Typography>
        </div>
        <ResumeError
          errors={fileDataError.errors}
          requireFields={`RUT Pensionado, Paga Aguinaldo, Código de institución, Monto de otra pensión`}
        />
      </>
    );

  return (
    <Card className={classes.content}>
      <CardHeader
        title="Resumen de importación Asignación Aguinaldos"
        className={classes.headerImport}
      />
      {handleResume(isError)}

      {!linkProps && (
        <div className={classes.footerButton}>
          <Button onClick={handleOnClick} className={classes.accept}>
            Aceptar
          </Button>
        </div>
      )}
    </Card>
  );
};

Resume.propTypes = {
  fileDataError: PropTypes.object,
  isError: PropTypes.bool
};
Resume.defaultProps = {
  fileDataError: { errors: [] },
  isError: false
};

export default Resume;
