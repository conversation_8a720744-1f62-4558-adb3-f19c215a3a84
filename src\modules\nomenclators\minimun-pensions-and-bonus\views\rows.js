import React from 'react';
import PropTypes from 'prop-types';
import { TableCell, TableRow, FormLabel } from '@material-ui/core';
import InputTextField from './textField';

const formatDecimal = number => {
  const fixedNumber = number && number !== undefined ? number.replace(',', '.') : number;
  return fixedNumber
    ? `$ ${new Intl.NumberFormat(['es', 'de'], {
        maximumFractionDigits: 2
      }).format(fixedNumber)}`
    : '';
};

const Rows = ({
  id,
  type,
  pensionType,
  age,
  isEditable,
  rowsValues,
  handleOnChange,
  hasError,
  handleError
}) => {
  const { minimun, law19403, law19539, law19953 } = rowsValues;

  return (
    <TableRow hover key={id}>
      <TableCell>{pensionType}</TableCell>
      <TableCell>{age}</TableCell>
      <TableCell>
        {isEditable ? (
          <InputTextField
            id={id}
            name="minimun"
            valueType={type}
            value={minimun || ''}
            handleError={handleError}
            handleOnChange={handleOnChange}
            valueIsObject
          />
        ) : (
          <FormLabel>{formatDecimal(minimun)}</FormLabel>
        )}
      </TableCell>
      <TableCell>
        {isEditable && law19403 !== undefined ? (
          <InputTextField
            id={id}
            name="law19403"
            valueType={type}
            value={law19403 || ''}
            handleError={handleError}
            handleOnChange={handleOnChange}
            valueIsObject
          />
        ) : (
          <FormLabel>{formatDecimal(law19403)}</FormLabel>
        )}
      </TableCell>
      <TableCell>
        {isEditable && law19539 !== undefined ? (
          <InputTextField
            id={id}
            name="law19539"
            valueType={type}
            value={law19539 || ''}
            handleError={handleError}
            handleOnChange={handleOnChange}
            valueIsObject
          />
        ) : (
          <FormLabel>{formatDecimal(law19539)}</FormLabel>
        )}
      </TableCell>
      <TableCell>
        {isEditable && law19953 !== undefined ? (
          <InputTextField
            id={id}
            name="law19953"
            valueType={type}
            value={law19953 || ''}
            handleError={handleError}
            handleOnChange={handleOnChange}
            valueIsObject
          />
        ) : (
          <FormLabel>{formatDecimal(law19953)}</FormLabel>
        )}
      </TableCell>
    </TableRow>
  );
};

Rows.propTypes = {
  id: PropTypes.string.isRequired,
  type: PropTypes.string.isRequired,
  pensionType: PropTypes.string.isRequired,
  age: PropTypes.string.isRequired,
  isEditable: PropTypes.bool.isRequired,
  rowsValues: PropTypes.objectOf(PropTypes.string).isRequired,
  handleOnChange: PropTypes.func.isRequired,
  hasError: PropTypes.oneOfType([PropTypes.bool, PropTypes.object]).isRequired,
  handleError: PropTypes.func.isRequired
};

export default Rows;
