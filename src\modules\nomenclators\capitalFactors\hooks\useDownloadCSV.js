import { setIsDownloading } from '../actions';

const ON_ERROR_TEXT = 'Hubo un error con la descarga del archivo';
const downloadFiles = async ({ dispatch, progress, onErrorSnackbar, service, filename }) => {
  let success;
  progress.show();
  dispatch(setIsDownloading(true));
  await service().then(({ isError, data }) => {
    success = !isError;
    if (success) {
      const url = window.URL.createObjectURL(data);
      const a = document.createElement('a');
      a.setAttribute('download', `${filename}.csv`);
      a.href = url;
      a.click();
    }
  });
  if (!success) {
    dispatch(setIsDownloading(false));
    onErrorSnackbar(`${ON_ERROR_TEXT}`);
    progress.hide();
    return false;
  }
  dispatch(setIsDownloading(false));
  progress.hide();
  return true;
};

const useDownloadCSV = ({ dispatch, progress, onErrorSnackbar, service, filename }) => {
  return () => downloadFiles({ dispatch, progress, onErrorSnackbar, service, filename });
};

export default useDownloadCSV;
