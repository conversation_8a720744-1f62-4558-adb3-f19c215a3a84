import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { TextValidator, ValidatorForm } from 'react-material-ui-form-validator';
import { rutMatchRule, percentageMatchRule, codeMatchRule } from '../../validator/validField';

const word = '0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäöüëïâêîôûçğş';
const regex = `^[${word}\\s\\.\\-',]+$`;
const regRule = new RegExp(regex, 'i');
const matchRule = (value = '') => regRule.test(value);
const SecureTextField = ({
  name,
  displayName,
  type,
  isValidListener,
  validators,
  errorMessages,
  inputprops,
  ...props
}) => {
  useEffect(() => {
    ValidatorForm.addValidationRule('matchCode', codeMatchRule);
    ValidatorForm.addValidationRule('match', matchRule);
    ValidatorForm.addValidationRule('matchRut', rutMatchRule);
    ValidatorForm.addValidationRule('matchPercentage', percentageMatchRule);
    // returned function will be called on component unmount
    return () => {
      ValidatorForm.removeValidationRule('matchCode');
      ValidatorForm.removeValidationRule('match');
      ValidatorForm.removeValidationRule('matchRut');
      ValidatorForm.removeValidationRule('matchPercentage');
    };
  }, []);
  return (
    <>
      <TextValidator
        name={name}
        label={displayName}
        type={type}
        validators={validators}
        errorMessages={errorMessages}
        variant="outlined"
        inputProps={{ ...inputprops }}
        validatorListener={isValidListener}
        {...props}
      />
    </>
  );
};
SecureTextField.propTypes = {
  name: PropTypes.string.isRequired,
  displayName: PropTypes.string.isRequired,
  type: PropTypes.oneOf(['text', 'number', 'email', 'date']),
  isValidListener: PropTypes.func,
  validators: PropTypes.arrayOf(PropTypes.string),
  errorMessages: PropTypes.arrayOf(PropTypes.string),
  inputprops: PropTypes.objectOf(PropTypes.string).isRequired
};
SecureTextField.defaultProps = {
  type: 'text',
  isValidListener: () => { /*any*/ },
  validators: ['required', 'match'],
  errorMessages: ['El valor es requerido', 'El valor es inválido']
};

export { matchRule, rutMatchRule, codeMatchRule };
export default SecureTextField;
