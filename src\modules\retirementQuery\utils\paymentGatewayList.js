const paymentGatewayList = [
  'Seleccione vía de pago',
  'Depósito cuenta corriente Banco de Chile',
  'Depósito cuenta corriente otros bancos',
  'Depósito cuenta de ahorro otros bancos',
  'Vale vista banco de Chile',
  'Vale vista entregado directamente a la empresa',
  'Cheque electrónico Banco de Chile',
  'SERVIPAG',
  'Pago retenido institucional',
  'Pago retenido no institucional'  
];

const bankOnlyRelatedOptions = [
  'Depósito cuenta corriente Banco de Chile',
  'Depósito cuenta corriente otros bancos',
  'Depósito cuenta de ahorro otros bancos'
];

const bankRelatedPaymentGateway = [
  'Depósito cuenta corriente Banco de Chile',
  'Depósito cuenta corriente otros bancos',
  'Depósito cuenta de ahorro otros bancos',
  'Cheque electrónico Banco de Chile'
];

const voucherBankRelatedOptions = [
  'Vale vista banco de Chile',
  'Vale vista entregado directamente a la empresa'
];

const voucherServipagRelatedOptions = ['SERVIPAG'];

const branchOfficeRelatedFields = [...voucherBankRelatedOptions, ...voucherServipagRelatedOptions];

const electronicCheckBancoDeChile = ['Cheque electrónico Banco de Chile'];

export {
  paymentGatewayList,
  bankRelatedPaymentGateway,
  bankOnlyRelatedOptions,
  voucherBankRelatedOptions,
  voucherServipagRelatedOptions,
  branchOfficeRelatedFields,
  electronicCheckBancoDeChile
};
