/* eslint-disable no-console */
/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import { Grid } from '@material-ui/core';
import { HeaderContent, Page } from 'components';
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { deleteNomenclatorMotive } from '../actions';
import { loadMotive, processMotive } from '../services';
import { MotiveTable } from './components';
import useStyles from './styles';

const MotiveNomenclatorPage = props => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const [isLoading, setLoading] = useState(true);
  const [enabledButton, setEnabledButton] = useState(false);
  const state = {
    data: useSelector(store => store.motive.data)
  };
  const { onSuccessSnackbar, onErrorSnackbar, role } = props;

  const CONFLICT = 409;
  const handleConflict = oldData => !oldData.motive && dispatch(deleteNomenclatorMotive(oldData));

  const keyValue = { motive: 'Motivo', option: 'Haber/Descuento' };
  const handleTranslate = key =>
    keyValue[key]
      ? `El ${keyValue[key]} que intenta guardar ya existe`
      : `El registro que intenta guardar ya existe`;
  const handleLoad = () => {
    return !isLoading
      ? state.data
      : async () => {
          return dispatch(loadMotive()).then(motive => {
            setLoading(false);
            return {
              data: motive,
              totalCount: motive.length
            };
          });
        };
  };
  const handleProcess = (okMessage, erroMessage, operation) => async (newData, oldData) => {
    try {
      if (operation === 'create' || (operation === 'update' && oldData) || operation === 'delete') {
        const result = await dispatch(processMotive(oldData, newData, operation))
          .then(() => {
            onSuccessSnackbar(okMessage);
            return true;
          })
          .catch(error => {
            let messageError;
            if (error.response.status === CONFLICT) {
              const key = Object.keys(error.response.data.error.keyValue);
              if (operation === 'update') handleConflict(oldData);
              messageError = handleTranslate(key);
              onErrorSnackbar(messageError);
              setEnabledButton(true);
            } else {
              messageError = erroMessage;
              onErrorSnackbar(messageError);
              setEnabledButton(true);
            }
            throw new Error(messageError);
          });
        if (!result) return false;
      }
      return true;
    } catch ({ message }) {
      onErrorSnackbar(message);
      setEnabledButton(true);
      throw new Error(message);
    }
  };

  return (
    <Page className={classes.root} title="Motivos">
      <Grid container justify="space-between" alignItems="center" className={classes.headerContent}>
        <HeaderContent overline="Configuraciones / Mantenedores" title="Motivos" />
      </Grid>
      <MotiveTable
        userRole={role}
        enabledButton={enabledButton}
        data={handleLoad()}
        onCreate={handleProcess(
          'Los valores se guardaron correctamente',
          'Error en actualización de registro',
          'create'
        )}
        onUpdate={handleProcess(
          'Los valores se guardaron correctamente',
          'Error en actualización de registro',
          'update'
        )}
        onDelete={handleProcess('Registro eliminado', 'Error en eliminación de registro', 'delete')}
      />
    </Page>
  );
};

MotiveNomenclatorPage.propTypes = {
  onSuccessSnackbar: PropTypes.func.isRequired,
  onErrorSnackbar: PropTypes.func.isRequired
};

// eslint-disable-next-line import/prefer-default-export
export { MotiveNomenclatorPage };
