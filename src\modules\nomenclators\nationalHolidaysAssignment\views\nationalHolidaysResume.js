import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import Resume from './resume';
import useRouter from 'utils/useRouter';

const NationalHolidaysResume = _props => {
  const router = useRouter();
  const data = useSelector(store => store.nationalHolidays.data);
  const isError = useSelector(store => store.nationalHolidays.isError);
  const errors = useSelector(store => store.nationalHolidays.errors);

  useEffect(() => {
    if (!isError) {
      router.history.push('mantenedores/asignacion-aguinaldos');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isError]);

  return <Resume data={data} isError={isError} fileDataError={{ errors }} />;
};

export default NationalHolidaysResume;
