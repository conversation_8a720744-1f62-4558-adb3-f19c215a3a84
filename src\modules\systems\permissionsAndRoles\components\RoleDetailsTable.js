/* eslint-disable react-hooks/exhaustive-deps */
import React, { forwardRef, useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';

import useOnlineStatus from '@rehooks/online-status';

import MaterialTable, { MTableEditRow } from 'material-table';
import { Checkbox, Switch, Tooltip, IconButton } from '@material-ui/core';
import { ArrowDownward, Clear, Check, DeleteOutline, Edit } from '@material-ui/icons';

import { getRole } from '../services/roles.service';
import { checkWritePermission } from '../../../../utils/checkUserPermission';

const NO_READ = 'NoRead';
const READ = 'Read';
const READ_WRITE = 'ReadWrite';
const HOME = /^home$/i;

const checkBoxStyle = {
  color: 'blue'
};
const inactiveBoxStyle = {
  color: 'gray'
};

// eslint-disable-next-line react/prop-types
const ActiveInactive = (state, setState, isFirstChange, setIsFirstChange, onlineStatus) => ({
  value = '',
  onChange
}) => {
  if (isFirstChange || !state) setState(value);
  setIsFirstChange(false);

  return (
    <Switch
      style={state !== NO_READ && onlineStatus ? checkBoxStyle : inactiveBoxStyle}
      checked={state !== NO_READ}
      onClick={() => {
        if ([READ, READ_WRITE].includes(state)) return setState(NO_READ);
        return setState(READ);
      }}
      disabled={!onlineStatus}
      color="primary"
      onChange={e => onChange(e.target.checked)}
    />
  );
};

const ReadOrNot = (state, setState, onlineStatus) => ({ onChange, value = '' }) => {
  return (
    <Checkbox
      color="primary"
      style={state !== NO_READ && onlineStatus ? checkBoxStyle : inactiveBoxStyle}
      disabled={state === NO_READ || !onlineStatus}
      checked={[READ, READ_WRITE].includes(state)}
      onClick={() => {
        if ([READ, READ_WRITE].includes(state)) return setState(NO_READ);
        return setState(READ);
      }}
      onChange={e => onChange(e.target.checked)}
    />
  );
};

const ReadWrite = (state, setState, onlineStatus) => ({ onChange, value = '' }) => {
  return (
    <Checkbox
      color="primary"
      style={state !== NO_READ && onlineStatus ? checkBoxStyle : inactiveBoxStyle}
      disabled={state === NO_READ || !onlineStatus}
      checked={state === READ_WRITE}
      onClick={() => {
        if (state === READ_WRITE) return setState(READ);
        return setState(READ_WRITE);
      }}
      onChange={e => onChange(e.target.checked)}
    />
  );
};

const tableIcons = {
  Check: forwardRef((props, ref) => <Check {...props} ref={ref} />),
  Edit: forwardRef((props, ref) => <Edit {...props} ref={ref} />),
  Delete: forwardRef((props, ref) => <DeleteOutline {...props} ref={ref} />),
  Clear: forwardRef((props, ref) => <Clear {...props} ref={ref} />),
  SortArrow: forwardRef((props, ref) => <ArrowDownward {...props} ref={ref} />)
};

const RoleDetails = ({ role, onUpdate, userRole }) => {
  const onlineStatus = useOnlineStatus();
  const tableRef = useRef();

  const [viewPrivileges, setViewPrivileges] = useState([]);
  const [interactingState, setInteractingState] = useState('');
  const [useInitialState, setUseInitialState] = useState(true);

  const SwitchFn = ActiveInactive(
    interactingState,
    setInteractingState,
    useInitialState,
    setUseInitialState,
    onlineStatus
  );
  const readProperty = ReadOrNot(interactingState, setInteractingState, onlineStatus);
  const readWriteProperty = ReadWrite(interactingState, setInteractingState, onlineStatus);
  const hasWritePermission = checkWritePermission(userRole);

  useEffect(() => {
    getRole(role, setViewPrivileges);
  }, []);

  return (
    <MaterialTable
      tableRef={tableRef}
      components={{
        Action: p => {
          let { action } = p;
          if (typeof p.action === 'function') {
            action = p.action();
          }

          return (
            <Tooltip title={!action.disabled && !p.disabled ? action.tooltip : ''}>
              <IconButton
                onClick={event => {
                  action.onClick(event, p.data);
                }}
                color="inherit"
                variant="contained"
                size={p.size}
                disabled={
                  !hasWritePermission ||
                  action.disabled ||
                  p.disabled ||
                  !onlineStatus ||
                  HOME.test(`${p?.data?.view?.view}`)
                }
              >
                {action.icon.render()}
              </IconButton>
            </Tooltip>
          );
        },
        EditRow: p => (
          <MTableEditRow
            {...p}
            onEditingCanceled={(mode, rowData) => {
              setUseInitialState(true);
              return p.onEditingCanceled(mode, rowData);
            }}
            onEditingApproved={async (mode, newData, oldData) => {
              setUseInitialState(true);
              return p.onEditingApproved(mode, newData, oldData);
            }}
          />
        )
      }}
      editable={{
        onRowUpdate: async (newData, oldData) =>
          // eslint-disable-next-line no-new
          new Promise(async (resolve, reject) => {
            setUseInitialState(true);
            try {
              const dataUpdate = [...viewPrivileges];
              const newPermission = interactingState;
              // eslint-disable-next-line no-underscore-dangle
              const index = dataUpdate.findIndex(d => d.view._id === oldData.view._id);
              dataUpdate.splice(index, 1, { ...newData, permission: newPermission });
              const { completed } = await onUpdate(newData, newPermission);
              if (completed) {
                setViewPrivileges(dataUpdate);
                setInteractingState('');
                return resolve();
              }
              return reject();
            } catch (e) {
              return reject();
            }
          })
      }}
      data={viewPrivileges}
      options={{
        search: false,
        actionsColumnIndex: -1,
        paging: false,
        toolbar: false,
        draggable: false,
        maxBodyHeight: 700,
        thirdSortClick: false
      }}
      localization={{
        body: {
          emptyDataSourceMessage: 'No existen registros ingresados',
          editTooltip: 'Editar',
          editRow: {
            deleteText: '¿Está seguro que desea eliminar el elemento?',
            saveTooltip: 'Aceptar',
            cancelTooltip: 'Cancelar'
          }
        },
        header: {
          actions: 'Acciones'
        }
      }}
      columns={[
        {
          title: 'Número de Vista',
          field: 'view.viewNumber',
          defaultSort: 'asc',
          editable: false,
          hideFilterIcon: true
        },
        {
          title: 'Módulo',
          field: 'view.module',
          sorting: false,
          editable: false,
          grouping: true
        },
        {
          title: 'Vista',
          field: 'view.view',
          sorting: false,
          editable: false
        },
        {
          title: 'Activar/Desactivar',
          field: 'permission',
          sorting: false,
          render: ({ permission }) => (permission === 'NoRead' ? 'Desactivado' : 'Activado'),
          editComponent: SwitchFn
        },
        {
          title: 'Lectura',
          field: 'permission',
          sorting: false,
          render: ({ permission }) => (permission !== 'NoRead' ? 'Sí' : 'No'),
          editComponent: readProperty
        },
        {
          title: 'Escritura',
          field: 'permission',
          sorting: false,
          render: ({ permission }) => (permission === 'ReadWrite' ? 'Sí' : 'No'),
          editComponent: readWriteProperty
        }
      ]}
      icons={tableIcons}
    />
  );
};

RoleDetails.propTypes = {
  role: PropTypes.string.isRequired,
  onUpdate: PropTypes.shape({}).isRequired
};

export default RoleDetails;
