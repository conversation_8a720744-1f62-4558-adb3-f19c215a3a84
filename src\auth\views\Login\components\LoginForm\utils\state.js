const initialState = {
  form: {
    email: {
      value: '',
      errors: []
    },
    password: {
      value: '',
      errors: []
    }
  },
  loginError: ''
};

function reducer(state, action) {
  const { data } = action;
  const { name, value, errors } = data;
  switch (action.type) {
    case 'removeFormField':
      return {
        ...state,
        form: { ...state.form, [name]: null }
      };
    case 'setFormValue':
      return {
        ...state,
        form: { ...state.form, [name]: { ...state.form[name], value } }
      };
    case 'setFormError':
      return {
        ...state,
        form: { ...state.form, [name]: { ...state.form[name], errors } }
      };
    case 'setValue':
      return {
        ...state,
        [name]: value
      };
    case 'resetForm':
      return {
        ...state,
        form: { ...state.form, ...initialState.form }
      };
    default:
      return state;
  }
}

export { reducer, initialState };
