export const FORM_WIDTH = '100%';

export const generalClasses = _theme => ({
  importButton: {
    height: '38px'
  },
  formControl: {
    marginLeft: 0,
    border: 0,
    display: 'inline-flex',
    padding: 0,
    width: '100%',
    'align-items': 'flex-end',
    position: 'relative',
    'min-width': 0,

    'flex-direction': 'column',
    'vertical-align': 0,
    '& p': {
      textAlign: 'center'
    }
  },
  attachIcon: {
    marginBottom: -81,
    marginRight: 25,
    border: 0.5,
    borderStyle: 'solid',
    padding: 3,
    '&:hover': {
      background: '#efefef'
    }
  }
});
