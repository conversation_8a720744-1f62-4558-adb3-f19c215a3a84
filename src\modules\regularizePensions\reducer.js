import {
  C<PERSON>AN_FAMILY_ASSIGNMENT_FILE_ERROR,
  CLEAN_FAMILY_ASSIGNMENT_RESUME_ERROR,
  SET_FAMILY_ASSIGNMENT_FILE_ERROR,
  SET_FAMILY_ASSIGNMENT_RESUME_ERROR,
  ENABLED_REACTIVATE,
  ENABLED_REACTIVATE_IMPORT,
  REACTIVATION_CRON_PROCESSED,
  INACT_REACT_PROCESS_AVAILABLE,
  WAS_INACT_REACT_ALREADY_EXECUTED,
  DAYS_TO_EXECUTE_INACT_REACT,
  WAS_API_CALLED,
  SET_ORPHANHOOD_FILE_ERROR,
  CLEAN_ORPHANHOOD_FILE_ERROR,
  CLEAN_ORPHANHOOD_RESUME_ERROR,
  ENABLED_ORPHANHOOD_IMPORT,
  ENABLED_ORPHANHOOD,
  ORPHANHOOD_CRON_PROCESSED,
  WAS_ORPHANHOOD_ALREADY_EXECUTED,
  ORPHANHOOD_PROCESS_AVAILABLE,
  DAYS_TO_EXECUTE_ORPHANHOOD,
  ORPHANHOOD_REACTIVATE,
  SET_HORPHANHOOD_RESUME_ERROR,
  WAS_ORPHANHOOD_API_CALLED
} from './actions';

const initialState = {
  data: [],
  isError: false,
  fileErrors: [],
  fileErrorsOrphanhood: [],
  errors: [],
  warnings: [],
  isReactivateEnable: false,
  wasReactivationCronExecuted: false,
  isReactInactAvailable: false,
  wasInacReactAlreadyExecuted: false,
  daysToExecuteInactReact: 0,
  apiCall: false,
  isOrphanhoodAvailable:false,
  wasOrphanhoodCronExecuted: false
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case SET_FAMILY_ASSIGNMENT_FILE_ERROR:
    case SET_ORPHANHOOD_FILE_ERROR:
      return {
        ...state,
        fileErrors: [...state.fileErrors, action.data.fileError]
      };
    case SET_FAMILY_ASSIGNMENT_RESUME_ERROR:
    case SET_HORPHANHOOD_RESUME_ERROR:
      return {
        ...state,
        isError: action.data.isError,
        errors: action.data.errors,
        warnings: action.data.warnings
      };
    case CLEAN_FAMILY_ASSIGNMENT_RESUME_ERROR:
    case CLEAN_ORPHANHOOD_RESUME_ERROR:    
      return {
        ...state,
        isError: false,
        errors: [],
        warnings: []
      };
    case CLEAN_FAMILY_ASSIGNMENT_FILE_ERROR:
    case CLEAN_ORPHANHOOD_FILE_ERROR:
      return {
        ...state,
        fileErrors: []
      };
    case ENABLED_REACTIVATE:
    case ORPHANHOOD_REACTIVATE:
      return {
        ...state,
        isReactivateEnable: action.data.isReactivateEnable
      };
    case ENABLED_REACTIVATE_IMPORT:
      return {
        ...state,
        isEnableReactivateImport: action.data.isEnableReactivateImport
      };
    case REACTIVATION_CRON_PROCESSED:
      return {
        ...state,
        wasReactivationCronExecuted: action.data
      };
    case INACT_REACT_PROCESS_AVAILABLE:
      return {
        ...state,
        isReactInactAvailable: action.data
      };
    case WAS_INACT_REACT_ALREADY_EXECUTED:
      return {
        ...state,
        wasInacReactAlreadyExecuted: action.data
      };
    case DAYS_TO_EXECUTE_INACT_REACT:
      return {
        ...state,
        daysToExecuteInactReact: action.data
      };
    case WAS_API_CALLED:
      return {
        ...state,
        apiCall: action.data
      };
    case ENABLED_ORPHANHOOD_IMPORT:
      return{
        ...state,
        isEnableOrphanhoodImport: action.data.isEnableOrphanhoodImport
      };
    case ENABLED_ORPHANHOOD:
      return{
        ...state,
        isOrphanhoodEnable: action.data.isOrphanhoodEnable
      };
    case ORPHANHOOD_CRON_PROCESSED:
      return {
        ...state,
        wasOrphanhoodCronExecuted: action.data
      };      
    case WAS_ORPHANHOOD_ALREADY_EXECUTED:
      return{
        ...state,
        wasOrphanhoodAlreadyExecuted: action.data
      };
    case ORPHANHOOD_PROCESS_AVAILABLE:
      return{
        ...state,
        isOrphanhoodAvailable: action.data
      };
    case DAYS_TO_EXECUTE_ORPHANHOOD:
      return{
        ...state,
        daysToExecuteOrphanhood: action.data
      };    
    case WAS_ORPHANHOOD_API_CALLED:
      return {
        ...state,
        OrphanhoodApiCall: action.data
    };

    default:
      return state;
  }
}
