/* eslint-disable react/no-multi-comp */
/* eslint-disable react/display-name */
import React, { lazy } from 'react';
import { Redirect } from 'react-router-dom';

import ProtectedRoute from './auth/components/ProtectedRoute';
import AuthLayout from './layouts/Auth';
import DashboardLayout from './layouts/Dashboard';
import ErrorLayout from './layouts/Error';
import { NomenclatorPage, MinimunPensionsAndBonus } from './modules/nomenclators';
import { AfpNomenclatorPage } from './modules/nomenclators/afp/views';
import { BankNomenclatorPage } from './modules/nomenclators/banks/views';
import { ServipagPage } from './modules/nomenclators/servipag/views';
import { IsapreNomenclatorPage } from './modules/nomenclators/isapre/views';
import { IsaprePortalPage } from './modules/nomenclators/isaprePortal/views';
import { MotiveNomenclatorPage } from './modules/nomenclators/motive/views';
import { TopesMaximoAndUnidadesReajustables } from './modules/nomenclators/topes-maximo-and-unidades-reajustables/views';
import { UnidadReajustable } from './modules/nomenclators/unidad-reajustable/views';


import {
  PortalIsapreResume,
  PortalIsapre
} from './modules/nomenclators/isaprePortal/views/portal-isapre';

import {
  CapitalFactorsNomenclatorPage,
  CapitalFactorsForm,
  ResumeErrorCapitalFactors
} from './modules/nomenclators/capitalFactors/views';

import {
  PrepareSettlementPage,
  CajaLosAndesDiscountsPage,
  FonasaDiscountsPage
} from './modules/prepareSettlements';

import {
  FamilyAssignment,
  FamilyAssignmentResume,
  RegularizedPensionsPage,
  Orphanhood,
  OrphanhoodResume,
} from './modules/regularizePensions/views';

import {
  TemporaryPensions,
  TemporaryPensionsPage,
  TemporaryPensionsResume
} from './modules/temporaryPensions/views';

import {
  DisplayRetired,
  PensionsModule,
  EditAssetsAndDiscounts
} from './modules/retirementQuery/index';

import {
  ReportsPage,
  SummarizeSettlements,
  InactivationReactivation,
  CurrentCapital,
  PensionsAccounting
} from './modules/currentCapitalReports/index';

import { BankFile } from './modules/bankFile/views/BankFile';

import {
  NationalHoliDaysAssignmentPage,
  NationalHoliDaysAssignment,
  NationalHolidaysResume
} from './modules/nomenclators/nationalHolidaysAssignment/views';

import { Systems, PermissionsAndRoles, RoleDetails, Users, MonitorJob } from './modules/systems';

import HomeView from './modules/homeView/views';

import { PreviredFile } from './modules/previredFile/views/previredFile';

const SeeMore = lazy(() => import('./modules/retirementQuery/views/seeMore'));

const routes = [
  {
    path: '/',
    exact: true,
    component: () => <Redirect to="/home" />
  },
  {
    path: '/auth',
    component: AuthLayout,
    routes: [
      {
        path: '/auth/login',
        exact: true,
        component: lazy(() => import('auth/views/Login'))
      },
      {
        component: () => <Redirect to="/errors/error-404" />
      }
    ]
  },
  {
    path: '/errors',
    component: ErrorLayout,
    routes: [
      {
        path: '/errors/error-401',
        exact: true,
        component: lazy(() => import('pages/Error401'))
      },
      {
        path: '/errors/error-404',
        exact: true,
        component: lazy(() => import('pages/Error404'))
      },
      {
        path: '/errors/error-500',
        exact: true,
        component: lazy(() => import('pages/Error500'))
      },
      {
        component: () => <Redirect to="/errors/error-404" />
      }
    ]
  },
  {
    route: '*',
    component: props => <ProtectedRoute {...props} component={DashboardLayout} />,
    routes: [
      {
        path: '/pensionados',
        component: PensionsModule,
        routes: [
          {
            path: '/pensionados/consulta-pensionados/',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={DisplayRetired}
                module="Consulta de pensionados"
              />
            )
          },
          {
            path: '/pensionados/consulta-pensionados/ver-mas',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={SeeMore}
                module="Consulta de pensionados"
                viewNumber={1}
              />
            )
          },
          {
            path: '/pensionados/consulta-pensionados/editar-haberes-y-descuentos-no-formulables',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={EditAssetsAndDiscounts}
                module="Consulta de pensionados"
              />
            )
          },
          {
            component: () => <Redirect to="/pensionados/" />
          }
        ]
      },
      {
        path: '/nuevas-pensiones',
        component: TemporaryPensionsPage,
        routes: [
          {
            path: '/nuevas-pensiones/importar',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={TemporaryPensions}
                module="Enlazar"
                viewNumber={1}
              />
            )
          },
          {
            path: '/nuevas-pensiones/resumen',
            exact: true,
            component: TemporaryPensionsResume
          },
          {
            component: () => <Redirect to="/nuevas-pensiones/importar" />
          }
        ]
      },
      {
        path: '/regularizar-pensiones',
        component: RegularizedPensionsPage,
        routes: [
          {
            path: '/regularizar-pensiones/inactivar-reactivar/reasignacion-familiar/',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={FamilyAssignment}
                module="Asignaci[óo]n Familiar"
                viewNumber={1}
              />
            )
          },
          {
            path: '/regularizar-pensiones/inactivar-reactivar/reasignacion-familiar/resumen',
            exact: true,
            component: FamilyAssignmentResume
          },
          {
            path: '/regularizar-pensiones/inactivar-reactivar/orfandad/',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={Orphanhood}
                module="Orfandad"
                viewNumber={1}
              />
            )
          },
          {
            path: '/regularizar-pensiones/inactivar-reactivar/orfandad/resumen',
            exact: true,
            component: OrphanhoodResume
          },
          {
            component: () => (
              <Redirect to="/regularizar-pensiones/inactivar-reactivar/orfandad/" />
            )
          } 
        ]
      },
      {
        path: '/home',
        exact: true,
        component: HomeView
      },
      {
        path: '/preparar-liquidacion',
        component: PrepareSettlementPage,
        routes: [
          {
            path: '/preparar-liquidacion/cargar-descuentos/caja-los-andes',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={CajaLosAndesDiscountsPage}
                module="Cargar descuentos"
                viewNumber={1}
              />
            )
          },
          {
            path: '/preparar-liquidacion/cargar-descuentos/fonasa',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={FonasaDiscountsPage}
                module="Cargar descuentos"
                viewNumber={2}
              />
            )
          },
          {
            component: () => (
              <Redirect to="/preparar-liquidacion/cargar-descuentos/caja-los-andes" />
            )
          }
        ]
      },
      {
        path: '/mantenedores',
        component: NomenclatorPage,
        routes: [
          {
            path: '/mantenedores/pensiones-minimas-y-aguinaldos',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={MinimunPensionsAndBonus}
                module="Mantenedor"
                viewNumber={1}
              />
            )
          },
          {
            path: '/mantenedores/afp',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={AfpNomenclatorPage}
                module="Mantenedor"
                viewNumber={2}
              />
            )
          },
          {
            path: '/mantenedores/isapres',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={IsapreNomenclatorPage}
                module="Mantenedor"
                viewNumber={3}
              />
            )
          },
          {
            path: '/mantenedores/isapres/portal',
            component: IsaprePortalPage,
            routes: [
              {
                path: '/mantenedores/isapres/portal',
                exact: true,
                component: props => (
                  <ProtectedRoute
                    {...props}
                    component={PortalIsapre}
                    module="Mantenedor"
                    viewNumber={1}
                  />
                )
              },
              {
                path: '/mantenedores/isapres/portal/resumen',
                exact: true,
                component: props => (
                  <ProtectedRoute
                    {...props}
                    component={PortalIsapreResume}
                    module="Mantenedor"
                    viewNumber={2}
                  />
                )
              },
              {
                component: () => <Redirect to="/mantenedores/isapres/portal" />
              }
            ]
          },
          {
            path: '/mantenedores/sucursales-banco-de-chile',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={BankNomenclatorPage}
                module="Mantenedor"
                viewNumber={4}
              />
            )
          },
          {
            path: '/mantenedores/sucursales-servipag',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={ServipagPage}
                module="Mantenedor"
                viewNumber={5}
              />
            )
          },
          {
            path: '/mantenedores/motivos',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={MotiveNomenclatorPage}
                module="Mantenedor"
                viewNumber={6}
              />
            )
          },
          {
            path: '/mantenedores/factores-capitales',
            component: CapitalFactorsNomenclatorPage,
            routes: [
              {
                path: '/mantenedores/factores-capitales/importar',
                exact: true,
                component: props => (
                  <ProtectedRoute
                    {...props}
                    component={CapitalFactorsForm}
                    module="Mantenedor"
                    viewNumber={7}
                  />
                )
              },
              {
                path: '/mantenedores/factores-capitales/errores',
                exact: true,
                component: ResumeErrorCapitalFactors
              },
              {
                component: () => <Redirect to="/mantenedores/factores-capitales/importar" />
              }
            ]
          },
          {
            path: '/mantenedores/asignacion-aguinaldos',
            component: NationalHoliDaysAssignmentPage,
            routes: [
              {
                path: '/mantenedores/asignacion-aguinaldos',
                exact: true,
                component: props => (
                  <ProtectedRoute
                    {...props}
                    component={NationalHoliDaysAssignment}
                    module="Mantenedor"
                    viewNumber={7}
                  />
                )
              },
              {
                path: '/mantenedores/asignacion-aguinaldos/resumen',
                exact: true,
                component: NationalHolidaysResume
              },
              {
                component: () => <Redirect to="/mantenedores/asignacion-aguinaldos/" />
              }
            ]
          },
          {
            path: '/mantenedores/topes-maximo-and-unidades-reajustables',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={TopesMaximoAndUnidadesReajustables}
                module="Mantenedor"
                viewNumber={1}
              />
            )
          },
          {
            path: '/mantenedores/unidad-reajustable',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={UnidadReajustable}
                module="Mantenedor"
                viewNumber={1}
              />
            )
          },          
        ]
      },
      {
        path: '/reporteria',
        component: ReportsPage,
        routes: [
          {
            path: '/reporteria/inactivaciones-y-reactivaciones',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={InactivationReactivation}
                module="Reporter[íi]a"
                viewNumber={1}
              />
            )
          },
          {
            path: '/reporteria/liquidaciones',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={SummarizeSettlements}
                module="Reporter[íi]a"
                viewNumber={2}
              />
            )
          },
          {
            path: '/reporteria/capitales-vigentes',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={CurrentCapital}
                module="Reporter[íi]a"
                viewNumber={3}
              />
            )
          },
          {
            path: '/reporteria/contabilizacion-pensiones',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={PensionsAccounting}
                module="Reporter[íi]a"
                viewNumber={4}
              />
            )
          },
          {
            path: '/reporteria/archivo-banco',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={BankFile}
                module="Reporter[íi]a"
                viewNumber={5}
              />
            )
          },
          {
            path: '/reporteria/archivo-previred',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={PreviredFile}
                module="Reporter[íi]a"
                viewNumber={6}
              />
            )
          },
          {
            component: () => <Redirect to="/reporteria" />
          }
        ]
      },
      {
        path: '/sistemas',
        component: Systems,
        routes: [
          {
            path: '/sistemas/roles-y-permisos',
            exact: true,
            component: props => (
              <ProtectedRoute
                {...props}
                component={PermissionsAndRoles}
                module="Sistemas"
                viewNumber={1}
              />
            )
          },
          {
            path: '/sistemas/roles-y-permisos/:role',
            exact: true,
            component: props => (
              <ProtectedRoute {...props} component={RoleDetails} module="Sistemas" viewNumber={1} />
            )
          },
          {
            path: '/sistemas/usuarios',
            exact: true,
            component: props => (
              <ProtectedRoute {...props} component={Users} module="Sistemas" viewNumber={2} />
            )
          },
          {
            path: '/sistemas/monitor',
            exact: true,
            component: props => (
              <ProtectedRoute {...props} component={MonitorJob} module="Sistemas" viewNumber={2} />
            )
          }
        ]
      },
      {
        component: () => <Redirect to="/errors/error-404" />
      }
    ]
  },

  {
    path: '/authtoken',
    exact: true,
    component: () => null
  }
];

export default routes;
