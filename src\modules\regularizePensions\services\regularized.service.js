/* eslint-disable no-console */
import {
  enableReactivate,
  enableReactivateImport,
  isInactReactAvailable,
  numberOfDaysToExecute,
  setWasInacReactAlreadyExecuted,
  apiCalled,
  enableOrphanhoodImport,
  setWasOrphanhoodAlreadyExecuted,
  isOrphanhoodAvailable,
  numberOfDaysToExecuteOrphanhood,
  orphanhoodReactivate,
  OrphanhoodApiCalled,
  enableOrphanhood
} from '../actions';

import { axiosRequest } from '../../../services/axiosRequest';
const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

//------------------------------------------------------------------------------------------------
export const fetchTemporaryFamilyAssigments = async () => {
  const { data } = await axiosRequest.get(`${api}/temporaryfamilyassignment`).catch(err => {
    console.error(err);
    return { data: [], isError: true };
  });
  return data;
};

//------------------------------------------------------------------------------------------------
export const insertTemporaryFamilyAssigments = jsonData =>
  axiosRequest.post(`${api}/temporaryfamilyassignment/bulk`, jsonData).catch(err => {
    console.error(err);
    return { data: {}, isError: true };
  });

//------------------------------------------------------------------------------------------------
export const inactivateOrphanPension = (onSuccessImport, onErrorImport) => async (
  dispatch,
  _getState
) => {
  const { data, isError = false } = await axiosRequest
    .post(`${api}/executeprocess/inactivateorreactivateprocess`)
    .catch(err => {
      console.error(err);
      return {
        data: {
          error: 'Error en proceso inactivar y reactivar ',
          message: 'Proceso de inactivar y reactivar fallido',
          executionCompleted: false
        },
        isError: true
      };
    });
  const { executionCompleted, error } = data;

  if (executionCompleted) {
    onSuccessImport('Proceso de inactivar y reactivar exitoso');
  }
  if (isError) {
    onErrorImport(error);
  }

  dispatch(setWasInacReactAlreadyExecuted(!!executionCompleted));
  dispatch(enableReactivateImport(!!executionCompleted, !!executionCompleted));
  dispatch(enableReactivate(!!executionCompleted));
};
//------------------------------------------------------------------------------------------------
export const wasInactivatedThisMonth = () => async (dispatch, _getState) => {
  const { data } = await axiosRequest
    .get(`${api}/temporaryfamilyassignment/was-inactivated`)

    .catch(err => {
      console.error(err);
      return { data: {}, isError: true };
    });
  dispatch(enableReactivateImport(data.wasInactivated, data.completed));
  dispatch(enableReactivate(data.wasInactivated));
};
//------------------------------------------------------------------------------------------------
export const checkCronExecution = dispatch => async (cronName, action) => {
  const {
    data: {
      result: { alreadyExecuted }
    }
  } = await axiosRequest
    .get(`${api}/verifycronexecution/${cronName}`)

    .catch(err => {
      console.error(err);
      return { data: { result: { alreadyExecuted: false, error: '' } } };
    });
  action && dispatch(action(alreadyExecuted));
};
//------------------------------------------------------------------------------------------------
export const checkInactReactAvailabilityProcess = async dispatch => {
  const {
    data: { processAvailable, nDays }
  } = await axiosRequest
    .get(`${api}/getBusinessDays/inact-react-process`)

    .catch(err => {
      console.error(err);
      return { data: { processAvailable: false, nDays: '' } };
    });
  dispatch(isInactReactAvailable(processAvailable));
  dispatch(numberOfDaysToExecute(nDays));
  dispatch(apiCalled(true));
};
//------------------------------------------------------------------------------------------------
export const existTemporaryFamilyAssigments = async () => {
  const { data } = await axiosRequest.get(`${api}/temporaryfamilyassignment/exist`).catch(err => {
    console.error(err);
    return { data: { result: 0 }, isError: true };
  });
  return data;
};

//------------------------------------------------------------------------------------------------
export const checkOrphanhoodAvailabilityProcess = async dispatch => {
  const {
    data: { processAvailable, nDays }
  } = await axiosRequest.get(`${api}/getBusinessDays/inact-react-process`).catch(err => {
    console.error(err);
    return { data: { processAvailable: false, nDays: '' } };
  });

  dispatch(isOrphanhoodAvailable(processAvailable));
  dispatch(numberOfDaysToExecuteOrphanhood(nDays));
  dispatch(OrphanhoodApiCalled(true));
};

//------------------------------------------------------------------------------------------------
export const inactivateOrphanhood = (onSuccessImport, onErrorImport, progress) => async (
  dispatch,
  _getState
) => {
  progress.show();  
  dispatch(enableOrphanhood(true));
  const { data, isError = false } = await axiosRequest
    .post(`${api}/executeprocess/inactivateorreactivateorphanhoodprocess`)
    .catch(err => {
      console.error(err);      
      return {
        data: {
          error: 'Error en proceso inactivar y reactivar orfandad',
          message: 'Proceso de inactivar y reactivar orfandad fallido',
          executionCompleted: false
        },
        isError: true
      };
    });
  const { executionCompleted, error } = data;

  if (executionCompleted) {
    onSuccessImport('Proceso de inactivar y reactivar exitoso');    
  }
  if (isError) {
    dispatch(enableOrphanhood(false));
    onErrorImport(error);
  }

  dispatch(setWasOrphanhoodAlreadyExecuted(!!executionCompleted));
  dispatch(enableOrphanhoodImport(!!executionCompleted, !!executionCompleted));
  dispatch(orphanhoodReactivate(!!executionCompleted));
  progress.hide();
};

//------------------------------------------------------------------------------------------------
export const insertTemporaryOrphanhood = jsonData =>
  axiosRequest.post(`${api}/temporaryHorphanhood/bulk`, jsonData).catch(err => {
    console.error(err);
    return { data: {}, isError: true };
  });

//------------------------------------------------------------------------------------------------
export const wasOrphanhoodInactivatedThisMonth = () => async (dispatch, _getState) => {
  const { data } = await axiosRequest
    .get(`${api}/temporaryHorphanhood/was-inactivated`)
    .catch(err => {
      console.error(err);
      return { data: {}, isError: true };
    });
  dispatch(enableOrphanhoodImport(data.wasInactivated, data.completed));
  dispatch(orphanhoodReactivate(data.wasInactivated));
};
