//------------------------------------------------------------------------------------------------
export const CREATE_NOM_USER = 'CREATE_NOM_USER';
export const createNomenclatorUser = user => {
  return {
    type: CREATE_NOM_USER,
    data: { user }
  };
};
//------------------------------------------------------------------------------------------------
export const DELETE_NOM_USER = 'DELETE_NOM_USER';
export const deleteNomenclatorUser = user => {
  return {
    type: DELETE_NOM_USER,
    data: { user }
  };
};
//------------------------------------------------------------------------------------------------
export const UPDATE_NOM_USER = 'UPDATE_NOM_USER';
export const updateNomenclatorUser = (prevUser, newUser) => {
  return {
    type: UPDATE_NOM_USER,
    data: { prevUser, newUser }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_NOM_USER = 'SET_NOM_USER';
export const loadNomenclatorUser = user => {
  return {
    type: SET_NOM_USER,
    data: { user }
  };
};
