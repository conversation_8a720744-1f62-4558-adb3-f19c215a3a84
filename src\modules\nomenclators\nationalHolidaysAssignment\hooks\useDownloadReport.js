import moment from 'moment';
import { getNationalHolidaysReport } from '../services/nationalHolidaysService';

const ON_ERROR_TEXT = 'Hubo un error con la descarga del reporte';

const downloadFiles = async ({ _dispatch, progress, onErrorSnackbar }) => {
  const date = moment().format('YYYYMM');
  const reportFileName = `ACHS_pareo_IPS_${date}`;
  progress.show();

  const { data, isError } = await getReport();

  if (isError) {
    onErrorSnackbar(`${ON_ERROR_TEXT}`);
    progress.hide();
    return false;
  }

  const linkDownload = document.createElement('a');
  linkDownload.href = 'data:text/plain;base64,' + data;
  linkDownload.download = reportFileName;
  linkDownload.click();

  progress.hide();
  return true;
};

const getReport = async () => await getNationalHolidaysReport();

export const checkReportAvailable = async setIsReporteAvailable => {
  const { data, isError } = await getReport();

  if (isError || !data) return setIsReporteAvailable(false);

  return setIsReporteAvailable(true);
};

export const UseDownloadReport = ({ dispatch, progress, onErrorSnackbar }) => {
  return downloadFiles({ dispatch, progress, onErrorSnackbar });
};
