import { formatRut, formatDate } from './formatters';

const formattingRutFields = data => ({
  ...data,
  collectorRut: formatRut(data.collector.rut),
  causantRut: formatRut(data.causant.rut)
});

const getFullname = ({ beneficiary: { name, lastName, mothersLastName = '' } }) =>
  `${name} ${lastName} ${mothersLastName}`.trim().toUpperCase();

const formatBeneficiary = data => ({
  ...data,
  beneficiary: `${getFullname(data)} \n ${formatRut(data.beneficiary.rut)}`
});

const formatDates = data => ({
  ...data,
  endDateOfValidity: formatDate(data.endDateOfValidity),
  dateOfBirth: formatDate(data.dateOfBirth)
});

const formatsFunctions = [formatBeneficiary, formattingRutFields, formatDates];

const composeFormats = ([...fns]) => data => fns.reduce((y, fn) => fn(y), data);

const formatData = (data = []) => data.map(registry => composeFormats(formatsFunctions)(registry));

export default formatData;
