/* eslint-disable react/prop-types */
/* eslint-disable no-underscore-dangle */
/* eslint-disable jsx-a11y/alt-text */
import React, { forwardRef, useState, useEffect } from 'react';
import moment from 'moment';
import { useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';
import useOnLineStatus from '@rehooks/online-status';

import MaterialTable from 'material-table';
import Snackbar from '@material-ui/core/Snackbar';
import MuiAlert from '@material-ui/lab/Alert';
import CircularProgress from '@material-ui/core/CircularProgress';
import { Edit, RemoveRedEye, DeleteOutline } from '@material-ui/icons';
import { Button, Grid, Box } from '@material-ui/core';

import ConfirmationSnackbar from '../../../../components/ConfirmationSnackbar';
import {
  updatePensionerAssetsAndDiscounts,
  isReadyToUpdateNonFormulable
} from '../../services/queryPensions.service';
import { formatCurrency } from '../../utils/discountsAndAssetsValidations';

import useStyles from './styles';
import AlertInfo from './alertNonFormulableAssetsAndDiscounts';
import { checkWritePermission } from '../../../../utils/checkUserPermission';

const tableIcons = {
  Edit: forwardRef((props, ref) => <Edit {...props} ref={ref} />),
  Delete: forwardRef((props, ref) => <DeleteOutline {...props} ref={ref} />)
};

const getStatus = endDate => (!moment(endDate, 'MM-YYYY').isAfter() ? 'Expirado' : 'Activo');

function Alert(props) {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
}

export default function NonFormulableAssetsAndDiscountsTable({
  data: discAndAssetsData,
  userRole,
  setData
}) {
  const onlineStatus = useOnLineStatus();
  const [discountsAndAssets, setDiscountsAndAssets] = useState(
    discAndAssetsData.discountsAndAssets || {}
  );
  const [rowData, setRowData] = useState({});
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackBarMessage, setSnackbarMessage] = useState('');
  const [snackBarSeverity, setSnackbarSeverity] = useState('');
  const [openConfirmationSnackbar, setOpenConfirmationSnackbar] = useState(false);
  const [loading, setLoading] = useState(false);
  const [deleting, setDeleting] = useState(false);

  const [isValidPeriod, setIsValidPeriod] = useState({
    postLiquidationCheckpointReportWorkerExecuted: false,
    unifiedUnifiedAndGenerateBankUploadExecuted: true,
    hasBusinessDayPassed: true
  });

  const classes = useStyles();
  const hasWritePermission = checkWritePermission(userRole);

  const messageOnNoData = () => {
    return (
      <div className={classes.messageNotFound}>
        <img src="/images/find_in_page48dp.svg" className={classes.tableImage} />
        <h5>No existen haberes o descuentos no formulables asociados al pensionado</h5>
      </div>
    );
  };

  const history = useHistory();

  const editRow = (row, isReadOnly) => {
    history.push({
      pathname: '/pensionados/consulta-pensionados/editar-haberes-y-descuentos-no-formulables',
      state: {
        ...row,
        amount: formatCurrency(row.amount),
        discountsAndAssets,
        readOnly: isReadOnly
      },
      title: 'Editar Haberes Y Descuentos No Formulables'
    });
  };

  const onDeleteIconClick = row => {
    if (deleting) return;
    setDeleting(true);
    setRowData(row);
    setOpenConfirmationSnackbar(true);
  };

  const showSnackbar = (severity, message) => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setOpenSnackbar(true);
  };

  const deleteRow = async setNewData => {
    const mapper = { haber: 'assetsNonFormulable', descuento: 'discountsNonFormulable' };
    const discountsAndAssetsData = { ...discountsAndAssets };
    discountsAndAssetsData[mapper[rowData.label.toLowerCase()]] = discountsAndAssetsData[
      mapper[rowData.label.toLowerCase()]
    ].filter(r => r._id !== rowData._id);

    setLoading(true);
    const { data, error } = await updatePensionerAssetsAndDiscounts(discountsAndAssetsData);
    setLoading(false);

    if (error) {
      showSnackbar('error', 'Error al eliminar el registro');
      return;
    }
    const { result } = data;
    setRowData({});
    setDiscountsAndAssets(result?.discountsAndAssets);
    setNewData(prevData => {
      return { ...prevData, discountsAndAssets: result?.discountsAndAssets || [] };
    });
    setOpenConfirmationSnackbar(false);
    showSnackbar('success', 'Registro eliminado');
    setDeleting(false);
  };

  const getNonFormulableDiscountsAndAssets = () => {
    const { assetsNonFormulable = [], discountsNonFormulable = [] } = discountsAndAssets;
    return [...assetsNonFormulable, ...discountsNonFormulable].sort((current, next) => {
      return moment(current.creationDate).diff(moment(next.creationDate));
    });
  };

  const hasBusinessDayPassedAlertText =
    'Se pueden realizar modificaciones hasta el día 13 hábil del mes y luego del punto de control hasta antes del envío del archivo al Banco';

  const isEnabledEdit = () => {
    return (
      !isValidPeriod.hasBusinessDayPassed ||
      (isValidPeriod.postLiquidationCheckpointReportWorkerExecuted &&
        !isValidPeriod.unifiedUnifiedAndGenerateBankUploadExecuted)
    );
  };

  useEffect(() => {
    async function fetchData() {
      const { data: result } = await isReadyToUpdateNonFormulable();
      setIsValidPeriod(result);
    }
    fetchData();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div>
      <Grid container justify="flex-end">
        <Box mb={2}>
          <Button
            variant="contained"
            color="primary"
            onClick={() =>
              history.push({
                pathname:
                  '/pensionados/consulta-pensionados/editar-haberes-y-descuentos-no-formulables',
                state: { discountsAndAssets }
              })
            }
            disabled={!hasWritePermission || !onlineStatus || !isEnabledEdit() || deleting}
          >
            Agregar
          </Button>
        </Box>
      </Grid>
      {loading && (
        <Grid className={classes.spinnerContainer}>
          <CircularProgress />
        </Grid>
      )}
      <Grid className={classes.grid}>
        <AlertInfo
          alertClass={classes.alertBar}
          severity="error"
          color="info"
          alertText={hasBusinessDayPassedAlertText}
        />
      </Grid>

      <MaterialTable
        icons={tableIcons}
        columns={[
          {
            title: 'Haber o Descuento',
            field: 'label',
            sorting: false
          },
          {
            title: 'Monto',
            field: 'amount',
            sorting: false,
            render: row => <p>{`${formatCurrency(row.amount)}`}</p>
          },
          {
            title: 'Motivo',
            field: 'reason',
            sorting: false
          },
          {
            title: 'Tipo de haber',
            field: 'assetType',
            sorting: false
          },
          {
            title: 'Fecha inicio',
            field: 'startDate',
            sorting: false
          },
          {
            title: 'Fecha fin',
            field: 'endDate',
            sorting: false
          },
          {
            title: 'Estado',
            render: row => <p>{getStatus(row.endDate)}</p>,
            sorting: false
          }
        ]}
        data={getNonFormulableDiscountsAndAssets()}
        options={{
          search: false,
          showTitle: true,
          actionsColumnIndex: -1,
          paging: false,
          toolbar: false,
          draggable: false,
          maxBodyHeight: 700,
          headerStyle: {
            fontWeight: 'bold'
          }
        }}
        localization={{
          body: {
            emptyDataSourceMessage: messageOnNoData()
          },
          header: {
            actions: 'Acciones'
          }
        }}
        editable={{
          isEditable: () => false
        }}
        actions={[
          () => ({
            icon: RemoveRedEye,
            tooltip: 'Ver detalles',
            onClick: (_, _rowData) => {
              editRow(_rowData, true);
            },
            disabled: !hasWritePermission || !onlineStatus || deleting
          }),
          () => ({
            icon: Edit,
            tooltip: 'Editar',
            onClick: (_, _rowData) => editRow(_rowData, false),
            disabled: !hasWritePermission || !onlineStatus || deleting || !isEnabledEdit()
          }),
          () => ({
            icon: DeleteOutline,
            tooltip: 'Eliminar',
            onClick: (_, _rowData) => onDeleteIconClick(_rowData),
            disabled: !hasWritePermission || !onlineStatus || deleting || !isEnabledEdit()
          })
        ]}
      />
      <ConfirmationSnackbar
        open={openConfirmationSnackbar}
        messageInfo="¿Está seguro que desea eliminar el elemento?"
        setOpen={open => {
          setOpenConfirmationSnackbar(open);
          setDeleting(false);
        }}
        onConfirm={() => {
          deleteRow(setData);
          setDeleting(false);
        }}
      />

      <Snackbar open={openSnackbar} autoHideDuration={5000} onClose={() => setOpenSnackbar(false)}>
        <Alert onClose={() => setOpenSnackbar(false)} severity={snackBarSeverity}>
          {snackBarMessage}
        </Alert>
      </Snackbar>
    </div>
  );
}

NonFormulableAssetsAndDiscountsTable.propTypes = {
  data: PropTypes.oneOfType([PropTypes.array, PropTypes.func]).isRequired
};
