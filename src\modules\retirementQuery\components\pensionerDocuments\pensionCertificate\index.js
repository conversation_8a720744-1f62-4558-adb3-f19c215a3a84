/* eslint-disable react/prop-types */
import React from 'react';
import { Document, Image, Page, Text, View } from '@react-pdf/renderer';

import styles from './styles';

const LIFE_PENSION = /Vigente vitalicia/i;
const FOOTER_TEXT = 'DEPTO. PRESTACIONES ECONÓMICAS';
const FOOTER_ACHS = 'ASOCIACIÓN CHILENA DE SEGURIDAD';

const getValidityText = (validityType, endDateOfValidity = '') =>
  LIFE_PENSION.test(validityType) ? validityType : `${validityType} hasta el ${endDateOfValidity}`;

const UpperLeftComponent = ({ pensionCodeId = '#######' }) => (
  <View style={styles.upperLeftContainer}>
    <Text style={styles.text}>{`CERTIFICADO Nº: ${pensionCodeId}`}</Text>
  </View>
);

const UpperRightComponent = () => (
  <View style={styles.upperRightContainer}>
    <Image style={styles.image} src="/Logo_ACHS.png" />
  </View>
);

const BodyWithoutCharges = ({
  beneficiaryFullName = '',
  beneficiaryRut = '',
  pensionType = '',
  pensionStartDate = '',
  validityType = '',
  endDateOfValidity = '',
  taxablePension = ''
}) => {
  const validityText = getValidityText(validityType, endDateOfValidity);
  return (
    <View style={styles.bodyContainer}>
      <Text style={styles.text}>
        <Text style={styles.highlight}>La Asociación Chilena de Seguridad, RUT 70.360.100-6, </Text>
        Mutual Administradora del Seguro Social contra riesgos de Accidente y Enfermedades
        Profesionales, Ley Nº 16.744 ubicada en Ramón Carnicer N° 163, Providencia, Santiago,
        certifica que el pensionado
        <Text style={styles.highlight}>{` ${beneficiaryFullName} RUT ${beneficiaryRut} `}</Text>
        {`es pensionado por ${pensionType}, desde el ${pensionStartDate} con vigencia 
    ${validityText} y el monto de dicha pensión es de`}
        <Text style={styles.highlight}>{` $${taxablePension}`}</Text>
        .- mensuales imponibles.
      </Text>
    </View>
  );
};

const DisabilityFamilyChargesTable = ({ chargesOfPensioner = [], familyGroup }) => {
  return (
    <>
      <View style={styles.tableContainerDisability}>
        <View style={styles.tableHeaderDisability}>
          <Text style={styles.textTable}>RUT</Text>
          <Text style={styles.textTable}>Grupo Familiar</Text>
          <Text style={styles.textTable}>Fecha Fin Pensión</Text>
        </View>

        {chargesOfPensioner.map(row => (
          <View style={styles.tableBodyDisability}>
            <Text style={styles.textTable}>{row.chargeId}</Text>
            <Text style={styles.textTable}>{familyGroup}</Text>
            <Text style={styles.textTable}>{row.endDateOfCertificationValidity}</Text>
          </View>
        ))}
      </View>
    </>
  );
};

const SurvivalFamilyChargesTable = ({ chargesOfPensioner = [] }) => {
  return (
    <>
      <View style={styles.tableContainerSurvival}>
        <View style={styles.tableHeaderSurvival}>
          <Text style={styles.textTable}>RUT</Text>
          <Text style={styles.textTable}>Nombre</Text>
          <Text style={styles.textTable}>Grupo Familiar</Text>
          <Text style={styles.textTable}>Fecha Nacimiento</Text>
          <Text style={styles.textTable}>Fecha Fin Pensión</Text>
        </View>

        {chargesOfPensioner.map(row => (
          <View style={styles.tableBodySurvival}>
            <Text style={styles.textTable}>{row.beneficiary.rut}</Text>
            <Text style={styles.textTable}>{row.chargeFullName}</Text>
            <Text style={styles.textTable}>{row.familyGroup}</Text>
            <Text style={styles.textTable}>{row.dateOfBirth}</Text>
            <Text style={styles.textTable}>{row.endDateOfValidity}</Text>
          </View>
        ))}
      </View>
    </>
  );
};

const BodyWithCharges = ({
  beneficiaryFullName = '',
  beneficiaryRut = '',
  causantRut = '',
  pensionType = '',
  pensionStartDate = '',
  validityType = '',
  endDateOfValidity = '',
  taxablePension = '',
  familyGroup,
  chargesOfPensioner = []
}) => {
  const validityText = getValidityText(validityType, endDateOfValidity);
  const isDisabilityPension = beneficiaryRut === causantRut;
  return (
    <>
      <View style={styles.bodyContainer}>
        <Text style={styles.text}>
          <Text style={styles.highlight}>
            {`La Asociación Chilena de Seguridad, RUT 70.360.100-6, `}
          </Text>
          Mutual Administradora del Seguro Social contra riesgos de Accidente y Enfermedades
          Profesionales, Ley Nº 16.744 ubicada en Ramón Carnicer N° 163, Providencia, Santiago,
          certifica que
          <Text style={styles.highlight}>{` ${beneficiaryFullName} `}</Text>
          RUT
          <Text style={styles.highlight}>{` ${beneficiaryRut} `}</Text>
          {`es pensionado de ${pensionType}, desde ${pensionStartDate} con vigencia 
      ${validityText} y su monto de pensión asciende a `}
          <Text style={styles.highlight}>{` $${taxablePension}.- `}</Text>
          mensuales imponibles. Mantiene como carga vigente a:
        </Text>
      </View>
      {isDisabilityPension ? (
        <DisabilityFamilyChargesTable
          chargesOfPensioner={chargesOfPensioner}
          familyGroup={familyGroup}
        />
      ) : (
        <SurvivalFamilyChargesTable chargesOfPensioner={chargesOfPensioner} />
      )}
    </>
  );
};

const CurrentDate = ({ currentDate = '' }) => {
  return <Text style={styles.headerDate}>{`Santiago, ${currentDate}`}</Text>;
};

const Signature = ({ pecBoss = 'JORGE NIEMANN MARTÍNEZ' }) => {
  return (
    <View styles={styles.signatureContainer}>
      <Text style={styles.textFooter}>{pecBoss}</Text>
      <Text style={styles.textFooter}>{FOOTER_TEXT}</Text>
      <Text style={styles.textFooter}>{FOOTER_ACHS}</Text>
    </View>
  );
};

const PensionCertificate = ({ data }) => {
  const {
    beneficiaryFullName,
    beneficiaryRut,
    causantRut,
    pensionType,
    pensionStartDate,
    validityType,
    endDateOfValidity,
    liquidation,
    currentDate,
    pecBoss,
    pensionCodeId,
    chargesOfPensioner,
    familyGroup
  } = data;
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.upperContainer}>
          <UpperLeftComponent pensionCodeId={pensionCodeId} />
          <UpperRightComponent />
        </View>
        {chargesOfPensioner?.length ? (
          <BodyWithCharges
            beneficiaryFullName={beneficiaryFullName}
            beneficiaryRut={beneficiaryRut}
            causantRut={causantRut}
            pensionType={pensionType}
            pensionStartDate={pensionStartDate}
            validityType={validityType}
            endDateOfValidity={endDateOfValidity}
            taxablePension={liquidation?.taxablePension}
            currentDate={currentDate}
            chargesOfPensioner={chargesOfPensioner}
            familyGroup={familyGroup}
          />
        ) : (
          <BodyWithoutCharges
            beneficiaryFullName={beneficiaryFullName}
            beneficiaryRut={beneficiaryRut}
            pensionType={pensionType}
            pensionStartDate={pensionStartDate}
            validityType={validityType}
            endDateOfValidity={endDateOfValidity}
            taxablePension={liquidation?.taxablePension}
            currentDate={currentDate}
          />
        )}
        <CurrentDate currentDate={currentDate} />
        <Signature pecBoss={pecBoss} />
      </Page>
    </Document>
  );
};

export default PensionCertificate;
