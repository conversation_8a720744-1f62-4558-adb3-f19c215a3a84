/* eslint-disable react/prop-types */
import React from 'react';
import Button from '@material-ui/core/Button';
import Tooltip from '@material-ui/core/Tooltip';
import FormControl from '@material-ui/core/FormControl';
import FormHelperText from '@material-ui/core/FormHelperText';
import { withStyles } from '@material-ui/core/styles';
import AttachFileIcon from '@material-ui/icons/AttachFile';
import useOnlineStatus from '@rehooks/online-status';
import { generalClasses as styles } from './styles';

const IMPORT = 'Importar';
const FOOTER_IMPORT = 'Formato CSV max 10MB';

const ImportButton = props => {
  const {
    classes,
    errorMessage = [],
    footer = FOOTER_IMPORT,
    onClick,
    disabled,
    text = IMPORT
  } = props;
  const onlineStatus = useOnlineStatus();
  return (
    <React.Fragment>
      <FormControl className={classes.formControl}>
        <Tooltip title={text} aria-label={text}>
          <span>
            <Button
              className={classes.importButton}
              variant="contained"
              size="small"
              color="primary"
              onClick={onClick}
              disabled={disabled || !onlineStatus}
            >
              <AttachFileIcon />
            </Button>
          </span>
        </Tooltip>
        <FormHelperText id="component-helper-text" error={errorMessage.length > 0}>
          {errorMessage.join(', ') || footer}
        </FormHelperText>
      </FormControl>
    </React.Fragment>
  );
};

export default withStyles(styles)(ImportButton);
