import React from 'react';

import MuiAlert from '@material-ui/lab/Alert';
import { Fade } from '@material-ui/core';

function Alert(props) {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
}

const LoadingNetwork = () => (
  <Fade in timeout={{ enter: 1000 }}>
    <Alert severity="warning" variant="filled">
      Sin conexión a Internet. Comprueba la conexión e inténtalo de nuevo
    </Alert>
  </Fade>
);

export default LoadingNetwork;
