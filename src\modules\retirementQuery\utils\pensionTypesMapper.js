const transformDiacritic = value =>
  value
    .replace(/[aàáâãäå]/gi, '[aàáâãäå]')
    .replace(/[eèéê<PERSON>]/gi, '[eèéê<PERSON>]')
    .replace(/[iì<PERSON><PERSON><PERSON>]/gi, '[i<PERSON><PERSON><PERSON><PERSON>]')
    .replace(/[oòóôõö]/gi, '[oòóôõö]')
    .replace(/[uùúûü]/gi, '[uùúûü]')
    .replace(/[nñ]/gi, '[nñ]');

const pensionTypes = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i,
  /Pensi[oó]n de viudez con hijos/i,
  /Pensi[oó]n de viudez sin hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial sin hijos/i,
  /Pensi[oó]n por orfandad/i,
  /Pensi[oó]n de orfandad de padre y madre/i
];

const isPensionTypeEditable = currentPensionType =>
  pensionTypes.some(pensionTypeRegex => pensionTypeRegex.test(currentPensionType));

const accidentOrDiseasePensions = [
  'Pensión por accidente de trabajo',
  'Pensión por accidente de trayecto',
  'Pensión por enfermedad profesional'
];

const widowhoodPensions = ['Pensión de viudez con hijos', 'Pensión de viudez sin hijos'];

const motherhoodPensions = [
  'Pensión de madre de hijo de filiación no matrimonial sin hijos',
  'Pensión de madre de hijo de filiación no matrimonial con hijos'
];

const orphanhoodPensions = ['Pensión de orfandad de padre y madre', 'Pensión por orfandad'];

const isPensionerWithFamilyCharges = selectedValue =>
  [
    'Pensión de viudez con hijos',
    'Pensión de madre de hijo de filiación no matrimonial con hijos'
  ].includes(selectedValue);

const changePensionTypeOptions = currentPensionType =>
  [
    accidentOrDiseasePensions,
    widowhoodPensions,
    motherhoodPensions,
    orphanhoodPensions
  ].find(pensionList =>
    pensionList.some(pensionType =>
      new RegExp(transformDiacritic(pensionType), 'i').test(currentPensionType)
    )
  );

const PensionTypesInstitutionalPatient = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i
];

const isSettlementPaymentEditable = currentPensionType =>
  PensionTypesInstitutionalPatient.some(typeRegex => typeRegex.test(currentPensionType));

export {
  isPensionTypeEditable,
  changePensionTypeOptions,
  isSettlementPaymentEditable,
  isPensionerWithFamilyCharges
};
