/* eslint-disable import/prefer-default-export */
/* eslint-disable no-console */
import { axiosRequest } from '../../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;

const api = REACT_APP_API_URL;

//------------------------------------------------------------------------------------------------
export const getRole = async (roleName, setViewPrivileges) => {
  const { data } = await axiosRequest.get(`${api}/system/get-role/${roleName}`).catch(err => {
    console.error(err);
    return { data: { views: [] }, isError: true };
  });

  const { views } = data || {};

  setViewPrivileges(views);
};
//------------------------------------------------------------------------------------------------
export const updateRole = async ({ roleName, view, newPermission }) => {
  const { data: completed, isError } = await axiosRequest
    .post(`${api}/system/update-role`, { roleName, view, newPermission })
    .catch(err => {
      console.error(err);
      return { data: {}, isError: true };
    });
  return { completed, isError };
};
//------------------------------------------------------------------------------------------------
export const getRoles = async setRoles => {
  const {
    data: { roles },
    isError
  } = await axiosRequest.get(`${api}/system/get-all-roles`).catch(err => {
    console.error(err);
    return { data: { roles: [] }, isError: true };
  });
  if (!roles?.length || isError) {
    setRoles([]);
  }

  setRoles(roles);
};
