/* eslint-disable import/no-unresolved */
/* eslint-disable react/prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import createTable from '../../utils/createTable';
import useStyles from '../pensionerDetail/style';
import {
  ALPHANUMERIC_DIACRITICS_PUNCTUATIONMARK,
  LETTERS_DIACRITICS,
  alphaNumericPunctuationMarkSanitizer,
  letterDiacriticsSanitizer,
  checkRutAndDV,
  dynamicFormatting
} from '../../utils/formatters';

import {
  setCollectorRetentionRut,
  setCollectorRetentionName,
  setCollectorRetentionLastName,
  setCollectorRetentionMothersLastName,
  setCollectorRetentionAddress,
  setCollectorRetentionCommune,
  setCollectorRetentionCity,
  setModifiedFieldErrors
} from '../../actions';

const handleRutChange = (
  e,
  { key, dispatch, action, actionError, modifiedFieldErrors, formatter, validation }
) => {
  const { value } = e.target;
  const formattedRut = formatter(value);
  const isCorrect = validation(formattedRut);
  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));

  dispatch(action(formattedRut));
};

const handleWordChange = (
  e,
  {
    key,
    dispatch,
    action,
    actionError,
    modifiedFieldErrors,
    formatter,
    validation,
    isRequired,
    maxLength
  }
) => {
  const { value } = e.target;

  const formattedWord = formatter(value) || '';

  const isValidFormatAndLength =
    formattedWord.length <= maxLength && validation.test(formattedWord);

  const isCorrect = isRequired
    ? isValidFormatAndLength
    : formattedWord.length === 0 || isValidFormatAndLength;
  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));
  dispatch(action(formattedWord));
};

const CollectorCardJudicialRetention = ({
  values,
  _rutBeneficiario,
  _rutCausante,
  editable,
  readOnly
}) => {
  const classes = useStyles();
  const dispatch = useDispatch();

  const collectorRetentionRut = useSelector(store => store.queryPensions.collectorRetentionRut);
  const collectorRetentionName = useSelector(store => store.queryPensions.collectorRetentionName);
  const collectorRetentionLastName = useSelector(
    store => store.queryPensions.collectorRetentionLastName
  );
  const collectorRetentionMothersLastName = useSelector(
    store => store.queryPensions.collectorRetentionMothersLastName
  );
  const collectorRetentionAddress = useSelector(
    store => store.queryPensions.collectorRetentionAddress
  );
  const collectorRetentionCommune = useSelector(
    store => store.queryPensions.collectorRetentionCommune
  );
  const collectorRetentionCity = useSelector(store => store.queryPensions.collectorRetentionCity);

  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);

  const rowFormation = [
    [
      {
        key: 'collectorRetentionRut',
        name: 'RUT cobrante',
        type: 'text',
        toWrite: collectorRetentionRut,
        handleInputChange: handleRutChange,
        condition: editable,
        validation: checkRutAndDV,
        formatter: dynamicFormatting,
        isRequired: true,
        errorMessage: 'RUT inválido',
        dispatch,
        action: setCollectorRetentionRut,
        modifiedField: collectorRetentionRut,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors
      },
      {
        key: 'collectorRetentionAddress',
        name: 'Dirección cobrante',
        type: 'text',
        toWrite: collectorRetentionAddress,
        handleInputChange: handleWordChange,
        condition: true,
        validation: ALPHANUMERIC_DIACRITICS_PUNCTUATIONMARK,
        formatter: alphaNumericPunctuationMarkSanitizer,
        errorMessage: 'El valor es inválido',
        dispatch,
        action: setCollectorRetentionAddress,
        modifiedField: collectorRetentionAddress,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 1000
      },
      {
        key: 'collectorRetentionCommune',
        name: 'Comuna cobrante',
        type: 'text',
        toWrite: collectorRetentionCommune,
        handleInputChange: handleWordChange,
        condition: true,
        validation: ALPHANUMERIC_DIACRITICS_PUNCTUATIONMARK,
        formatter: alphaNumericPunctuationMarkSanitizer,
        errorMessage: 'El valor es inválido',
        dispatch,
        action: setCollectorRetentionCommune,
        modifiedField: collectorRetentionCommune,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 1000
      }
    ],
    [
      {
        key: 'collectorRetentionName',
        name: 'Nombre cobrante',
        type: 'text',
        toWrite: collectorRetentionName,
        handleInputChange: handleWordChange,
        condition: editable,
        validation: LETTERS_DIACRITICS,
        formatter: letterDiacriticsSanitizer,
        isRequired: true,
        errorMessage: !collectorRetentionName
          ? 'El valor no se ha ingresado'
          : 'El valor es inválido',
        dispatch,
        action: setCollectorRetentionName,
        modifiedField: collectorRetentionName,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 60
      },
      {
        key: 'collectorRetentionLastName',
        name: 'Apellido paterno cobrante',
        type: 'text',
        toWrite: collectorRetentionLastName,
        handleInputChange: handleWordChange,
        condition: editable,
        validation: LETTERS_DIACRITICS,
        formatter: letterDiacriticsSanitizer,
        isRequired: true,
        errorMessage: !collectorRetentionLastName
          ? 'El valor no se ha ingresado'
          : 'El valor es inválido',
        dispatch,
        action: setCollectorRetentionLastName,
        modifiedField: collectorRetentionLastName,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 60
      },
      {
        key: 'collectorRetentionCity',
        name: 'Ciudad cobrante',
        type: 'text',
        toWrite: collectorRetentionCity,
        handleInputChange: handleWordChange,
        condition: true,
        validation: ALPHANUMERIC_DIACRITICS_PUNCTUATIONMARK,
        formatter: alphaNumericPunctuationMarkSanitizer,
        errorMessage: 'El valor es inválido',
        dispatch,
        action: setCollectorRetentionCity,
        modifiedField: collectorRetentionCity,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 1000
      }
    ],
    [
      {
        key: 'collectorRetentionMothersLastName',
        name: 'Apellido materno cobrante',
        type: 'text',
        toWrite: collectorRetentionMothersLastName,
        handleInputChange: handleWordChange,
        condition: editable,
        validation: LETTERS_DIACRITICS,
        formatter: letterDiacriticsSanitizer,
        errorMessage: 'El valor es inválido',
        dispatch,
        action: setCollectorRetentionMothersLastName,
        modifiedField: collectorRetentionMothersLastName,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 60
      }
    ]
  ];

  return (
    <>
      <Card className={classes.cardContainer}>
        <CardHeader title="Información cobrante" className={classes.cardHeader} />
        <Divider />
        <fieldset disabled={readOnly}>
          <CardContent className={classes.content}>
            {createTable({
              data: values,
              format: rowFormation,
              editable: true, // editable && onlineStatus,
              panelName: 'collector'
            })}
          </CardContent>
        </fieldset>
      </Card>
    </>
  );
};

CollectorCardJudicialRetention.propTypes = {
  values: PropTypes.shape({}).isRequired,
  editable: PropTypes.bool.isRequired
};

export default CollectorCardJudicialRetention;
