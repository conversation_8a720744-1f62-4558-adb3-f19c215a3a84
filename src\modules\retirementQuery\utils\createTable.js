/* eslint-disable react/no-array-index-key */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react/prop-types */
import React, { Fragment } from 'react';
import { TableRow, Table, TableBody, TextField } from '@material-ui/core';
import moment from 'moment';
import CustomCell from '../components/pensionerDetail/CustumCell';
import { datePickerStyles } from '../components/picker/accidentPickerStyles';
import Picker from '../components/picker/accidentPicker';

const LITTLE_ENDIAN_LENGTH = 10;

const styles = {
  title: {
    fontWeight: 'bolder',
    minWidth: 120,
    maxWidth: 350
  },
  data: {
    minWidth: 120,
    maxWidth: 200,
    wordBreak: 'break-word'
  },
  select: {
    minWidth: 150,
    maxWidth: 200,
    wordBreak: 'break-all',
    display: 'block',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis'
  }
};

const mutableFields = {
  select: props => {
    const {
      data,
      key,
      options = [],
      handleInputChange,
      onlineStatus = true,
      selectedValue,
      dispatch,
      action,
      modifiedField,
      actionError,
      modifiedFieldErrors = {},
      isRequired,
      errorMessage
    } = props;
    return (
      <TextField
        id="outlined-select-native"
        select
        value={selectedValue}
        InputLabelProps
        onChange={e => {
          handleInputChange(e, {
            dispatch,
            action,
            modifiedField,
            actionError,
            modifiedFieldErrors,
            key,
            data,
            isRequired,
            selectedValue,
            options
          });
        }}
        disabled={!onlineStatus}
        SelectProps={{
          native: true
        }}
        error={modifiedFieldErrors[key]}
        helperText={modifiedFieldErrors[key] ? errorMessage : ''}
        variant="outlined"
        margin="dense"
        style={styles.select}
      >
        {options.map(opt => (
          <option key={opt} value={opt}>
            {opt}
          </option>
        ))}
      </TextField>
    );
  },
  text: props => {
    const {
      key,
      toWrite = '',
      handleInputChange,
      errorMessage,
      validation,
      isRequired,
      dispatch,
      action,
      modifiedField,
      actionError,
      modifiedFieldErrors = {},
      maxLength,
      data,
      formatter
    } = props;
    const helperfn = () => {
      if (modifiedFieldErrors[key]) return errorMessage;
      if (isRequired && !toWrite) return 'El valor no se ha ingresado';
      if (maxLength && toWrite?.length > maxLength)
        return 'El valor excede el máximo de caracteres';
      return '';
    };

    return (
      <TextField
        id={`${key}-textField`}
        value={toWrite}
        onChange={e =>
          handleInputChange(e, {
            key,
            dispatch,
            action,
            modifiedField,
            actionError,
            modifiedFieldErrors,
            maxLength,
            validation,
            data,
            isRequired,
            formatter
          })
        }
        helperText={helperfn()}
        error={modifiedFieldErrors[key] || (isRequired && !toWrite)}
        InputLabelProps={{
          shrink: true
        }}
        autoComplete="off"
        variant="outlined"
        size="small"
      />
    );
  },
  picker: props => {
    const {
      key,
      toWrite = '',
      handleInputChange,
      minDate,
      maxDate,
      label,
      errorMessage,
      classes,
      dispatch,
      action,
      actionError,
      modifiedFieldErrors
    } = props;

    let formatedToWrite = toWrite;

    if (toWrite?.length === LITTLE_ENDIAN_LENGTH) {
      const [day, month, year] = toWrite?.split('-') || [];

      formatedToWrite = moment(new Date(year, month - 1, day), 'DD-MM-YYYY').toDate();
    }

    return (
      <Picker
        className={classes}
        label={label}
        value={formatedToWrite}
        error={modifiedFieldErrors[key]}
        helperText={modifiedFieldErrors[key] ? errorMessage : ''}
        minDate={minDate}
        maxDate={maxDate}
        onChange={e =>
          handleInputChange(e, {
            key,
            dispatch,
            action,
            actionError,
            modifiedFieldErrors
          })
        }
        MuiStyle={datePickerStyles}
      />
    );
  }
};

const displayUnmodified = props => {
  const { data, key, dispatch, actionError, modifiedFieldErrors } = props;

  if (dispatch && actionError && modifiedFieldErrors && modifiedFieldErrors[key]) {
    dispatch(actionError({ ...modifiedFieldErrors, [key]: false }));
  }

  return data[key] || '-';
};

export default ({ data, format, tableStyle = {}, editable, panelName }) => {
  return (
    <Table key={`tabla-${JSON.stringify(data)}`} style={tableStyle}>
      <TableBody key={`body-table-${panelName}`}>
        {format.map((row, index) => (
          <TableRow id={`table-${index}-${panelName}`} key={`table-${index}-${panelName}`}>
            {row.map(
              ({
                key,
                name,
                type,
                options,
                handleInputChange,
                condition,
                selectedValue,
                toWrite,
                validation,
                errorMessage,
                isRequired,
                dispatch,
                action,
                modifiedField,
                actionError,
                modifiedFieldErrors,
                maxLength,
                formatter,
                classes,
                maxDate,
                minDate
              }) => (
                <Fragment key={`${key}-row`}>
                  <CustomCell id={`${key}-label`} key={`${key}-label`} style={styles.title}>
                    {name}
                  </CustomCell>
                  {(!editable || !condition) && (
                    <CustomCell id={`${key}-value`} key={`${key}-value`} style={styles.data}>
                      {displayUnmodified({
                        data,
                        key,
                        dispatch,
                        modifiedField,
                        action,
                        actionError,
                        modifiedFieldErrors,
                        isRequired
                      })}
                    </CustomCell>
                  )}
                  {editable && condition && (
                    <CustomCell style={styles.data}>
                      {mutableFields[type]({
                        options,
                        type,
                        key,
                        handleInputChange,
                        data,
                        selectedValue,
                        toWrite,
                        validation,
                        errorMessage,
                        isRequired,
                        dispatch,
                        action,
                        modifiedField,
                        actionError,
                        modifiedFieldErrors,
                        maxLength,
                        formatter,
                        classes,
                        maxDate,
                        minDate
                      })}
                    </CustomCell>
                  )}
                </Fragment>
              )
            )}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
