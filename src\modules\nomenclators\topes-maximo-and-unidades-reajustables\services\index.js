/* eslint-disable no-console */
/* eslint-disable import/prefer-default-export */
import { updateNomenclatorCaja, loadNomenclatorCaja } from '../actions';

//-----------------------------------------------------------------------------------------------------------
export const processCaja = (prevCaja, newCaja, operation) => async (
  dispatch,
  _getState,
  { api, axiosRequest }
) => {
  const createdCaja = newCaja;
  createdCaja.percentage = newCaja.percentage.replace(',', '.');
  createdCaja.isMaxAmount = newCaja.isMaxAmount === 'Si' ? true : false;
  if (operation === 'update') {
    const {
      data: { error, isError }
    } = await updateCaja({ axiosRequest, api, caja: createdCaja });
    if (isError) {
      throw new Error(error.code);
    }
    createdCaja.percentage = createdCaja.percentage.replace('.', ',');
    createdCaja.isMaxAmount = createdCaja.isMaxAmount === true ? 'Si' : 'No';
    await dispatch(updateNomenclatorCaja(prevCaja, createdCaja));
  }
};

const updateCaja = async ({ axiosRequest, api, caja }) =>
  axiosRequest.put(`${api}/nomenclators/caja`, { caja });

//-----------------------------------------------------------------------------------------------------------
export const loadCaja = () => async (dispatch, _getState, { api, axiosRequest }) => {
  const {
    data: { result: boxes }
  } = await axiosRequest.get(`${api}/nomenclators/caja`).catch(err => {
    console.error(err);
    return { data: { result: [] } };
  });

  const compensationBox = boxes.map(box => {
    const { isMaxAmount, percentage } = box;
    return {
      ...box,
      isMaxAmount: isMaxAmount ? 'Si' : 'No',
      percentage: percentage.toString().replace('.', ',')
    };
  });

  await dispatch(loadNomenclatorCaja(compensationBox));
  return compensationBox;
};
