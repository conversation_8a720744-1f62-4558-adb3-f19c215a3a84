import moment from 'moment';

const checkStartingLEQEnding = (startingDate, endingDate, setIsStartingLeqEnding) => {
  const starting = moment(startingDate).startOf('month');
  const ending = moment(endingDate).startOf('month');

  const isLarger = starting.diff(ending, 'months') > 0;

  isLarger ? setIsStartingLeqEnding(false) : setIsStartingLeqEnding(true);
  return isLarger;
};

export default checkStartingLEQEnding;
