/* eslint-disable react/prop-types */
import React from 'react';
import { KeyboardDatePicker } from '@material-ui/pickers';
import 'moment/locale/es';
import { ThemeProvider } from '@material-ui/styles';

const Picker = ({
  className,
  disabled,
  label,
  value,
  error,
  helperText,
  minDate,
  maxDate,
  onChange,
  MuiStyle
}) => {
  return (
    <div>
      <ThemeProvider theme={MuiStyle}>
        <KeyboardDatePicker
          lang="es"
          autoOk
          className={className.calendar}
          disabled={disabled}
          disableFuture
          allowKeyboardControl={false}
          rightArrowIcon
          emptyLabel={label}
          format="MM-YYYY"
          value={value}
          inputVariant="outlined"
          error={error}
          helperText={helperText}
          minDate={minDate}
          maxDate={maxDate}
          label={label}
          variant="inline"
          views={['month', 'year']}
          size="small"
          onChange={e => onChange(e)}
          InputLabelProps={{
            shrink: true
          }}
        />
      </ThemeProvider>
    </div>
  );
};

export default Picker;
