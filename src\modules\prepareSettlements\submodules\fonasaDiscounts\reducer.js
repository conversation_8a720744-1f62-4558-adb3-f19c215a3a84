import {
  SET_FONASA_INVALID_FILENAME,
  CLEAN_FONASA_FILENAME_ERRORS,
  SET_FONASA_FILE_ERROR,
  CLEAN_FONASA_FILE_ERRORS,
  ENABLE_FILE_UPLOAD,
  DISABLE_FILE_UPLOAD,
  STORE_JSON,
  CLEAN_JSON,
  WAS_FONASA_EXECUTED,
  SET_IN_DAYS_LIMIT_RANGE,
  SET_IN_NUMBER_DAYS_LIMIT_RANGE,
  IS_UPLOADING,
  SET_CURRENT_DATE,
  SET_CRON_BASE_MINIMUM_PENSION_EXECUTED
} from './actions';

const initialState = {
  isError: false,
  filenameErrors: [],
  fileErrors: [],
  enableUpload: false,
  file: [],
  wasFonasaProcessExecuted: true,
  isInDaysLimitRange: false,
  isUploading: false,
  currentDate: ''
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case SET_FONASA_INVALID_FILENAME:
      return {
        ...state,
        filenameErrors: [...state.filenameErrors, action.data]
      };
    case CLEAN_FONASA_FILENAME_ERRORS:
      return {
        ...state,
        filenameErrors: []
      };
    case SET_FONASA_FILE_ERROR:
      return {
        ...state,
        fileErrors: [...state.fileErrors, action.data]
      };
    case CLEAN_FONASA_FILE_ERRORS:
      return {
        ...state,
        fileErrors: []
      };
    case ENABLE_FILE_UPLOAD:
    case DISABLE_FILE_UPLOAD:
      return {
        ...state,
        enableUpload: action.data
      };    
    case STORE_JSON:
      return {
        ...state,
        file: action.data
      };
    case CLEAN_JSON:
      return {
        ...state,
        file: []
      };
    case WAS_FONASA_EXECUTED:
      return {
        ...state,
        wasFonasaProcessExecuted: action.data
      };
    case SET_IN_DAYS_LIMIT_RANGE:
      return {
        ...state,
        isInDaysLimitRange: action.data.isInDaysLimitRange
      };
    case SET_IN_NUMBER_DAYS_LIMIT_RANGE:
      return {
        ...state,
        isInNumberDaysLimitRange: action.data.isInNumberDaysLimitRange
      };        
    case SET_CRON_BASE_MINIMUM_PENSION_EXECUTED:
      return {
        ...state,
        cronBaseMinimumPensionExecuted: action.data.cronBaseMinimumPensionExecuted
      };
    case IS_UPLOADING:
      return {
        ...state,
        isUploading: action.data
      };
    case SET_CURRENT_DATE:
      return {
        ...state,
        currentDate: action.data
      };
    default:
      return state;
  }
}
