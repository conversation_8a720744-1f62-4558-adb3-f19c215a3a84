/* eslint-disable consistent-return */
/* eslint-disable no-underscore-dangle */
/* eslint-disable no-console */
import { axiosRequest } from '../../../../services/axiosRequest';

import { updateNomenclatorUser, loadNomenclatorUser, createNomenclatorUser } from '../actions';

const { REACT_APP_API_URL } = process.env;

const api = REACT_APP_API_URL && REACT_APP_API_URL.replace(/(\/)$/, '');

//------------------------------------------------------------------------------------------------
export const getUsers = async (roleName = '') => {
  const { data } = await axiosRequest.get(`${api}/users?role=${roleName}`).catch(err => {
    console.error(err);
    return { data: [], isError: true };
  });

  if (!data || !data.result) {
    return [];
  }

  return data.result;
};

//------------------------------------------------------------------------------------------------
export const createUser = async user => {
  const { data, error } = await axiosRequest.post(`${api}/users`, user).catch(err => {
    console.error(err);

    return { data: {}, error: { ...err, code: err.code || err.response.status } };
  });

  return { data, error };
};

//------------------------------------------------------------------------------------------------

export const updateUser = async ({ user }) => {
  // eslint-disable-next-line no-underscore-dangle
  const { error } = await axiosRequest.put(`${api}/users/${user._id}`, { ...user }).catch(err => {
    console.error(err);
    return { data: {}, error: err };
  });
  return { error };
};

//------------------------------------------------------------------------------------------------
export const deleteUser = async userId => {
  const { data } = await axiosRequest.delete(`${api}/users/${userId}`).catch(err => {
    console.error(err);
    return { data: {}, isError: true };
  });

  return { data };
};

//------------------------------------------------------------------------------------------------
export const processUser = (prevUser, newUser, operation) => async (dispatch, getState) => {
  let createdUser = newUser;

  if (operation === 'create') {
    const {
      data: { result },
      error
    } = await createUser({ user: createdUser });

    if (error) {
      throw new Error(error.code);
    }
    createdUser = result;

    return dispatch(createNomenclatorUser(createdUser));
  }
  if (operation === 'update' || operation === 'delete') {
    const { error } = await updateUser({ user: createdUser });
    if (error) {
      throw new Error(error.code);
    }
    return dispatch(updateNomenclatorUser(prevUser, createdUser));
  }
};

//------------------------------------------------------------------------------------------------
export const loadUsers = async (dispatch, roleName = '') => {
  const {
    data: { result }
  } = await axiosRequest.get(`${api}/users?role=${roleName}`).catch(err => {
    console.error(err);
    return { data: { result: [] } };
  });
  if (!result.length) {
    return dispatch(loadNomenclatorUser([]));
  }

  return dispatch(loadNomenclatorUser(result));
};
