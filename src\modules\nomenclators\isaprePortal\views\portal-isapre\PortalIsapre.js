/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React, { useEffect, Fragment } from 'react';
import useOnlineStatus from '@rehooks/online-status';
import ImportButton from 'components/ImportButton';
import { Grid, Fade, Button, Tooltip } from '@material-ui/core';
import MuiAlert from '@material-ui/lab/Alert';
import useRouter from 'utils/useRouter';
import { useSelector, useDispatch } from 'react-redux';

import { useProgress } from 'components';
import useStyles from '../styles';
import useDataUpload from '../../hooks/useDataUpload';
import {
  checkIsaprePortalAvailabilityProcess,
  updateIsaprePensions,
  wasExecutedThisMonth
} from '../../services/isaprePortal.service';
import { cleanFileError, cleanIsaprePortalResumeErrors } from '../../actions';

const FOOTER_IMPORT = 'Formato xlsx max 10MB';

const Alert = props => <MuiAlert elevation={6} variant="filled" {...props} />;

const renderAlert = (inDayRange, availableDay, display) => {
  if (inDayRange === undefined) return null;
  const allowMesagge = `Hasta el día hábil número ${availableDay} del mes actual, podrá  realizar la carga de un archivo`;
  const denyMessage = `No se puede realizar la carga del archivo, han pasado los primeros ${availableDay} días hábiles del mes permitidos`;
  const variant = inDayRange ? 'filled' : 'outlined';
  const severity = inDayRange ? 'info' : 'error';
  const message = inDayRange ? allowMesagge : denyMessage;

  return (
    display && (
      <div>
        <Fade in timeout={{ enter: 1000 }}>
          <Alert severity={severity} variant={variant}>
            {message}
          </Alert>
        </Fade>
      </div>
    )
  );
};

const PortalIsapre = ({ onSuccessImport, onErrorImport }) => {
  const fileUploadInput = React.createRef();
  const router = useRouter();
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();
  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const fileErrors = useSelector(store => store.isaprePortal.fileErrors);
  const isIsaprePortalEnable = useSelector(store => store.isaprePortal.isIsaprePortalEnable);
  const isEnableIsaprePortalImport = useSelector(
    store => store.isaprePortal.isEnableIsaprePortalImport
  );

  const isIsaprePortalAvailable = useSelector(store => store.isaprePortal.isIsaprePortalAvailable);
  const daysToExecuteIsaprePortalProcess = useSelector(
    store => store.isaprePortal.daysToExecuteIsaprePortalProcess
  );

  const apiCall = useSelector(store => store.isaprePortal.apiCall);

  const handleUploadButton = () => {
    fileUploadInput.current.value = '';
    fileUploadInput.current.click();
  };

  const handleUpdateIsaprePortalPension = () =>
    dispatch(updateIsaprePensions(onSuccessImport, onErrorImport));

  const verifyIsaprePortalConditions = () => !isIsaprePortalEnable || !onlineStatus;

  const handleFileUpload = useDataUpload(
    dispatch,
    router,
    progress,
    onSuccessImport,
    onErrorImport
  );

  const enabledImport = () =>
    !isIsaprePortalAvailable || isEnableIsaprePortalImport || !onlineStatus;

  useEffect(() => {
    dispatch(cleanFileError());
    dispatch(cleanIsaprePortalResumeErrors());
    dispatch(wasExecutedThisMonth());

    checkIsaprePortalAvailabilityProcess(dispatch);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Fragment>
      {renderAlert(isIsaprePortalAvailable, daysToExecuteIsaprePortalProcess, apiCall)}
      <Grid
        className={classes.formControl}
        container
        direction="row"
        justify="space-around"
        alignItems="center"
      >
        <Grid item>
          <ImportButton
            classes={{ formControl: classes.formControl }}
            errorMessage={fileErrors}
            onClick={handleUploadButton}
            footer={FOOTER_IMPORT}
            disabled={enabledImport()}
          />
        </Grid>
        <Grid item>
          <Tooltip title="Finalizar" aria-label="inactReactTooltip">
            <span>
              <Button
                variant="contained"
                disabled={verifyIsaprePortalConditions()}
                onClick={handleUpdateIsaprePortalPension}
              >
                Finalizar
              </Button>
            </span>
          </Tooltip>
        </Grid>
      </Grid>

      <input
        ref={fileUploadInput}
        type="file"
        style={{ display: 'none' }}
        id="fileUpload"
        disabled={!onlineStatus}
        onChange={v => v.target.files.length && handleFileUpload(v.target.files[0])}
        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      />
    </Fragment>
  );
};

export default PortalIsapre;
