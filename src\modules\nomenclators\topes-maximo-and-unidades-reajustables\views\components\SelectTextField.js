/* eslint-disable no-underscore-dangle */
import PropTypes from 'prop-types';
import React from 'react';
import TextField from '@material-ui/core/TextField';

const SelectTextField = ({
  name,
  handler,
  displayName,
  value,
  onChange,
  inputprops,
  options,
  ...props
}) => {
  return (
    <>
      <TextField
        id="outlined-select-native"
        select
        value={value || ''}
        InputLabelProps
        onChange={onChange}
        SelectProps={{
          native: true
        }}
        placeholder="Seleccione"
        variant="outlined"
        margin="dense"
        error={!value}
        helperText={!value && 'El campo es obligatorio'}
      >
        <option key="No" value="No">
          No
        </option>
        <option key="Si" value="Si">
          Si
        </option>
      </TextField>
    </>
  );
};
SelectTextField.propTypes = {
  name: PropTypes.string.isRequired,
  handler: PropTypes.func,
  displayName: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  isValidListener: PropTypes.func,
  validators: PropTypes.arrayOf(PropTypes.string),
  errorMessages: PropTypes.arrayOf(PropTypes.string),
  inputprops: PropTypes.objectOf(PropTypes.string).isRequired,
  options: PropTypes.arrayOf(Object)
};
SelectTextField.defaultProps = {
  handler: () => {
    /* any */
  },
  isValidListener: () => {
    /* any */
  },
  validators: ['required', 'match'],
  errorMessages: ['El valor es requerido', 'El valor es inválido'],
  options: []
};

export default SelectTextField;
