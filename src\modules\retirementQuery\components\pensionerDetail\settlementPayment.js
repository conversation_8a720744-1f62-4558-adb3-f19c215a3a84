/* eslint-disable no-param-reassign */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/no-unused-prop-types */
/* eslint-disable react/prop-types */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import { useSelector, useDispatch } from 'react-redux';
import useStyles from './style';
import createTable from '../../utils/createTable';
import { checkPensionerDocuments } from '../../../../modules/retirementQuery/services/queryPensions.service';
import {
  setPaymentGateway,
  setBank,
  setAccountNumber,
  setBranchOffice,
  setModifiedFieldErrors,
  setBankRejected,
  setPaycheckRefunded,
  setInstitutionalPatient,
  setWasModifiedAFieldThatRequiresAFile,
  setPaymentGatewayOld,
  setAttachNeurologicalCertificate
} from '../../actions';
import genericHandle from '../../utils/genericHandler';

import {
  paymentGatewayList,
  bankOnlyRelatedOptions,
  bankRelatedPaymentGateway,
  voucherBankRelatedOptions,
  branchOfficeRelatedFields,
  voucherServipagRelatedOptions
} from '../../utils/paymentGatewayList';
import bankList from '../../utils/bankList';

import { NUMERIC_ONLY, numericOnlySanitizer } from '../../utils/formatters';

import { generateAndUploadBankFileExecuted } from '../../services/queryPensions.service';

const booleanOptions = ['No', 'Sí'];
const bcoChile = /Banco de Chile/i;
let selectChange = false;
let searchFiles = false;

const nameAndRegex = {
  court: {
    name: 'Dictamen_de_tribunales',
    regex: 'tribunales',
    existence: false
  },
  neurological: {
    name: 'Certificado_neurologico',
    regex: 'neurologico',
    existence: false
  },
  changeCollector: {
    name: 'Poder_notarial_para_cambio_de_cobrante',
    regex: 'notarial',
    existence: false
  },
  AFPaffiliation: {
    name: 'Certificado_de_afiliacion_AFP',
    regex: 'AFP',
    existence: false
  }
};

const handleAccountChange = (
  e,
  {
    key,
    dispatch,
    action,
    actionError,
    modifiedFieldErrors,
    formatter,
    validation,
    isRequired,
    maxLength
  }
) => {
  const { value } = e.target;

  const formattedWord = formatter(value) || '';

  const isValidFormatAndLength =
    formattedWord.length <= maxLength && validation.test(formattedWord);

  const isCorrect = isRequired
    ? isValidFormatAndLength
    : formattedWord.length === 0 || isValidFormatAndLength;
  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));
  dispatch(action(formattedWord));
};

const handlePaymentGatewayAndBankChange = (
  e,
  { dispatch, action, actionError, modifiedFieldErrors, key, isRequired, options }
) => {
  const { value } = e.target;

  if(key === 'paymentGateway'){
    selectChange = true;          
  }  
  
  const isCorrect = isRequired ? value !== options[0] : true;
  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));
  dispatch(action(value));  
};

const handledBankRejectedChange = (e, { dispatch, action }) => {
  const { value } = e.target;

  dispatch(action(value));
};

const handledPaycheckRefundedChange = (e, { dispatch, action }) => {
  const { value } = e.target;

  dispatch(action(value));
};

const notEqualToPaymentRetention = (paymentGatewayOld, paymentGateway, dispatch) => {
  if(selectChange && paymentGatewayOld !== paymentGatewayList[8] && paymentGateway !== paymentGatewayList[8]){                   
    dispatch(setAttachNeurologicalCertificate(false));        
  } 
};

const equalToPaymentOldDetained = (paymentGatewayOld, paymentGateway, dispatch) => {
  if(selectChange && paymentGatewayOld === paymentGatewayList[8] && paymentGateway === paymentGatewayList[8]){      
    dispatch(setWasModifiedAFieldThatRequiresAFile(false));         
    dispatch(setInstitutionalPatient('Si'));
    selectChange = false;
  } 
};

const paymentWithheldToAnotherOption = (paymentGatewayOld, paymentGateway, dispatch) => {
  if(selectChange && paymentGatewayOld === paymentGatewayList[8] && paymentGateway !== paymentGatewayList[8]){      
    dispatch(setInstitutionalPatient('No'));
    dispatch(setWasModifiedAFieldThatRequiresAFile(true));
    selectChange = false;
  }    
};


const withheldPaymentWithValueNot = (paymentGateway, institutionalPatient, dispatch) => {
  if(selectChange && paymentGateway === paymentGatewayList[8] && /No/i.test(institutionalPatient)){        
    dispatch(setInstitutionalPatient('Si'));   
    dispatch(setWasModifiedAFieldThatRequiresAFile(true));
    selectChange = false;
  }
};

const withheldPaymentWithValueYesAndNotFile = (paymentGateway, institutionalPatient, fileExist, dispatch) => {
  if(selectChange && paymentGateway === paymentGatewayList[8] && /Si/i.test(institutionalPatient) && !fileExist){      
    dispatch(setWasModifiedAFieldThatRequiresAFile(true));
    dispatch(setAttachNeurologicalCertificate(true));        
    selectChange = false;
  }
};

const equalToPaymentDetained = (paymentGateway, institutionalPatientValue, dispatch) => {
  if(selectChange && paymentGateway !== paymentGatewayList[8]){      
    dispatch(setWasModifiedAFieldThatRequiresAFile(false));         
    dispatch(setInstitutionalPatient(institutionalPatientValue));
    selectChange = false;
  }   
};

const SettlementPayment = ({ values, editable }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();

  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);

  const bankBranchOfficeList = useSelector(store => store.queryPensions.bankBranchOfficeList);
  const servipagBranchOfficeList = useSelector(
    store => store.queryPensions.servipagBranchOfficeList
  );

  const paymentGateway = useSelector(store => store.queryPensions.paymentGateway);
  let bank = useSelector(store => store.queryPensions.bank);
  const branchOffice = useSelector(store => store.queryPensions.branchOffice);
  const accountNumber = useSelector(store => store.queryPensions.accountNumber);

  const bankRejected = useSelector(store => store.queryPensions.bankRejected);
  const paycheckRefunded = useSelector(store => store.queryPensions.paycheckRefunded);
  const dependencyExecuted = useSelector(store => store.queryPensions.dependencyExecuted);
  const institutionalPatient = useSelector(store => store.queryPensions.institutionalPatient);
  const paymentGatewayOld = useSelector(store => store.queryPensions.paymentGatewayOld);  

  const [validateFiles, setValidateFiles] = useState(nameAndRegex);
  const { beneficiaryRut, causantRut, pensionCodeId } = values;
  if(!searchFiles){
    checkPensionerDocuments({ beneficiaryRut, causantRut, pensionCodeId, nameAndRegex }, setValidateFiles);
    searchFiles = true;
  }
  
  
  const isCorrectOptionSelected = (
    selectedValue = bankRelatedPaymentGateway[0],
    optionsToActivate
  ) => {
    return optionsToActivate.includes(selectedValue);
  };

  const branchOfficeOptions = (selectedValue = bankRelatedPaymentGateway[0]) => {
    return selectedValue === 'SERVIPAG' ? servipagBranchOfficeList : bankBranchOfficeList;
  };

  const [conditionBank, setConditionBank] = useState(false);
  const [conditionAccountNumber, setConditionAccountNumber] = useState(false);  

  useEffect(() => {
    dispatch(generateAndUploadBankFileExecuted());
  }, []);

  const rowFormation = [
    [
      {
        key: 'paymentGateway',
        name: 'Vía de pago',
        type: 'select',
        options: paymentGatewayList,
        handleInputChange: handlePaymentGatewayAndBankChange,
        selectedValue: paymentGateway,
        condition: true,
        isRequired: true,
        errorMessage: 'Debe seleccionar una opción',
        dispatch,
        action: setPaymentGateway,
        actionError: setModifiedFieldErrors,
        modifiedField: paymentGateway,
        modifiedFieldErrors
      },
      {
        key: 'bank',
        name: 'Banco',
        type: 'select',
        options: bankList,
        handleInputChange: handlePaymentGatewayAndBankChange,
        selectedValue: bank,
        isRequired: true,
        errorMessage: 'Debe seleccionar una opción',
        condition: conditionBank,
        dispatch,
        action: setBank,
        actionError: setModifiedFieldErrors,
        modifiedField: bank,
        modifiedFieldErrors
      }
    ],
    [
      {
        key: 'branchOffice',
        name: 'Sucursal',
        type: 'select',
        options: branchOfficeOptions(paymentGateway),
        handleInputChange: genericHandle,
        selectedValue: branchOffice,
        isRequired: true,
        condition: isCorrectOptionSelected(paymentGateway, branchOfficeRelatedFields),
        dispatch,
        action: setBranchOffice,
        actionError: setModifiedFieldErrors,
        modifiedField: branchOffice,
        modifiedFieldErrors
      },
      { key: 'nextPaymentDate', name: 'Fecha próximo pago' }
    ],
    [
      {
        key: 'accountNumber',
        name: 'Número de cuenta',
        type: 'text',
        toWrite: accountNumber,
        handleInputChange: handleAccountChange,
        condition: conditionAccountNumber,
        validation: NUMERIC_ONLY,
        formatter: numericOnlySanitizer,
        isRequired: conditionAccountNumber,
        errorMessage: 'El valor es inválido',
        dispatch,
        action: setAccountNumber,
        modifiedField: accountNumber,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 30
      },
      {
        key: 'bankRejected',
        name: 'Rechazado por el banco',
        type: 'select',
        options: booleanOptions,
        handleInputChange: handledBankRejectedChange,
        selectedValue: bankRejected,
        isRequired: true,
        errorMessage: 'Debe seleccionar una opción',
        condition: dependencyExecuted,
        modifiedField: bankRejected,
        dispatch,
        action: setBankRejected
      }
    ],
    [
      {
        key: 'paycheckRefunded',
        name: 'Vale vista reintegrado',
        type: 'select',
        options: booleanOptions,
        handleInputChange: handledPaycheckRefundedChange,
        selectedValue: paycheckRefunded,
        isRequired: true,
        condition: dependencyExecuted,
        errorMessage: 'Debe seleccionar una opción',
        dispatch,
        action: setPaycheckRefunded
      }
    ]
  ];

  useEffect(() => {
    notEqualToPaymentRetention(paymentGatewayOld, paymentGateway, dispatch);
    equalToPaymentOldDetained(paymentGatewayOld, paymentGateway, dispatch);
    paymentWithheldToAnotherOption(paymentGatewayOld, paymentGateway, dispatch);
    withheldPaymentWithValueYesAndNotFile(paymentGateway, institutionalPatient, validateFiles.neurological.existence, dispatch);
    withheldPaymentWithValueNot(paymentGateway, institutionalPatient, dispatch);
    equalToPaymentDetained(paymentGateway, values.institutionalPatient, dispatch);

    if(bcoChile.test(paymentGateway) || ((/vista/i).test(paymentGateway)))
      {
        dispatch(setBank(bankList[1]));
        dispatch(setBranchOffice(bankBranchOfficeList[0]));
        setConditionAccountNumber(((/vista/i).test(paymentGateway)) ? false : true);
        setConditionBank(true);
        return;
      } 
 
    if (paymentGateway && paymentGateway !== paymentGatewayList[0]) {
      setConditionBank(isCorrectOptionSelected(paymentGateway, bankOnlyRelatedOptions));
      setConditionAccountNumber(isCorrectOptionSelected(paymentGateway, bankRelatedPaymentGateway));               
    } else {
      setConditionAccountNumber(false);
      setConditionBank(false);
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          accountNumber: true,
          bank: true,
          paymentGateway: true
        })
      );
    }

    if (paymentGateway && paymentGateway === values.paymentGateway) {
      dispatch(setBank(values.bank));
      dispatch(setAccountNumber(values.accountNumber));
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          bank: false,
          accountNumber: false
        })
      );
    } else {
      dispatch(setBank(bankList[0]));
      dispatch(setAccountNumber(''));
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          bank: true,
          accountNumber: true
        })
      );
    }

    if (editable && voucherBankRelatedOptions.includes(paymentGateway)) {
      dispatch(setBranchOffice(bankBranchOfficeList[0]));
      return;
    }
    if (editable && voucherServipagRelatedOptions.includes(paymentGateway)){
      dispatch(setBranchOffice(servipagBranchOfficeList[0]));           
    }
  }, [paymentGateway]);

  useEffect(() => {
     if(paymentGatewayOld === undefined){
        dispatch(setPaymentGatewayOld(paymentGateway));     
     }
  }, [paymentGatewayOld]);


  useEffect(() => {
    if (conditionAccountNumber && !accountNumber)
      dispatch(setModifiedFieldErrors({ ...modifiedFieldErrors, accountNumber: true }));
  }, [conditionAccountNumber]);

  useEffect(() => {
    if (paymentGateway === paymentGatewayList[0]) {
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          paymentGateway: true,
          accountNumber: true,
          bank: true
        })
      );
      return;
    }
    if (conditionAccountNumber && !accountNumber) {
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          accountNumber: true
        })
      );
      return;
    }
    if (conditionBank && bank === bankList[0]) {
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          bank: true
        })
      );
    }    
  }, [editable]);

  return (
    <>
      <Card className={classes.cardContainer}>
        <CardHeader title="Pago liquidación" className={classes.cardHeader} />
        <Divider />
        <CardContent className={classes.content}>
          {createTable({
            data: values,
            format: rowFormation,
            editable: editable && onlineStatus,
            panelName: 'settlementPayment'
          })}
        </CardContent>
      </Card>
    </>
  );
};

SettlementPayment.propTypes = {
  values: PropTypes.shape({}).isRequired
};

export default SettlementPayment;
