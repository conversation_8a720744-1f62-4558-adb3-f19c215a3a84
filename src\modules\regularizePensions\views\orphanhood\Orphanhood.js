import React, { useEffect, Fragment } from 'react';
import useOnlineStatus from '@rehooks/online-status';
import ImportButton from 'components/ImportButton';
import { Grid, Fade, Button, Tooltip } from '@material-ui/core';
import MuiAlert from '@material-ui/lab/Alert';
import useRouter from 'utils/useRouter';
import { useSelector, useDispatch } from 'react-redux';

import { useProgress } from 'components';
import useStyles from '../styles';
import {
  cleanOrphanhoodFileError,
  cleanOrphanhoodResumeErrors,  
  setWasOrphanhoodCronExecuted,
  setWasOrphanhoodAlreadyExecuted  
} from '../../actions'

import {
  wasOrphanhoodInactivatedThisMonth,
  checkOrphanhoodAvailabilityProcess,
  inactivateOrphanhood,
  checkCronExecution
} from '../../services/regularized.service'
import { checkWritePermission } from 'utils/checkUserPermission';
import useDataUpload from '../../hooks/useDataUploadOrphanhood';

const cronDependency = 'REACTIVATE_TRANSIENT_PRE_WORKER';
const cronMark = 'INACTIVATE_OR_REACTIVATE_ORPHANHOOD_PROCESS';
const Alert = props => <MuiAlert elevation={6} variant="filled" {...props} />;

const renderAlert = (inDayRange, availableDay, display) => {
    if (inDayRange === undefined) return null;
    const allowMesagge = `Hasta el día hábil número ${availableDay} del mes actual, puede realizar la inactivación y reactivación de pensiones por orfandad`;
    const denyMessage = `No se puede realizar la inactivación y reactivación, han pasado los primeros ${availableDay} días hábiles del mes permitidos para orfandad`;
  
    const variant = inDayRange ? 'filled' : 'outlined';
    const severity = inDayRange ? 'info' : 'error';
    const message = inDayRange ? allowMesagge : denyMessage;
  
    return (
      display && (
        <div>
          <Fade in timeout={{ enter: 1000 }}>
            <Alert severity={severity} variant={variant}>
              {message}
            </Alert>
          </Fade>
        </div>
      )
    );
  };
const Orphanhood = ({ onSuccessImport, onErrorImport, role }) => {
  const fileUploadInput = React.createRef();
  const router = useRouter();
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();
  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const OrphanhoodfileErrors = useSelector(store => store.regularizedPensions.fileErrors);
  const hasWritePermission = checkWritePermission(role);
  const fileErrorsOrphanhood = useSelector(store => store.regularizedPensions.fileErrorsOrphanhood);
  const isOrphanhoodEnable = useSelector(store => store.regularizedPensions.isOrphanhoodEnable);
  const isEnableOrphanhoodImport = useSelector(store => store.regularizedPensions.isEnableOrphanhoodImport);
  const wasOrphanhoodCronExecuted = useSelector(store => store.regularizedPensions.wasOrphanhoodCronExecuted);
  const isOrphanhoodAvailable = useSelector(store => store.regularizedPensions.isOrphanhoodAvailable);
  const daysToExecuteOrphanhood = useSelector(store => store.regularizedPensions.daysToExecuteOrphanhood);
  const wasOrphanhoodAlreadyExecuted = useSelector(store => store.regularizedPensions.wasOrphanhoodAlreadyExecuted);
  const OrphanhoodApiCall = useSelector(store => store.regularizedPensions.OrphanhoodApiCall);

  const handleUploadButton = () => {
    fileUploadInput.current.value = '';
    fileUploadInput.current.click();
  };

  const verifyOrphanhoodConditions = () => 
     wasOrphanhoodAlreadyExecuted ||
    !wasOrphanhoodCronExecuted ||
    !isOrphanhoodAvailable ||
    isOrphanhoodEnable ||
    fileErrorsOrphanhood.length > 0 ||
    !onlineStatus;
  

  const getInactReactTooltipText = () => {
    const allowMesagge = 'Inactivar y reactivar pensionados';
    const alreadyDoneMesage =
      'La Inactivación/Reactivación de pensiones del mes actual ya fue realizada';
    const wasNotPerformed =
      'No se realizó la Inactivación/Reactivación de pensiones del mes actual';

    let text = allowMesagge;
    if (wasOrphanhoodAlreadyExecuted) text = alreadyDoneMesage;
    if (!wasOrphanhoodAlreadyExecuted && !isOrphanhoodAvailable) text = wasNotPerformed;
    return text;
  };

  const handleInactivateOrphanPension = () =>
    dispatch(inactivateOrphanhood(onSuccessImport, onErrorImport, progress));

  const handleFileUpload = useDataUpload(
    dispatch,
    router,
    progress,
    onSuccessImport,
    onErrorImport
  );

  useEffect(() => {
    dispatch(cleanOrphanhoodFileError());
    dispatch(cleanOrphanhoodResumeErrors());
    dispatch(wasOrphanhoodInactivatedThisMonth());
    const checkCronExec = checkCronExecution(dispatch);
    checkCronExec(cronDependency, setWasOrphanhoodCronExecuted);
    checkCronExec(cronMark, setWasOrphanhoodAlreadyExecuted);
    checkOrphanhoodAvailabilityProcess(dispatch);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Fragment>
      {renderAlert(isOrphanhoodAvailable, daysToExecuteOrphanhood, OrphanhoodApiCall)}
      <Grid
        className={classes.formControl}
        container
        direction="row"
        justify="space-around"
        alignItems="center"
      >
        <Grid item>
          <ImportButton
            classes={{ formControl: classes.formControl }}
            errorMessage={OrphanhoodfileErrors}
            onClick={handleUploadButton}
            disabled={
              !hasWritePermission ||
              !isOrphanhoodAvailable ||
              //wasOrphanhoodAlreadyExecuted ||
              isEnableOrphanhoodImport ||
              !onlineStatus
            }
          />
        </Grid>
        <Grid item>
          <Tooltip title={getInactReactTooltipText()} aria-label="inactReactTooltip">
            <span>
              <Button
                variant="contained"
                disabled={!hasWritePermission || verifyOrphanhoodConditions()}
                onClick={handleInactivateOrphanPension}
              >
                Inactivar/Reactivar
              </Button>
            </span>
          </Tooltip>
        </Grid>
      </Grid>

      <input
        ref={fileUploadInput}
        type="file"
        style={{ display: 'none' }}
        id="fileUpload"
        disabled={!onlineStatus}
        onChange={v => v.target.files.length && handleFileUpload(v.target.files[0])}
        accept=".csv"
      />
    </Fragment>
  );


};

export default Orphanhood;