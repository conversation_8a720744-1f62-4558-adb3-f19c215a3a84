/* eslint-disable no-magic-numbers */
import { makeStyles } from '@material-ui/styles';
import { colors } from '@material-ui/core';

const styles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(3)
  },
  tabs: {
    marginTop: theme.spacing(3)
  },
  headerButtons: {
    margin: 12
  },
  formControl: {
    margin: 30,
    paddingTop: 15
  },
  divider: {
    backgroundColor: colors.grey['300']
  },
  content: {
    marginTop: theme.spacing(3)
  },
  footerButton: {
    display: 'flex',
    justifyContent: 'center',
    margin: '1rem'
  },
  headerImport: {
    background: '#006531',
    '& div': {
      '& span': {
        color: 'white'
      }
    }
  },
  principalErrors: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: '1rem',
    marginBottom: '1rem'
  },
  scientificWarningContainer: {
    margin: '-20px 0 30px 0',
    width: '630px'
  },
  scientificWarningMessage: {
    margin: '10px 0 10px 15px',
    fontWeight: 'bold'
  },
  scientificBulletPoints: {
    margin: '5px 0 5px 30px'
  },
  acceptButton: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.primary.light,
      color: theme.palette.primary.contrastText
    }
  },
  cancelButton: {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.error.light,
      color: theme.palette.error.contrastText
    }
  }
}));

export default styles;
