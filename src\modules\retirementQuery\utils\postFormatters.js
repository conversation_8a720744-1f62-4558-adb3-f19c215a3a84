/* eslint-disable import/prefer-default-export */
import moment from 'moment';

import { especialCaseToNotInactivateThisMonth } from './validityTypeMapper';
import { letterDiacriticsPunctuationMarkSanitizer } from './formatters';
import { nationalities } from '../../../resources/nationalities.json';

import {  
  electronicCheckBancoDeChile,
  bankOnlyRelatedOptions,
  voucherBankRelatedOptions,
  voucherServipagRelatedOptions
} from './paymentGatewayList';

const NON_VALID_PENSION = /no vigente/i;

const dateFormatter = date => {
  if (!date) return undefined;
  return moment(date, 'DD-MM-YYYY').toString();
};

const mapCountryToCode = (listOfNations, country) =>
  listOfNations.find(
    ({ name }) =>
      name.toLowerCase() === letterDiacriticsPunctuationMarkSanitizer(country).toLocaleLowerCase()
  );

const dateFields = ['accidentDate', 'endDateOfValidity', 'endDateOfTheoricalValidity'];

const isThisMonth = (date, currentDate) => {
  if (!date || !currentDate) return false;
  return moment(date).isBetween(
    moment(currentDate).startOf('month'),
    moment(currentDate).endOf('month')
  );
};

const isAfterThisMonth = (date, currentDate) => {
  if (!date || !currentDate) return false;
  return moment(date).isAfter(moment(currentDate).endOf('month'));
};

const isBeforeThisMonth = (date, currentDate) => {
  if (!date || !currentDate) return false;
  return moment(date).isBefore(moment(currentDate).startOf('month'));
};

const doesGoFromNotValidToValid = (previousValue, currentValue) =>
  NON_VALID_PENSION.test(previousValue) &&
  !NON_VALID_PENSION.test(currentValue) &&
  previousValue !== currentValue;
const doesRemainAValidPension = (previousValue, currentValue) =>
  !NON_VALID_PENSION.test(previousValue) &&
  !NON_VALID_PENSION.test(currentValue) &&
  previousValue !== currentValue;
const doesGoFromValidToNotValid = (previousValue, currentValue) =>
  !NON_VALID_PENSION.test(previousValue) &&
  NON_VALID_PENSION.test(currentValue) &&
  previousValue !== currentValue;

const adjustInactivationDateWhenNotValidPension = ({ previousData, currentData, currentDate }) => {
  const { validityType: previousValidityType } = previousData;
  const {
    inactivationDate,
    inactivationReason,
    evaluationDate,
    inactivateManually,
    endDateOfValidity,
    endDateOfTheoricalValidity,
    validityType,
    ...otherFields
  } = currentData;

  if (
    doesGoFromValidToNotValid(previousValidityType, validityType) &&
    endDateOfValidity &&
    isBeforeThisMonth(endDateOfValidity, currentDate)
  ) {
    return {
      previousData,
      currentData: {
        ...otherFields,
        inactivateManually: true,
        validityType,
        inactivationDate: new Date(),
        inactivationReason,
        endDateOfValidity,
        endDateOfTheoricalValidity
      },
      currentDate
    };
  }

  if (
    doesGoFromValidToNotValid(previousValidityType, validityType) &&
    endDateOfValidity &&
    isThisMonth(endDateOfValidity, currentDate) &&
    especialCaseToNotInactivateThisMonth.includes(inactivationReason)
  ) {
    return {
      previousData,
      currentData: {
        ...otherFields,
        inactivationReason,
        evaluationDate: currentDate,
        validityType: previousValidityType,
        inactivateManually: true,
        endDateOfValidity,
        endDateOfTheoricalValidity
      },
      currentDate
    };
  }

  if (
    doesGoFromValidToNotValid(previousValidityType, validityType) &&
    endDateOfValidity &&
    isThisMonth(endDateOfValidity, currentDate) &&
    !especialCaseToNotInactivateThisMonth.includes(inactivationReason)
  ) {
    return {
      previousData,
      currentData: {
        ...otherFields,
        inactivationReason,
        inactivationDate: currentDate,
        validityType,
        inactivateManually: true,
        endDateOfValidity,
        endDateOfTheoricalValidity
      },
      currentDate
    };
  }

  if (
    doesGoFromValidToNotValid(previousValidityType, validityType) &&
    endDateOfValidity &&
    isAfterThisMonth(endDateOfValidity, currentDate)
  ) {
    return {
      previousData,
      currentData: {
        ...otherFields,
        evaluationDate: currentDate,
        inactivationReason,
        validityType: previousValidityType,
        endDateOfValidity,
        endDateOfTheoricalValidity,
        inactivateManually: true
      },
      currentDate
    };
  }

  if (doesRemainAValidPension(previousValidityType, validityType)) {
    return {
      previousData,
      currentData: { ...otherFields, validityType, endDateOfValidity, endDateOfTheoricalValidity },
      currentDate
    };
  }

  if (doesGoFromNotValidToValid(previousValidityType, validityType)) {
    return {
      previousData,
      currentData: {
        ...otherFields,
        validityType,
        manuallyReactivated: true,
        endDateOfTheoricalValidity,
        endDateOfValidity: endDateOfTheoricalValidity,
        reactivationDate: currentDate
      },
      currentDate
    };
  }
  return {
    previousData,
    currentData: {
      ...currentData,
      inactivationDate,
      validityType,
      endDateOfValidity,
      endDateOfTheoricalValidity
    },
    currentDate
  };
};

const filterActualChanges = ({ previousData = {}, currentData = {} }) => {
  const { beneficiaryRut, causantRut, pensionCodeId } = previousData;
  const currentKeys = Object.keys(currentData);
  return currentKeys.reduce(
    (diffObject, key) => {
      if (`${previousData[key]}` === `${currentData[key]}`) return { ...diffObject };
      return { ...diffObject, [key]: currentData[key] };
    },
    { beneficiaryRut, causantRut, pensionCodeId }
  );  
};

const sanitizeDatesAndTrim = (data = {}) => {
  const fields = Object.keys(data);
  return fields.reduce((obj, key) => {
    if (dateFields.includes(key)) return { ...obj, [key]: dateFormatter(data[key]) };
    if (!data[key]) return { ...obj, [key]: data[key] };
    return { ...obj, [key]: `${data[key]}`.trim() };
  }, data);  
};

const bleachUnnecesaryPaymentGatewayFields = (data = {}) => {  
  let bleachedObj;  
  const { paymentGateway, bank, branchOffice, accountNumber, ...otherFields } = data;
  if (voucherBankRelatedOptions.includes(paymentGateway)) {    
    bleachedObj = { ...otherFields, paymentGateway, branchOffice, bank, accountNumber: '' };
    return bleachedObj;
  }
  if (voucherServipagRelatedOptions.includes(paymentGateway)) {    
    bleachedObj = { ...otherFields, paymentGateway, branchOffice, bank: '', accountNumber: '' };
    return bleachedObj;
  }
  if (bankOnlyRelatedOptions.includes(paymentGateway)) {    
    bleachedObj = { ...otherFields, paymentGateway, bank, accountNumber, branchOffice: '' };
    return bleachedObj;
  }
  if (electronicCheckBancoDeChile.includes(paymentGateway)) {    
    bleachedObj = { ...otherFields, paymentGateway, accountNumber, branchOffice: '', bank };
    return bleachedObj;
  }
  return data;
};

const transformCountryToCode = data => {
  if (!data?.country) return data;
  const nationality = mapCountryToCode(nationalities, data.country);
  return { ...data, country: nationality?.key };
};

const transformGenderToLetter = data => {
  if (!data?.gender) return data;
  const genderLetter = data.gender === 'Femenino' ? 'F' : 'M';
  return { ...data, gender: genderLetter };
};

const pipe = (...args) => initialData => [...args].reduce((data, func) => func(data), initialData);

const transformData = pipe(
  adjustInactivationDateWhenNotValidPension,
  filterActualChanges,
  sanitizeDatesAndTrim,
  bleachUnnecesaryPaymentGatewayFields,
  transformCountryToCode,
  transformGenderToLetter
);

export { transformData };
