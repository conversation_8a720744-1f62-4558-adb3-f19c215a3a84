/* eslint-disable import/no-unresolved */
import { getEncoding } from '../../../../../utils/encodingFile';
const INVALID_FILE_EXTENSION = 'Formato de archivo incorrecto';
const INVALID_FILE_NAME = 'Nombre del archivo incorrecto, debe ser';
const FILE_SIZE_IS_TOO_BIG = 'Excede el tamaño permitido';
const VALID_EXTENSIONS = ['.txt'];

const MAX_FILE_SIZE = 10485760;
const NOT_VALID_TXT_CHARS = /[^\s\n+\-*/=@#&%$!¡¿?ºª.:;,_|><´`¨""{})'^[\]~áéíóúàèìòùãẽĩõñũỹg̃äöüëïâêîôûçğşa-z0-9]/im;

const readFile = async (file, createJSON) => {
  const encoding = await getEncoding(file);
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const { result } = reader;
      if (NOT_VALID_TXT_CHARS.test(result)) {
        reject(new Error('Error al leer el archivo.'));
      }
      const splittedResult = result.split('\n');

      const rows = splittedResult.filter(row => row && row.length);
      if (!rows || !rows.length) {
        reject(new Error('Archivo vacio.'));
      }

      let indice = 0;
      resolve(rows.map(row => {
        indice = indice + 1
        return createJSON(row, indice)
      }));
    };
    reader.onerror = () => {
      return reject(new Error('Error al leer el archivo.'));
    };

    reader.readAsText(file, encoding);
  });
};

const validateFile = ({ name, size }, acceptedFilenameRegex, acceptedFilename, fn = () => { /*any*/ }) => {
  let isValid = true;
  // eslint-disable-next-line prefer-const
  let [fileName, fileExtension] = name.split('.');
  fileExtension = fileExtension.toLowerCase();

  if (VALID_EXTENSIONS.indexOf(`.${fileExtension}`) === -1) {
    fn(INVALID_FILE_EXTENSION);
    isValid = false;
  }

  if (!acceptedFilenameRegex.test(fileName)) {
    fn(`${INVALID_FILE_NAME} ${acceptedFilename}`);
    isValid = false;
  }

  if (size > MAX_FILE_SIZE) {
    fn(FILE_SIZE_IS_TOO_BIG);
    isValid = false;
  }

  return isValid;
};

export { readFile, validateFile, VALID_EXTENSIONS };
