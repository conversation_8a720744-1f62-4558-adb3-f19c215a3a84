import moment from 'moment';
import { getCurrentCapitalReport } from '../../../../../services/currentCapitalService';

const ON_ERROR_TEXT = 'Hubo un error con la descarga del reporte';
const downloadFiles = async ({ _dispatch, progress, onErrorSnackbar, currentDate, reportType }) => {
  let success;

  const capitalize = str => str.charAt(0).toUpperCase() + str.slice(1);
  const month = moment(currentDate)
    .locale('es')
    .format('MMMM');
  const date = moment().format('YYYYMM');

  const previousMonth = moment(currentDate)
    .subtract(1, 'month')
    .locale('es')
    .format('MMMM');
  const reportMapper = {
    report1: `Vigentes ${date} Reporte ${capitalize(month)}`,
    report2: `Vigentes ${date} Reporte ${capitalize(previousMonth)}`,
    report3: `Entradas vigentes ${date} Reporte ${capitalize(month)}`,
    report4: `Salidas vigentes ${date} Reporte ${capitalize(month)}`,
    report5: `Contabilización de Capitales Vigentes ${date} Reporte ${capitalize(month)}`
  };
  const reportFileName = () => reportMapper[reportType];
  progress.show();

  await getCurrentCapitalReport(reportType).then(({ isError, data }) => {
    success = !isError;
    if (success) {
      const url = window.URL.createObjectURL(data);
      const a = document.createElement('a');
      a.setAttribute('download', `${reportFileName()}.xlsx`);
      a.href = url;
      a.click();
    }
  });
  if (!success) {
    onErrorSnackbar(`${ON_ERROR_TEXT}`);
    progress.hide();
    return false;
  }

  progress.hide();
  return true;
};

const UseDataDownload = ({ dispatch, progress, onErrorSnackbar, reportType }) => {
  return () => downloadFiles({ dispatch, progress, onErrorSnackbar, reportType });
};

export default UseDataDownload;
