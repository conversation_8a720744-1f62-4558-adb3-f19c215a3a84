export const SET_CURRENT_MONTH_YEAR_QUADRATURE = 'SET_CURRENT_MONTH_YEAR_QUADRATURE';
export const setCurrentMonthYear = date => {
  return {
    type: SET_CURRENT_MONTH_YEAR_QUADRATURE,
    data: date
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_CURRENT_YEAR_QUADRATURE = 'SET_CURRENT_YEAR_QUADRATURE';
export const setCurrentYear = date => {
  return {
    type: SET_CURRENT_YEAR_QUADRATURE,
    data: date
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_FACTORS_FILENAME_ERROR = 'SET_FACTORS_FILENAME_ERROR';
export const setFactorsFilenameError = error => {
  return {
    type: SET_FACTORS_FILENAME_ERROR,
    data: error
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_CONCURRENCIES_FILENAME_ERROR = 'SET_CONCURRENCIES_FILENAME_ERROR';
export const setConcurrenciesFilenameError = error => {
  return {
    type: SET_CONCURRENCIES_FILENAME_ERROR,
    data: error
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_FACTORS_FILE_DATA = 'SET_FACTORS_FILE_DATA';
export const setFactorsFileData = data => {
  return {
    type: SET_FACTORS_FILE_DATA,
    data
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_CONCURRENCIES_FILE_DATA = 'SET_CONCURRENCIES_FILE_DATA';
export const setConcurrenciesFileData = data => {
  return {
    type: SET_CONCURRENCIES_FILE_DATA,
    data
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_HAS_FACTORS_FILE = 'SET_HAS_FACTORS_FILE';
export const setHasFactorsFile = data => {
  return {
    type: SET_HAS_FACTORS_FILE,
    data
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_HAS_CONCURRENCIES_FILE = 'SET_HAS_CONCURRENCIES_FILE';
export const setHasConcurrenciesFile = data => {
  return {
    type: SET_HAS_CONCURRENCIES_FILE,
    data
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_FILE_ERROR = 'SET_FILE_ERROR';
export const setFileError = data => {
  return {
    type: SET_FILE_ERROR,
    data
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_FILE_TYPE = 'SET_FILE_TYPE';
export const setFileType = data => {
  return {
    type: SET_FILE_TYPE,
    data
  };
};
// ------------------------------------------------------------------------------------------------
export const SET_IS_DOWNLOADING_QUADRATURE = 'SET_IS_DOWNLOADING_QUADRATURE';
export const setIsDownloading = bool => {
  return {
    type: SET_IS_DOWNLOADING_QUADRATURE,
    data: bool
  };
};
