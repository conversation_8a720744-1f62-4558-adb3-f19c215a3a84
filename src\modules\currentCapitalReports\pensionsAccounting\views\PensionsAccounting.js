/* eslint-disable import/no-unresolved */
/* eslint-disable import/prefer-default-export */
/* eslint-disable react/prop-types */

import React, { useEffect, useState } from 'react';
import { Button, Grid, Typography, Tooltip } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import { HeaderContent, useProgress } from 'components';
import { useSelector, useDispatch } from 'react-redux';
import useStyles from './styles';
import useDataDownload from '../hooks/getPensionAccountingReport';
import { setIsDownloading } from '../actions';
import { existsData } from '../services/pensionsAccountingServices';

const TITLE = 'Reporte de Conciliación';
const SUBTITLE = 'Configuraciones / Reportería';
const NO_DATA_MESSAGE = 'No hay datos en la tabla ExpenseAccountingReport';

const PensionsAccounting = ({ onErrorSnackbar }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();

  const isDownloading = useSelector(store => store.pensionAccounting.isDownloading);

  const [existsTable, setExistsTable] = useState(false);

  useEffect(() => {
    dispatch(setIsDownloading(false));
    existsData(setExistsTable);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const handleRequestPensions = useDataDownload({ dispatch, progress, onErrorSnackbar });

  return (
    <>
      <Grid container className={classes.root}>
        <Grid item xs={12}>
          <Typography className={classes.subtitle} color="textSecondary">
            {SUBTITLE}
          </Typography>
        </Grid>
        <Grid item xs={12}>
          <HeaderContent title={TITLE} />
        </Grid>
        <Grid container justify="flex-end">
          <Tooltip title={existsTable ? '' : NO_DATA_MESSAGE}>
            <span>
              <Button
                className={classes.export}
                color="primary"
                disabled={!onlineStatus || isDownloading || !existsTable}
                onClick={handleRequestPensions}
                size="medium"
                variant="contained"
              >
                Exportar
              </Button>
            </span>
          </Tooltip>
        </Grid>
      </Grid>
    </>
  );
};

export { PensionsAccounting };
