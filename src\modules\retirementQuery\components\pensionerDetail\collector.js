/* eslint-disable import/no-unresolved */
/* eslint-disable react/prop-types */
import React, { createRef } from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import useOnlineStatus from '@rehooks/online-status';
import { Card, CardContent, CardHeader, Divider, Fade } from '@material-ui/core';
import MuiAlert from '@material-ui/lab/Alert';

import { useProgress } from 'components';
import ImportButton from '../importButton';
import createTable from '../../utils/createTable';
import usePdfUpload from '../../hooks/usePdfUpload';
import useStyles from './style';
import {
  ALPHANUMERIC_DIACRITICS_PUNCTUATIONMARK,
  LETTERS_DIACRITICS,
  alphaNumericPunctuationMarkSanitizer,
  letterDiacriticsSanitizer,
  checkRutAndDV,
  dynamicFormatting
} from '../../utils/formatters';

import {
  setCollectorRut,
  setCollectorName,
  setCollectorLastName,
  setCollectorMothersLastName,
  setCollectorAddress,
  setCollectorCommune,
  setCollectorCity,
  setModifiedFieldErrors
} from '../../actions';

import CollectorChangeModal from './collectorChangeModal';

const handleRutChange = (
  e,
  { key, dispatch, action, actionError, modifiedFieldErrors, formatter, validation }
) => {
  const { value } = e.target;
  const formattedRut = formatter(value);
  const isCorrect = validation(formattedRut);
  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));

  dispatch(action(formattedRut));
};

const handleWordChange = (
  e,
  {
    key,
    dispatch,
    action,
    actionError,
    modifiedFieldErrors,
    formatter,
    validation,
    isRequired,
    maxLength
  }
) => {
  const { value } = e.target;

  const formattedWord = formatter(value) || '';

  const isValidFormatAndLength =
    formattedWord.length <= maxLength && validation.test(formattedWord);

  const isCorrect = isRequired
    ? isValidFormatAndLength
    : formattedWord.length === 0 || isValidFormatAndLength;
  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));
  dispatch(action(formattedWord));
};

const FILE_MESSAGE =
  'Debe importar el poder notarial correspondiente y llenar los campos solicitados antes de realizar el cambio, "Poder_notarial_para_cambio_de_cobrante.pdf"';

const Alert = props => {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
};

const alertModifiedField = (agreesToChange, isFileUploaded, isEditable) => {
  return (
    agreesToChange &&
    !isFileUploaded &&
    isEditable && (
      <Fade in timeout={{ enter: 2000 }}>
        <Alert severity="info" variant="filled">
          {FILE_MESSAGE}
        </Alert>
      </Fade>
    )
  );
};

const Collector = ({ values, editable, onErrorSnackbar, onSuccessSnackbar }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const fileUploadInput = createRef();
  const onlineStatus = useOnlineStatus();

  const collectorRut = useSelector(store => store.queryPensions.collectorRut);
  const collectorName = useSelector(store => store.queryPensions.collectorName);
  const collectorLastName = useSelector(store => store.queryPensions.collectorLastName);
  const collectorMothersLastName = useSelector(
    store => store.queryPensions.collectorMothersLastName
  );
  const collectorAddress = useSelector(store => store.queryPensions.collectorAddress);
  const collectorCommune = useSelector(store => store.queryPensions.collectorCommune);
  const collectorCity = useSelector(store => store.queryPensions.collectorCity);

  const institutionalPatient = useSelector(store => store.queryPensions.institutionalPatient);
  const afpAffiliation = useSelector(store => store.queryPensions.afpAffiliation);

  const agreesToChangeCollectorRut = useSelector(
    store => store.queryPensions.agreesToChangeCollectorRut
  );
  const successfulFileUpload = useSelector(store => store.queryPensions.successfulFileUpload);
  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);

  const displayImportAndAlert = () => agreesToChangeCollectorRut;
  const activateImportButton = () => true;

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const handleFileUpload = usePdfUpload({
    progress,
    onErrorSnackbar,
    onSuccessSnackbar,
    fileUploadInput,
    modifiedField: { values, institutionalPatient, afpAffiliation, collectorRut },
    dispatch
  });

  const handleUploadButton = () => {
    fileUploadInput.current.value = '';
    fileUploadInput.current.click();
  };

  const conditionToActivateCollectorEdition = () =>
    values.afpAffiliation === afpAffiliation &&
    values.institutionalPatient === institutionalPatient;

  const rowFormation = [
    [
      {
        key: 'collectorRut',
        name: 'RUT cobrante',
        type: 'text',
        toWrite: collectorRut,
        handleInputChange: handleRutChange,
        condition: conditionToActivateCollectorEdition(),
        validation: checkRutAndDV,
        formatter: dynamicFormatting,
        isRequired: true,
        errorMessage: 'RUT inválido',
        dispatch,
        action: setCollectorRut,
        modifiedField: collectorRut,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors
      },
      {
        key: 'collectorAddress',
        name: 'Dirección cobrante',
        type: 'text',
        toWrite: collectorAddress,
        handleInputChange: handleWordChange,
        condition: conditionToActivateCollectorEdition(),
        validation: ALPHANUMERIC_DIACRITICS_PUNCTUATIONMARK,
        formatter: alphaNumericPunctuationMarkSanitizer,
        errorMessage: 'El valor es inválido',
        dispatch,
        action: setCollectorAddress,
        modifiedField: collectorAddress,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 1000
      },
      {
        key: 'collectorCommune',
        name: 'Comuna cobrante',
        type: 'text',
        toWrite: collectorCommune,
        handleInputChange: handleWordChange,
        condition: conditionToActivateCollectorEdition(),
        validation: ALPHANUMERIC_DIACRITICS_PUNCTUATIONMARK,
        formatter: alphaNumericPunctuationMarkSanitizer,
        errorMessage: 'El valor es inválido',
        dispatch,
        action: setCollectorCommune,
        modifiedField: collectorCommune,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 1000
      }
    ],
    [
      {
        key: 'collectorName',
        name: 'Nombre cobrante',
        type: 'text',
        toWrite: collectorName,
        handleInputChange: handleWordChange,
        condition: conditionToActivateCollectorEdition(),
        validation: LETTERS_DIACRITICS,
        formatter: letterDiacriticsSanitizer,
        isRequired: true,
        errorMessage: !collectorName ? 'El valor no se ha ingresado' : 'El valor es inválido',
        dispatch,
        action: setCollectorName,
        modifiedField: collectorName,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 60
      },
      {
        key: 'collectorLastName',
        name: 'Apellido paterno cobrante',
        type: 'text',
        toWrite: collectorLastName,
        handleInputChange: handleWordChange,
        condition: conditionToActivateCollectorEdition(),
        validation: LETTERS_DIACRITICS,
        formatter: letterDiacriticsSanitizer,
        isRequired: true,
        errorMessage: !collectorLastName ? 'El valor no se ha ingresado' : 'El valor es inválido',
        dispatch,
        action: setCollectorLastName,
        modifiedField: collectorLastName,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 60
      },
      {
        key: 'collectorCity',
        name: 'Ciudad cobrante',
        type: 'text',
        toWrite: collectorCity,
        handleInputChange: handleWordChange,
        condition: conditionToActivateCollectorEdition(),
        validation: ALPHANUMERIC_DIACRITICS_PUNCTUATIONMARK,
        formatter: alphaNumericPunctuationMarkSanitizer,
        errorMessage: 'El valor es inválido',
        dispatch,
        action: setCollectorCity,
        modifiedField: collectorCity,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 1000
      }
    ],
    [
      {
        key: 'collectorMothersLastName',
        name: 'Apellido materno cobrante',
        type: 'text',
        toWrite: collectorMothersLastName,
        handleInputChange: handleWordChange,
        condition: conditionToActivateCollectorEdition(),
        validation: LETTERS_DIACRITICS,
        formatter: letterDiacriticsSanitizer,
        errorMessage: 'El valor es inválido',
        dispatch,
        action: setCollectorMothersLastName,
        modifiedField: collectorMothersLastName,
        actionError: setModifiedFieldErrors,
        modifiedFieldErrors,
        maxLength: 60
      }
    ]
  ];

  return (
    <>
      <CollectorChangeModal values={values} />
      {alertModifiedField(agreesToChangeCollectorRut, successfulFileUpload, editable)}

      <input
        ref={fileUploadInput}
        type="file"
        style={{ display: 'none' }}
        id="fileUpload"
        disabled={!activateImportButton()}
        onChange={v => v.target.files.length && handleFileUpload(v.target.files[0])}
        accept=".pdf"
      />
      <Card className={classes.cardContainer}>
        <CardHeader title="Información cobrante" className={classes.cardHeader} />
        <div className={classes.divFileUpload}>
        {displayImportAndAlert() && (
          <ImportButton
            classes={{ formControl: classes.formControl }}
            errorMessage={[]}
            onClick={handleUploadButton}
            disabled={!activateImportButton()}
          />
          )}        
        </div>
        <Divider />
        <CardContent className={classes.content}>
          {createTable({
            data: values,
            format: rowFormation,
            editable: editable && onlineStatus,
            panelName: 'collector'
          })}
        </CardContent>
      </Card>
    </>
  );
};

Collector.propTypes = {
  values: PropTypes.shape({}).isRequired,
  editable: PropTypes.bool.isRequired
};

export default Collector;
