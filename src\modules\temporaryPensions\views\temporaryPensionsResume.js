/* eslint-disable react/forbid-prop-types */
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import useRouter from 'utils/useRouter';

import Resume from './resume';

const TemporaryPensionsResume = props => {
  const router = useRouter();
  const { location, onLink } = props;
  const showLinkResume = new URLSearchParams(location.search).get('link');

  const data = useSelector(store => store.temporaryPensions.data);
  const isError = useSelector(store => store.temporaryPensions.isError);
  const errors = useSelector(store => store.temporaryPensions.errors);
  const warnings = useSelector(store => store.temporaryPensions.warnings);

  useEffect(() => {
    if (data.length === 0 && !isError) {
      router.history.push('/nuevas-pensiones/importar');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, isError]);

  const onCancel = () => {
    router.history.push('/nuevas-pensiones/importar');
  };

  return (
    <Resume
      data={data}
      isError={isError}
      fileDataError={{ errors, warnings }}
      linkProps={
        showLinkResume
          ? {
              title:
                'Al confirmar el enlace de pensiones, no se podrá deshacer ni realizar nuevamente dentro del mes actual',
              onLink,
              onCancel
            }
          : false
      }
    />
  );
};

TemporaryPensionsResume.propTypes = {
  location: PropTypes.object.isRequired,
  onLink: PropTypes.func.isRequired
};
export default TemporaryPensionsResume;
