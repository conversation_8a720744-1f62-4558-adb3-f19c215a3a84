/* eslint-disable no-underscore-dangle */
import PropTypes from 'prop-types';
import React from 'react';
import { datePickerStyles } from '../../../../../modules/retirementQuery/components/picker/accidentPickerStyles';
import Picker from '../../../../../modules/retirementQuery/components/picker/accidentPicker';
const minDate = new Date('01-01-2000');
const maxDate = new Date('01-01-2099');

const DateTextField = ({
  classes,
  name,
  handler,
  displayName,
  value,
  onChange,
  inputprops,
  error,
  helperText,
  options,
  ...props
}) => {
  return (
    <>
      <Picker
        className={classes}
        id="outlined-date-native"
        label={displayName}
        value={value || ''}
        error={error}
        helperText={helperText}
        minDate={minDate}
        maxDate={maxDate}
        onChange={onChange}
        MuiStyle={datePickerStyles}
      />
    </>
  );
};
DateTextField.propTypes = {
  name: PropTypes.string.isRequired,
  handler: PropTypes.func,
  displayName: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  isValidListener: PropTypes.func,
  validators: PropTypes.arrayOf(PropTypes.string),
  errorMessages: PropTypes.arrayOf(PropTypes.string),
  inputprops: PropTypes.objectOf(PropTypes.string).isRequired,
  options: PropTypes.arrayOf(Object)
};
DateTextField.defaultProps = {
  handler: () => {
    /* any */
  },
  isValidListener: () => {
    /* any */
  },
  validators: ['required', 'match'],
  errorMessages: ['El valor es requerido', 'El valor es inválido'],
  options: []
};

export default DateTextField;
