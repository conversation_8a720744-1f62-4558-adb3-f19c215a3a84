// Taken from https://gist.github.com/rotvulpix/69a24cc199a4253d058c
import validator from 'validator';
import numbUtils from '../../../../utils/numberOperations';

const isNumerico = value => {
  return /^\d+$/.test(value);
};

const formatterPercent = value => {
  return new Intl.NumberFormat('de-DE').format(value);
};

const formatChileanAmount = value => {
  return new Intl.NumberFormat('de-DE').format(value);
};

const validFormat = percent => {
  return percent ? percent.replace(/(\d{1})(\d{1,2})/gi, '$1.$2') : '';
};

const isValidDecimal = val => {
  const stringValue = `${val}`;
  if (stringValue.length === 0) return true;
  if (!validator.isDecimal(stringValue, { decimal_digits: '0,2', locale: 'es-ES' })) return false;

  const fixedValue = numbUtils.roundValue(stringValue.replace(',', '.'));
  if (fixedValue < 0.01 || fixedValue > 10) return false;

  return true;
};

const amountMatchRule = value => {
  return isNumerico(value) && value <= 999999 && value >= 0 ? value : '';
};

const defaultFormatter = (value = '') =>
  value
    .replace(/^\s|[^a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûçğş0-9.',´`^¨~-\s]/gi, '')
    .replace(/\s+/g, ' ');

export {
  amountMatchRule,
  formatterPercent,
  validFormat,
  defaultFormatter,
  formatChileanAmount,
  isValidDecimal
};
