/* eslint-disable no-underscore-dangle */
import { createStore, applyMiddleware, compose } from 'redux';
import ReduxThunk from 'redux-thunk';
import axios from 'axios';
import axiosRetry from 'axios-retry';
import reducers from './reducers';
import { axiosRequest } from '../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const headers = {
  'Content-Type': 'application/json; charset=utf-8'
};

axiosRetry(axiosRequest, {
  retries: 3
});

const httpRequest = axios.create({
  headers
});

const composeEnhancers =
  typeof window === 'object' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__
    ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({
        // Specify extension’s options like name, actionsBlacklist, actionsCreators, serialize...
      })
    : compose;

const enhancer = composeEnhancers(
  applyMiddleware(
    ReduxThunk.withExtraArgument({
      api: REACT_APP_API_URL,
      axiosRequest,
      httpRequest
    })
  )
  // other store enhancers if any
);

const store = createStore(reducers, enhancer);

export default store;
