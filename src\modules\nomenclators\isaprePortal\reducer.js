import {
  CLEAN_ISAPRE_PORTAL_RESUME_ERROR,
  CLEAN_ISAPRE_PORTAL_FILE_ERROR,
  SET_ISAPRE_PORTAL_FILE_ERROR,
  SET_ISAPRE_PORTAL_RESUME_ERROR,
  ENABLED_ISAPRE_PORTAL,
  ENABLED_ISAPRE_PORTAL_IMPORT,
  DAYS_TO_EXECUTE_ISAPRE_PORTAL_PROCESS,
  WAS_API_CALLED,
  ISAPRE_PORTAL_PROCESS_AVAILABLE
} from './actions';

const initialState = {
  data: [],
  isError: false,
  fileErrors: [],
  errors: [],
  warnings: [],
  isIsaprePortalEnable: false,
  daysToExecuteIsaprePortalProcess: 0,
  apiCall: false,
  isIsaprePortalAvailable: false
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case SET_ISAPRE_PORTAL_FILE_ERROR:
      return {
        ...state,
        fileErrors: [...state.fileErrors, action.data.fileError]
      };
    case SET_ISAPRE_PORTAL_RESUME_ERROR:
      return {
        ...state,
        isError: action.data.isError,
        errors: action.data.errors,
        warnings: action.data.warnings
      };

    case CLEAN_ISAPRE_PORTAL_RESUME_ERROR:
      return {
        ...state,
        isError: false,
        errors: [],
        warnings: []
      };

    case CLEAN_ISAPRE_PORTAL_FILE_ERROR:
      return {
        ...state,
        fileErrors: []
      };
    case ISAPRE_PORTAL_PROCESS_AVAILABLE:
      return {
        ...state,
        isIsaprePortalAvailable: action.data
      };
    case ENABLED_ISAPRE_PORTAL:
      return {
        ...state,
        isIsaprePortalEnable: action.data.isIsaprePortalEnable
      };
    case ENABLED_ISAPRE_PORTAL_IMPORT:
      return {
        ...state,
        isEnableIsaprePortalImport: action.data.isEnableIsaprePortalImport
      };
    case DAYS_TO_EXECUTE_ISAPRE_PORTAL_PROCESS:
      return {
        ...state,
        daysToExecuteIsaprePortalProcess: action.data
      };
    case WAS_API_CALLED:
      return {
        ...state,
        apiCall: action.data
      };
    default:
      return state;
  }
}
