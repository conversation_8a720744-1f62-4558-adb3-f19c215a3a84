/* eslint-disable import/no-unresolved */
/* eslint-disable react-hooks/rules-of-hooks */
import { setFileError, setStoreJson, setFileUpload, setDisableFileUpload } from '../actions';
import readFile from './readtxtFile';
import validate<PERSON><PERSON><PERSON> from './validateJSON';
import validation from './filenameValidation';

const FILE_SIZE_IS_TOO_BIG = 'Excede el tamaño permitido';
const ERROR_AT_READING = 'Error al leer archivo';
const WRONG_DATA = 'Archivo contiene datos incorrectos';
const MAX_FILE_SIZE = 10485760;

const sizeValidation = file => file.size <= MAX_FILE_SIZE;

const UseDataProcessing = async (filename, file, currentDate, dispatch) => {
  const isValidName = await validation(filename, currentDate, dispatch);
  const isValidSize = sizeValidation(file);
  if (isValidName && isValidSize) {
    const result = await readFile(file).catch(() => {
      dispatch(setFileError(ERROR_AT_READING));
      dispatch(setDisableFileUpload());
      return [];
    });
    await validateJSON(result)
      .then(() => dispatch(setStoreJson(result)))
      .then(() => dispatch(setFileUpload()))
      .catch(() => dispatch(setFileError(WRONG_DATA)));
  }
  if (isValidName && !isValidSize) dispatch(setFileError(FILE_SIZE_IS_TOO_BIG));
};

export default UseDataProcessing;
