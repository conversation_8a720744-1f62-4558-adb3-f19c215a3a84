/* eslint-disable no-underscore-dangle */
import { SKIP_MON_JOB, SET_MON_JOB, EXEC_MON_JOB } from './actions';

const initialState = {
  data: [],
  errors: []
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case SKIP_MON_JOB: 
    case EXEC_MON_JOB: {    
      const data = [...state.data];
      return { ...state, data };
    }        
    case SET_MON_JOB: {
      const data = [...action.data.monitor];
      return { ...state, data };
    }
    default:
      return state;
  }
}
