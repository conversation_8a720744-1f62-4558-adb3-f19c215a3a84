/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
/* eslint-disable no-magic-numbers */
import React, { useContext, useReducer, useCallback, useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import { CircularProgress, Grid } from '@material-ui/core';
import useOnLineStatus from '@rehooks/online-status';
import { ButtonTooltip } from '../../../../../components';
import { AppContext } from '../../../../../provider/app';
import useRouter from '../../../../../utils/useRouter';
import { reducer, initialState } from './utils/state';
import authService from '../../../../../services/auth';
import { AuthenticationService } from '../../../../../services/security';
import jwt_decode from 'jwt-decode';

const useStyles = makeStyles(theme => ({
  root: {},
  fields: {
    margin: theme.spacing(-1),
    display: 'flex',
    flexWrap: 'wrap',
    '& > *': {
      flexGrow: 1,
      margin: theme.spacing(1)
    }
  },
  progress: {
    marginRight: theme.spacing(1),
    top: theme.spacing(0.5),
    position: 'relative',
    color: 'white'
  }
}));

async function validateAADSession(handleSubmit) {
  const aad = await AuthenticationService.login();
  if (aad != null) {
    handleSubmit();
  }
}

const LoginForm = _props => {
  const isOnline = useOnLineStatus();
  const { loggedUser, setLoggedUser } = useContext(AppContext);

  const [isLoading, setLoading] = useState(false);

  const isAppBlocked = () => isLoading || !isOnline;

  const [state, dispatch] = useReducer(reducer, initialState);

  const routes = useRouter();

  const classes = useStyles();

  useEffect(() => {
    if (loggedUser && loggedUser.token) {
      routes.history.push('/');
    } else if (localStorage.getItem(`msal.${process.env.REACT_APP_AAD_CLIENT_ID}.idtoken`)) {
      validateAADSession(handleSubmit);
    }
  }, [loggedUser]);

  const handleSubmit = useCallback(async () => {
    dispatch({ type: 'setValue', data: { name: 'loginError', value: '' } });
    setLoading(true);

    const result = await authService.login();

    setLoading(false);
    if (result?.success) {
      const { token } = result;
      const user = jwt_decode(token);

      const { _id, name, email, role = {} } = user;

      const { roleName, views = [] } = role;
      const userViews = views.map(({ permission, view: viewItem }) => ({
        view: viewItem?.view,
        module: viewItem?.module,
        viewNumber: viewItem?.viewNumber,
        permission
      }));

      const loginUser = { _id, name, email, token, role: roleName, userViews };

      setLoggedUser(loginUser);
    } else if (result?.error) {
      dispatch({ type: 'setValue', data: { name: 'loginError', value: result.error.message } });
    } else {
      dispatch({
        type: 'setValue',
        data: {
          name: 'loginError',
          value: 'Ha ocurrido un error inesperado. Favor, intente nuevamente.'
        }
      });
    }
  }, []);

  return (
    <>
      <Grid container spacing={2} style={{ marginTop: '10px' }}>
        <Grid item xs={12}>
          <ButtonTooltip
            onClick={handleSubmit}
            disabled={isAppBlocked()}
            stylePreset="login"
            errorMessage={state.loginError}
          >
            {isLoading && (
              <div className={classes.progress}>
                <CircularProgress size={20} style={{ color: 'white' }} />
              </div>
            )}
            <span>Ingresar</span>
          </ButtonTooltip>
        </Grid>
      </Grid>
    </>
  );
};

export default LoginForm;
