//------------------------------------------------------------------------------------------------
export const CREATE_NOM_ISAPRE = 'CREATE_NOM_ISAPRE';
export const createNomenclatorIsapre = isapre => {
  return {
    type: CREATE_NOM_ISAPRE,
    data: { isapre }
  };
};
//------------------------------------------------------------------------------------------------
export const DELETE_NOM_ISAPRE = 'DELETE_NOM_ISAPRE';
export const deleteNomenclatorIsapre = isapre => {
  return {
    type: DELETE_NOM_ISAPRE,
    data: { isapre }
  };
};
//------------------------------------------------------------------------------------------------
export const UPDATE_NOM_ISAPRE = 'UPDATE_NOM_ISAPRE';
export const updateNomenclatorIsapre = (prevIsapre, newIsapre) => {
  return {
    type: UPDATE_NOM_ISAPRE,
    data: { prevIsapre, newIsapre }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_NOM_ISAPRE = 'SET_NOM_ISAPRE';
export const loadNomenclatorIsapre = isapres => {
  return {
    type: SET_NOM_ISAPRE,
    data: isapres
  };
};
