### Prestaciones economicas 

Aplicación para administrar el proceso de generación y ejecución de pagos de pensiones. Este proyecto se encarga del Frontend.

## Comenzando 🚀

El repositorio del proyecto se ordena bajo el modelo de branching [GitFlow](https://achsdev.visualstudio.com/Template-Estandar_Proyecto_Devops/_wiki/wikis/Template-Estandar_Proyecto_Devops.wiki/4591/Repositorio-de-c%C3%B3digo-Source-Code-Management-(SCM)?anchor=4)-branching-model)
Información complementaria de GitFlow: [GitFlow Atlassian](https://www.atlassian.com/es/git/tutorials/comparing-workflows/gitflow-workflow)

Mira **Deployment** para conocer como desplegar el proyecto.


### Pre-requisitos 📋

API PEC funcionando 
En caso de actualizar librerías se deberá considerar mantener la siguiente versión "^1.57.2" de la librería "material-table" debido a que si se sube de versión las tablas funcionan de forma anómala

### Instalación 🔧

Instalar librerías del proyecto ejecutando desde la consola el comando "npm ci"
Crear archivo de variables de entorno ".env.development.local" en la raiz del proyecto WEB y copiar el contenido del archivo ".env.development" en el nuevo archivo.
Cambiar los valores de las siguientes variables de entorno:	
    REACT_APP_PORT
    REACT_APP_API_URL


## Ejecutando las pruebas ⚙️

ejecute desde la consola el comando "npm start"
verifique que existe como usuario en la collection users de mongoDB de no existir insertar registro
registro de ejemplo:
{
    "name" : "NUEVO USUARIO",
    "email" : "<EMAIL>",
    "role" : ObjectId("601ac916287abf7c7efa6966"),
    "isActive" : true,
    "enabled" : true,
    "createdAt" : ISODate("2022-02-17T13:42:21.935Z"),
    "updatedAt" : ISODate("2022-07-06T19:16:24.909Z"),
    "__v" : 0
}

Ingrese credenciales en pantalla de inicio de sesión desplegada en el navegador

### Analice las pruebas end-to-end 🔩

_Explica que verifican estas pruebas y por qué_

```
Da un ejemplo
```

### Y las pruebas de estilo de codificación ⌨️

_Explica que verifican estas pruebas y por qué_

```
Da un ejemplo
```

## Despliegue 📦

instrucciones para realizar modificaciones al front
posicionarse sobre branche develop-achs "git checkout develop-achs"
actualizar branche "git pull origin develop-achs"
crear branche para nuevo desarrollo "git checkout -b feature/xxxxx-nuevo-desarrollo"
realizar commit de nuevo desarrollo "git commit -m "texto descriptivo de la modificacion""
realizar push de nuevo desarrollo "git push origin feature/111622-correccion-calculo-dias-transitoria"

crear PR feature/xxxxx-nuevo-desarrollo en develop-achs
desplegar PR en ambientes de QA
cerrar PR feature/xxxxx-nuevo-desarrollo

## Construido con 🛠️

* [ReactJs](https://es.reactjs.org/) - El framework frontend usado


## Contribuyendo 🖇️

Por favor lee el [CONTRIBUTING.md](https://gist.github.com/villanuevand/xxxxxx) para detalles de nuestro código de conducta, y el proceso para enviarnos pull requests.

## Wiki 📖

Puedes encontrar mucho más de cómo utilizar este proyecto en nuestra [Wiki](https://achsdev.visualstudio.com/AG139%20-%20Prestaciones%20Economicas%202.0/_wiki/wikis/AG139---Prestaciones-Economicas-2.0.wiki/352/Informaci%C3%B3n-del-Proyecto-Prestaciones-Econ%C3%B3micas-2.0)

## Versionado 📌

Usamos [SemVer](http://semver.org/) para el versionado. Para todas las versiones disponibles, mira los [tags en este repositorio](https://github.com/tu/proyecto/tags).

## Autores ✒️

_Menciona a todos aquellos que ayudaron a levantar el proyecto desde sus inicios_

* **Andrés Villanueva** - *Trabajo Inicial* - [villanuevand](https://github.com/villanuevand)
* **Fulanito Detal** - *Documentación* - [fulanitodetal](#fulanito-de-tal)

También puedes mirar la lista de todos los [Equipo Proyecto](https://achsdev.visualstudio.com/AG139%20-%20Prestaciones%20Economicas%202.0/_wiki/wikis/AG139---Prestaciones-Economicas-2.0.wiki/5446/1.-Resumen-Ejecutivo) quíenes han participado en este proyecto. 

## Licencia 📄

Este proyecto está bajo la Licencia (Tu Licencia) - mira el archivo [LICENSE.md](LICENSE.md) para detalles

## Expresiones de Gratitud 🎁

* Comenta a otros sobre este proyecto 📢
* Invita una cerveza 🍺 o un café ☕ a alguien del equipo. 
* Da las gracias públicamente 🤓.
* etc.


