/* eslint-disable no-magic-numbers */
import moment from 'moment';

const formatMoney = amount =>
  new Intl.NumberFormat('es-CL', {
    style: 'currency',
    currency: 'CLP',
    maximumFractionDigits: 0
  }).format(amount);
const roundValue = (value, decimalPlaces = 0) => {
  const decimals = decimalPlaces >= 0 ? decimalPlaces : 2;
  return Math.round((+value + Number.EPSILON) * 10 ** decimals) / 10 ** decimals;
};
const sumByField = (field, arr = []) => arr.reduce((sum, curr) => sum + curr[field] || 0, 0);

const spanishFullDate = date =>
  moment(date)
    .locale('es')
    .format('DD [de] MMMM [de] YYYY');
const spanishMonthYear = date =>
  moment(date)
    .locale('es')
    .format('MMMM YYYY');
const DDMMYYYYDate = date => moment(date).format('DD/MM/YYYY');
const MMYYYYDate = date => moment(date).format('MM/YYYY');

const generateTotalFields = data => {
  const totalTaxablePension = roundValue(sumByField('taxablePension', data?.historicalSettlements));
  const totalNetPension = roundValue(sumByField('netPension', data?.historicalSettlements));
  const sumOfTotalDiscounts = roundValue(sumByField('totalDiscounts', data?.historicalSettlements));
  return { ...data, totalTaxablePension, sumOfTotalDiscounts, totalNetPension };
};

const formatDates = data => {
  const currentDate = spanishFullDate(data?.currentDate);
  const lowerDate = MMYYYYDate(data?.lowerDate);
  const upperDate = MMYYYYDate(data?.upperDate);
  const historicalSettlements = data?.historicalSettlements?.map(({ updatedAt, ...others }) => {
    return { ...others, updatedAt: spanishMonthYear(updatedAt) };
  });
  const chargesOfPensioner = data?.chargesOfPensioner?.map(
    ({ dateOfBirth, endDateOfValidity, endDateOfCertificationValidity, ...others }) => {
      return {
        ...others,
        dateOfBirth: DDMMYYYYDate(dateOfBirth),
        endDateOfValidity: DDMMYYYYDate(endDateOfValidity),
        endDateOfCertificationValidity: DDMMYYYYDate(endDateOfCertificationValidity)
      };
    }
  );
  const pensionStartDate = DDMMYYYYDate(data?.pensionStartDate);
  const endDateOfValidity = DDMMYYYYDate(data?.endDateOfValidity);
  return {
    ...data,
    historicalSettlements,
    chargesOfPensioner,
    currentDate,
    lowerDate,
    upperDate,
    pensionStartDate,
    endDateOfValidity
  };
};

const formatMonetaryFields = data => {
  const {
    totalTaxablePension,
    totalNetPension,
    sumOfTotalDiscounts,
    historicalSettlements: settlements
  } = data;
  const historicalSettlements = settlements?.map(
    ({ taxablePension, netPension, totalDiscounts, ...others }) => {
      return {
        ...others,
        taxablePension: formatMoney(roundValue(taxablePension)),
        netPension: formatMoney(roundValue(netPension)),
        totalDiscounts: formatMoney(roundValue(totalDiscounts))
      };
    }
  );

  return {
    ...data,
    historicalSettlements,
    totalTaxablePension: formatMoney(totalTaxablePension),
    totalNetPension: formatMoney(totalNetPension),
    sumOfTotalDiscounts: formatMoney(sumOfTotalDiscounts)
  };
};

const formatTextFields = data => {
  const chargesOfPensioner = data?.chargesOfPensioner?.map(charge => {
    return {
      ...charge,
      chargeFullName: `${charge?.beneficiary?.name} ${charge?.beneficiary?.lastName} ${charge?.beneficiary?.mothersLastName}`
    };
  });
  return { ...data, chargesOfPensioner };
};

const functionOrder = [generateTotalFields, formatDates, formatMonetaryFields, formatTextFields];

const composeFormats = ([...fns]) => data => fns.reduce((y, fn) => fn(y), data);

const formatPDFData = (data = {}) => composeFormats(functionOrder)(data);

export default formatPDFData;
