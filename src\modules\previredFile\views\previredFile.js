/* eslint-disable import/no-unresolved */
/* eslint-disable prefer-template */
/* eslint-disable import/prefer-default-export */
/* eslint-disable react/prop-types */

import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Alert } from '@material-ui/lab';
import { Card, Grid, Typography } from '@material-ui/core';

import useOnlineStatus from '@rehooks/online-status';
import { useProgress } from 'components';
import { DownloadForm, Header } from './components';
import useDataDownload from '../hooks/getPreviredFile';
import { resetPreviredFile, setPreviredFile } from '../actions';
import { getLatestPreviredFile } from '../services/previredFileServices';
import useStyles from './styles';

const HTTP_OK_STATUS = 200;
const TITLE = 'Archivo Previred';
const SUBTITLE = 'Configuraciones / Reportería';
const NO_FILE_MESSAGE = 'No hay archivo';

const alertText =
  'Al momento de la generación del archivo Previred, los Jefe de PEC serán notificados a través de un email';
const titleNewCurrent = 'Último archivo Previred generado';

const PreviredFile = ({ onErrorSnackbar }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();

  const latestFile = useSelector(store => store.previredFile.latestFile);
  const isDownloading = useSelector(store => store.previredFile.isDownloading);

  useEffect(() => {
    getLatestPreviredFile().then(res => {
      const { status, data } = res;
      if (status === HTTP_OK_STATUS) {
        dispatch(setPreviredFile(data.result));
      } else {
        dispatch(resetPreviredFile);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const handleDownloadPreviredFile = useDataDownload({
    dispatch,
    latestFile,
    progress,
    onErrorSnackbar
  });

  return (
    <>
      <Grid>
        <Header subtitle={SUBTITLE} title={TITLE} />
      </Grid>
      <Grid>
        <Grid className={classes.grid}>
          <Alert className={classes.alertBar} severity="error" color="info">
            {alertText}
          </Alert>
        </Grid>
        <Card className={classes.cardStyle}>
          <Grid className={classes.gripTop}>
            <Grid className={classes.grid}>
              <Grid className={classes.grid}>
                <Typography className={classes.title}>{titleNewCurrent}</Typography>
              </Grid>

              <DownloadForm
                label={latestFile ? 'Descargar' : NO_FILE_MESSAGE}
                value={latestFile ? latestFile.virtualPath?.split('/').pop() : ''}
                tooltipTitle="Descargar"
                onClick={handleDownloadPreviredFile}
                variant="contained"
                color="primary"
                disabled={!onlineStatus || isDownloading || !latestFile}
              />
            </Grid>
          </Grid>
        </Card>
      </Grid>
    </>
  );
};

export { PreviredFile };
