import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { TextValidator, ValidatorForm } from 'react-material-ui-form-validator';
import { matchRequired, matchName, matchEmail, matchRole } from '../validator/validField';

const SecureTextField = ({
  name,
  displayName,
  type,
  isValidListener,
  validators,
  errorMessages,
  inputprops,
  ...props
}) => {
  useEffect(() => {
    ValidatorForm.addValidationRule('required', matchRequired);
    ValidatorForm.addValidationRule('matchName', matchName);
    ValidatorForm.addValidationRule('matchEmail', matchEmail);
    ValidatorForm.addValidationRule('matchRole', matchRole);
    // returned function will be called on component unmount
    return () => {
      ValidatorForm.removeValidationRule('required');
      ValidatorForm.removeValidationRule('matchName');
      ValidatorForm.removeValidationRule('matchEmail');
      ValidatorForm.removeValidationRule('matchRole');
    };
  }, []);
  return (
    <>
      <TextValidator
        name={name}
        label={displayName}
        type={type}
        validators={validators}
        errorMessages={errorMessages}
        variant="outlined"
        inputProps={{ ...inputprops }}
        validatorListener={isValidListener}
        {...props}
      />
    </>
  );
};
SecureTextField.propTypes = {
  name: PropTypes.string.isRequired,
  displayName: PropTypes.string.isRequired,
  type: PropTypes.oneOf(['text', 'number', 'email', 'date']),
  isValidListener: PropTypes.func,
  validators: PropTypes.arrayOf(PropTypes.string),
  errorMessages: PropTypes.arrayOf(PropTypes.string),
  inputprops: PropTypes.objectOf(PropTypes.string).isRequired
};
SecureTextField.defaultProps = {
  type: 'text',
  isValidListener: () => { /*any*/ },
  validators: ['required'],
  errorMessages: ['El valor es requerido']
};

export { matchEmail, matchRole };
export default SecureTextField;
