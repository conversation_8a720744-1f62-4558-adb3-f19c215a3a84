/* eslint-disable import/no-unresolved */
import { useState } from 'react';

// eslint-disable-next-line import/prefer-default-export
export const useSnackbar = () => {
  const [text, setText] = useState('');
  const [error, setError] = useState('');
  const [successImportSnackbar, setSuccessImportSnackbar] = useState(false);
  const [errorImportSnackbar, setErrorImportSnackbar] = useState(false);

  return {
    text,
    error,
    successImportSnackbar,
    errorImportSnackbar,
    setSuccessImportSnackbar,
    setErrorImportSnackbar,
    setText,
    setError
  };
};
