/* eslint-disable import/no-unresolved */
/* eslint-disable react-hooks/rules-of-hooks */
import moment from 'moment';

import { readFile, validateFile } from './csv';
import { setFileError, updateFileDataError, cleanFileError, enableReactivate } from '../actions';
import {
  insertTemporaryFamilyAssigments,
  wasInactivatedThisMonth
} from '../services/regularized.service';

const DEFAULT_DATE = '01-01-1900';
function compare(a, b) {
  if (a > b) return 1;
  if (b > a) return -1;
  return 0;
}
const datamodelSchema = [
  { name: 'chargeId', required: true, order: 1 },
  { name: 'causantId', required: true, order: 2 },
  { name: 'dateOfChargeInactivation', type: 'date', order: 3 },
  { name: 'startDateOfCertificationValidity', type: 'date', order: 4 },
  { name: 'endDateOfCertificationValidity', type: 'date', order: 5 },
  { name: 'invalid', order: 6 },
  { name: 'collectorId', required: true, order: 7 },
  { name: 'chargeValidityType', required: true, order: 8 },
  { name: 'typeOfAssocietedPension', required: true, order: 9 },
  { name: 'relationship', order: 10 },
  { name: 'familyAssignment', required: true, order: 11 },
  { name: 'retroactiveFamilyAssignment', required: true, order: 12 }
];
const attrs = datamodelSchema.sort(compare).map(o => o.name);
const dateAttrs = datamodelSchema.filter(o => o.type === 'date').map(o => o.name);
const requiredAttrs = datamodelSchema.filter(o => o.required).map(o => o.name);

const createJSON = values => {
  return attrs.reduce((o, k, i) => {
    let attrValue = values[i] ? `${values[i]}` : null;
    if (dateAttrs.includes(k)) {
      const dateValue = moment(attrValue, 'DD-MM-YYYY').isValid() ? attrValue : DEFAULT_DATE;
      attrValue = moment(dateValue, 'DD-MM-YYYY');
    }
    return { ...o, [k]: attrValue };
  }, {});
};
const isEmpty = str => !str || !str.trim();

const validateRow = (row, index) => {
  return Object.entries(row)
    .filter(([key, values]) => requiredAttrs.includes(key) && isEmpty(values))
    .map(() => {
      return {
        row: index + 1,
        message: `Existen campos obligatorios vacíos`,
        type: 'error'
      }
    });
};

const processData = async ({
  data,
  router,
  dispatch,
  progress,
  onSuccessImport,
  onErrorImport
}) => {
  const errors = data.map((row, i) => validateRow(row, i)).flat();
  dispatch(cleanFileError());
  dispatch(updateFileDataError({ errors }));

  if (errors.length === 0) {
    progress.show();
    const response = await insertTemporaryFamilyAssigments(data);
    if (!response.isError) {
      onSuccessImport();
    } else {
      onErrorImport();
    }
    dispatch(wasInactivatedThisMonth());
    dispatch(enableReactivate(!response.isError));
    progress.hide();
  } else {
    await insertTemporaryFamilyAssigments('');
    dispatch(enableReactivate(false));
    router.history.push('/regularizar-pensiones/inactivar-reactivar/reasignacion-familiar/resumen');
  }
};

const useDataUpload = (dispatch, router, progress, onSuccessImport, onErrorImport) => {
  return async file => {
    dispatch(cleanFileError());
    if (validateFile(file, err => dispatch(setFileError(err)))) {
      const data = await readFile(file, createJSON).catch(() => {
        dispatch(setFileError('Error al leer archivo'));
        dispatch(enableReactivate(false));
        return [];
      });

      if (data.length > 0) {
        await processData({ data, router, dispatch, progress, onSuccessImport, onErrorImport });
      }
    }
  };
};
export default useDataUpload;
