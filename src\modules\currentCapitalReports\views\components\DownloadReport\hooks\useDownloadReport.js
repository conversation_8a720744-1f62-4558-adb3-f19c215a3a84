import { useState } from 'react';
import { useProgress } from '../../../../../../components';
import {
  getInactivationsReactivationsReport,
  getReportsCurrentDate
} from '../../../../services/report.service';

const useDownloadReport = onErrorSnackbar => {
  const [download, setDownload] = useState(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const handleDownloadReport = async () => {
    let success;
    setIsDownloading(true);
    progress.show();
    await getInactivationsReactivationsReport().then(async ({ data, isError }) => {
      success = !isError;
      if (success) {
        const date = await getReportsCurrentDate();
        const url = window.URL.createObjectURL(data);
        const a = document.createElement('a');
        a.setAttribute('download', `Reporte Inactivaciones y Reactivaciones ${date}.xlsx`);
        a.href = url;
        a.click();
      }
    });

    if (!success) {
      onErrorSnackbar('Hubo un error con la descarga del reporte');
      setIsDownloading(false);
      progress.hide();
      return;
    }

    progress.hide();
    setIsDownloading(false);
  };

  const cleanDownload = () => {
    setDownload(null);
  };

  return [download, isDownloading, handleDownloadReport, cleanDownload];
};

export default useDownloadReport;
