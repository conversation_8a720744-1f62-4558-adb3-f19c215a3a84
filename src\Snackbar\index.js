import React from 'react';
import PropTypes from 'prop-types';
import { Snackbar as M<PERSON><PERSON>nackbar, Button } from '@material-ui/core';
import { Alert as M<PERSON><PERSON>lert } from '@material-ui/lab';
import {
  CheckCircleOutlined,
  ErrorOutlined,
  InfoOutlined,
  WarningOutlined
} from '@material-ui/icons';

import { useSnackbar } from './useSnackbar';

import styleList from './styles';

const Snackbar = props => {
  const {
    actions,
    autoHideDuration,
    icon,
    iconMapping,
    message,
    onClose,
    open,
    setOpen,
    stylePreset
  } = props;

  const muiAttrs = {
    base: { color: null, severity: null, variant: 'filled', elevation: 6 },
    error: { color: 'error', severity: 'error', variant: 'filled', elevation: 6 },
    info: { color: 'info', severity: 'info', variant: 'filled', elevation: 6 },
    success: { color: 'success', severity: 'success', variant: 'filled', elevation: 6 },
    warning: { color: 'warning', severity: 'warning', variant: 'filled', elevation: 6 }
  };

  const selectedMuiAttrs = { ...muiAttrs.base, ...muiAttrs[stylePreset] };

  const useStyles = styleList[stylePreset] || styleList.base;
  const classes = useStyles();

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpen(false);
    onClose && onClose();
  };

  const handleActionClose = fn => () => {
    handleClose();
    fn && fn();
  };

  const renderActions = () => {
    if (!actions) {
      return null;
    }
    return actions.map(action => {
      return (
        <Button
          key={`snackbar-${action.name}`}
          classes={classes}
          onClick={handleActionClose(action.onClick)}
        >
          {action.name}
        </Button>
      );
    });
  };

  return (
    <MUISnackbar
      classes={classes}
      message={message}
      autoHideDuration={autoHideDuration}
      onClose={handleClose}
      open={open}
    >
      <MUIAlert
        color={selectedMuiAttrs.color}
        elevation={6}
        icon={icon}
        iconMapping={iconMapping}
        onClose={onClose && handleClose}
        severity={selectedMuiAttrs.severity}
        variant={selectedMuiAttrs.variant}
      >
        {message}
        {renderActions()}
      </MUIAlert>
    </MUISnackbar>
  );
};

Snackbar.propTypes = {
  actions: PropTypes.arrayOf(PropTypes.shape({ name: PropTypes.string, onClick: PropTypes.func })),
  autoHideDuration: PropTypes.number,
  icon: PropTypes.node,
  iconMapping: PropTypes.shape({
    error: PropTypes.node,
    info: PropTypes.node,
    success: PropTypes.node,
    warning: PropTypes.node
  }),
  message: PropTypes.string,
  onClose: PropTypes.func,
  open: PropTypes.bool,
  setOpen: PropTypes.func,
  stylePreset: PropTypes.string
};

Snackbar.defaultProps = {
  actions: null,
  autoHideDuration: 5000,
  icon: null,
  iconMapping: {
    error: <ErrorOutlined />,
    info: <InfoOutlined />,
    success: <CheckCircleOutlined />,
    warning: <WarningOutlined />
  },
  message: 'Success snackbar',
  onClose: null,
  open: false,
  setOpen: () => {
    /* any */
  },
  stylePreset: 'base'
};

export { useSnackbar };

export default Snackbar;
