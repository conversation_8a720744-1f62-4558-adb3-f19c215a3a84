/* eslint-disable react/prop-types */
import React from 'react';
import { PDFDownloadLink } from '@react-pdf/renderer';
import Template from './index';

const DOCUMENT_NAME = 'Certificado de pensión.pdf';

const downloadPDF = ({ blob, setPensionCertificateData, progress, setIsCurrentlyDownloading }) => {
  const PDF = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.setAttribute('download', DOCUMENT_NAME);
  a.href = PDF;
  a.click();

  progress.hide();
  setPensionCertificateData(null);
  setIsCurrentlyDownloading(false);
};

const DownloadPDFPensionCertificate = ({
  pensionCertificateData,
  setPensionCertificateData,
  progress,
  setIsCurrentlyDownloading,
  documentName
}) => {
  return (
    <>
      {pensionCertificateData && (
        <PDFDownloadLink document={<Template data={pensionCertificateData} />}>
          {({ blob, loading }) =>
            loading
              ? ''
              : downloadPDF({
                  blob,
                  documentName,
                  setPensionCertificateData,
                  progress,
                  setIsCurrentlyDownloading
                })
          }
        </PDFDownloadLink>
      )}
    </>
  );
};
export default DownloadPDFPensionCertificate;
