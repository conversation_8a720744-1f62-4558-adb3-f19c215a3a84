/* eslint-disable no-console */

import { axiosRequest } from '../../../../services/axiosRequest';
const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;
//------------------------------------------------------------------------------------------------
export const fetchMinimunPensionsAndBonuses = async () => {
  const { data } = await axiosRequest
    .get(`${api}/nomenclators/minimun-pensions-and-bonus`)
    .catch(err => {
      console.error(err);
      return { data: {} };
    });

  return data;
};

export const modifyMinimunPensionsAndBonuses = async obj => {
  const { data } = await axiosRequest
    .put(`${api}/nomenclators/minimun-pensions-and-bonus`, { data: obj })
    .catch(err => {
      console.error(err);
      return { data: {} };
    });

  return data;
};
