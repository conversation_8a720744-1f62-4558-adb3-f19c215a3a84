const RUT_LENGTH_ERROR = 'El Rut debe tener entre 8 y 10 caracteres';
const RUT_RANGE_ERROR = 'El Rut debe estar entre 1.000.000 y 50.000.000';
const RUT_FORMAT_ERROR = 'Formato de Rut incorrecto';
const INVALID_RUT = 'El Rut ingresado es inválido';

const RUT_PATTERN = /^(\d{7,8})-?[0-9kK]$/;

const MIN_RUT_LENGTH = 8;
const MAX_RUT_LENGTH = 10;
const MIN_RUT_VALUE = 1000000;
const MAX_RUT_VALUE = 50000000;

const doesRutMatch = rut => RUT_PATTERN.test(rut);

const isValidRutLength = rut => rut.length >= MIN_RUT_LENGTH && rut.length <= MAX_RUT_LENGTH;

const isValidRutRange = rut => {
  const cleanRut = rut && rut.replace(/[^0-9kK]+/gi, '');
  const rutWithoutDV = cleanRut.slice(0, cleanRut.length - 1);
  return rutWithoutDV >= MIN_RUT_VALUE && rutWithoutDV <= MAX_RUT_VALUE;
};

const isValidRut = (rut, rowError) => {
  if (!isValidRutLength(rut)) rowError.push(RUT_LENGTH_ERROR);
  if (!isValidRutRange(rut)) rowError.push(RUT_RANGE_ERROR);
  if (!doesRutMatch(rut)) rowError.push(RUT_FORMAT_ERROR);
  if (!checkDigitValidation(rut)) rowError.push(INVALID_RUT);
};

const checkDigitValidation = rutWithDV => {
  const [rut, currentDV] = splitRutAndDV(rutWithDV);
  const module = 11;
  const multipliers = '234567'.split('').map(x => +x);
  const sumOfElements = rut
    .replace(/[^\d]/g, '')
    .split('')
    .reverse()
    .map((value, index) => +value * multipliers[index % multipliers.length])
    .reduce((accumulator, currentValue) => accumulator + currentValue);
  let actualDV = module - (sumOfElements % module);
  if (actualDV === module - 1) {
    actualDV = 'k';
    return actualDV === currentDV.toLowerCase();
  }
  if (actualDV === module) actualDV = 0;
  return actualDV === +currentDV;
};

const splitRutAndDV = rutWithDV => {
  return rutWithDV.includes('-')
    ? rutWithDV.split('-')
    : [rutWithDV.slice(0, -1), rutWithDV.slice(-1)];
};

export default isValidRut;
