import { combineReducers } from 'redux';
import {
  afp,
  banks,
  servipag,
  isapres,
  motive,
  capitalFactors,
  nationalHolidays,
  caja,
  salary
} from '../modules/nomenclators/reducers';
import pensions from '../modules/pensions/reducer';
import regularizedPensions from '../modules/regularizePensions/reducer';
import isaprePortal from '../modules/nomenclators/isaprePortal/reducer';
import { cajaLosAndesDiscounts, fonasaDiscounts } from '../modules/prepareSettlements/reducers';

import temporaryPensions from '../modules/temporaryPensions/reducer';
import summarizeSettlements from '../modules/currentCapitalReports/summarizeSettlement/reducer';
import progress from './progress';
import queryPensions from '../modules/retirementQuery/reducer';
import users from '../modules/systems/users/reducer';
import pensionAccounting from '../modules/currentCapitalReports/pensionsAccounting/reducer';
import bankFile from '../modules/bankFile/reducer';
import monitorJobs from '../modules/systems/monitorJobs/reducer';
import previredFile from '../modules/previredFile/reducer';

export default combineReducers({
  progress,
  pensions,
  temporaryPensions,
  regularizedPensions,
  isaprePortal,
  afp,
  banks,
  servipag,
  isapres,
  motive,
  capitalFactors,
  nationalHolidays,
  summarizeSettlements,
  fonasaDiscounts,
  cajaLosAndesDiscounts,
  queryPensions,
  users,
  pensionAccounting,
  bankFile,
  monitorJobs,
  caja,
  salary,
  previredFile
});
