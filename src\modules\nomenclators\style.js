import { makeStyles } from '@material-ui/styles';
import { colors } from '@material-ui/core';

const styles = makeStyles(theme => ({
  root: {
    // eslint-disable-next-line no-magic-numbers
    padding: theme.spacing(3)
  },
  tabs: {
    // eslint-disable-next-line no-magic-numbers
    marginTop: theme.spacing(3)
  },
  headerButtons: {
    margin: 12
  },
  formControl: {
    paddingTop: 15
  },
  divider: {
    // eslint-disable-next-line no-magic-numbers
    backgroundColor: colors.grey[300]
  },
  content: {
    // eslint-disable-next-line no-magic-numbers
    marginTop: theme.spacing(3)
  },
  footerButton: {
    display: 'flex',
    justifyContent: 'center',
    margin: '1rem'
  },
  headerImport: {
    background: '#006531',
    '& div': {
      '& span': {
        color: 'white'
      }
    }
  },
  principalErrors: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: '1rem',
    marginBottom: '1rem'
  },
  inputContainer: {
    marginTop: 15,
    backgroundColor: '#FFFFFF',
    border: 'solid 1px #e0e0e0',
    'border-spacing': 0,
    'border-collapse': 'collapse',
    // eslint-disable-next-line no-magic-numbers
    padding: theme.spacing(2)
  },
  gridTextField: {
    paddingLeft: '5px',
    paddingRight: '5px'
  }
}));

export default styles;
