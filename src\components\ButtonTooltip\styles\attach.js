/* eslint-disable no-magic-numbers */
import { makeStyles } from '@material-ui/styles';
import customColors from '../../../theme/customColors';

export default makeStyles(_theme => ({
  root: {
    color: 'red',
    borderColor: customColors.primary[600],
    backgroundColor: customColors.neutral.zero,
    '&:hover': {
      color: customColors.primary[800],
      borderColor: customColors.primary[800],
      backgroundColor: customColors.neutral[100]
    },
    transition: '0.3s'
  }
}));
