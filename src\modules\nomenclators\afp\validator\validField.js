// Taken from https://gist.github.com/rotvulpix/69a24cc199a4253d058c

const MIN_NUMBER_ALLOWED = 10.0;
const MAX_NUMBER_ALLOWED = 100;
const CODE_LENGTH = 2;
const percentRegex = /^\d{2}\.?\d{0,2}$/;
const regRule = new RegExp(percentRegex, 'i');

const isPercent = value => regRule.test(value);
const formatterPercent = value => {
  const number = value.toString().replace(/^0+|[^0-9.]/gi, '');
  return number
    ? `${number.toLocaleString(['es', 'en'], { style: 'percent', minimumFractionDigits: 2 })}%`
    : '';
};

const validFormat = percent =>
  percent ? percent.replace(/^0+|[^0-9]+/gi, '').replace(/(\d{2})(\d{1,2})/gi, '$1.$2') : '';

const RUT_REGEX = /(\d{1,2})\.?(\d{3})\.?(\d{3})-?(\d|k)/i;
const calculateCheckDigit = rut => {
  const stringRut = `${rut}`;
  let count = 0;
  let multiple = 2;

  for (let i = 1; i <= stringRut.length; i += 1) {
    const index = multiple * stringRut.charAt(stringRut.length - i);
    count += index;

    multiple = multiple < 7 ? multiple + 1 : 2;
  }

  const expectedCheckDigit = 11 - (count % 11);
  if (expectedCheckDigit === 10) return 'K';
  if (expectedCheckDigit === 11) return '0';
  return `${expectedCheckDigit}`;
};

const isValidRut = rut => {
  if (!rut || rut.trim().length < 3) return false;
  const cleanRut = rut.replace(/[^0-9kK-]/g, '');

  if (cleanRut.length < 3 || cleanRut.length > 10) return false;

  const split = cleanRut.split('-');
  if (split.length !== 2) return false;

  const numericRut = parseInt(split[0], 10);
  const checkDigit = split[1];

  const expectedCheckDigit = calculateCheckDigit(numericRut);
  return checkDigit === expectedCheckDigit && RUT_REGEX.test(rut);
};
const rutFormatter = rut =>
  rut
    ? rut
        .replace(/^0+|[^0-9kK]+/gi, '')
        .replace(/^k+/gi, '')
        .replace(/^0^k+|[^0-9kK]+|[kK](?!\s*$)/gi, '')
        .replace(/(\d{1,2})(\d{3})(\d{3})(\d|k)/gi, '$1.$2.$3-$4')
        .toUpperCase()
    : '';

const codeRegex = /(\d{2})/;
const isValidCode = (code = '') => {
  const number = code.toString().replace(/[^0-9]/gi, '');
  return codeRegex.test(number) && number.length === CODE_LENGTH ? number : '';
};
const codeFormatter = code =>
  code
    ? code
        .toString()
        .replace(/[^0-9]+/gi, '')
        .replace(/(\d{2})/gi, '$1')
    : '';

const codeMatchRule = code => isValidCode(code);

const rutMatchRule = rut => isValidRut(rut);
const percentageMatchRule = value =>
  isPercent(value) && value < MAX_NUMBER_ALLOWED && value > MIN_NUMBER_ALLOWED
    ? formatterPercent(value)
    : '';

const defaultFormatter = (value = '') =>
  value
    .replace(/^\s|[^a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûçğş0-9.',´`^¨~-\s]/gi, '')
    .replace(/\s+/g, ' ');

export {
  rutMatchRule,
  percentageMatchRule,
  codeMatchRule,
  isValidCode,
  isValidRut,
  rutFormatter,
  codeFormatter,
  formatterPercent,
  validFormat,
  defaultFormatter
};
