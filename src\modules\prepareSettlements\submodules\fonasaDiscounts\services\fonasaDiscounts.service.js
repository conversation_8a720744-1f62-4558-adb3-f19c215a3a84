/* eslint-disable no-console */

import {
  setWasFonasaProcessExecuted,
  setCurrentDate,
  setInDaysLimitRange,
  setInNumberDaysLimitRange,
  setBaseMinimumPensionExecuted
} from '../actions';

import { axiosRequest } from '../../../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;
//------------------------------------------------------------------------------------------------
export const insertFonasaDiscount = async arrayJSON => {
  const data = await axiosRequest
    .post(`${api}/preparesettlements/fonasa-discounts`, arrayJSON)
    .catch(err => {
      console.error(err);
      return {
        data: {},
        isError: true
      };
    });
  return {
    data,
    isError: false
  };
};
//------------------------------------------------------------------------------------------------
export const getMonthYear = async dispatch => {
  const { data } = await axiosRequest
    .get(`${api}/preparesettlements/fonasa-discounts/time`)
    .catch(err => {
      console.error(err);
      return { data: '', isError: true };
    });
  dispatch(setCurrentDate(data));
  return data;
};
//------------------------------------------------------------------------------------------------
export const wasFonasaExecuted = async dispatch => {
  const { data } = await axiosRequest
    .get(`${api}/verifycronexecution/BULK_LOAD_FONASA`)
    .catch(err => {
      console.error(err);
      return { data: { result: { alreadyExecuted: false } } };
    });
  dispatch(setWasFonasaProcessExecuted(data?.result?.alreadyExecuted));
};
//------------------------------------------------------------------------------------------------
export const isInDaysLimitRange = () => async dispatch => {
  const { data } = await axiosRequest
    .get(`${api}/preparesettlements/indaylimitrange`)
    .catch(err => {
      console.error(err);
      dispatch(setInNumberDaysLimitRange(0));
      return { data: { result: { isInDaysLimitRange: false } } };
    });
  dispatch(setInDaysLimitRange(data?.result?.isInDaysLimitRange));
  dispatch(setInNumberDaysLimitRange(data?.result?.nDays));
};
//------------------------------------------------------------------------------------------------
export const baseMinimumPensionExecuted = () => async dispatch => {
  const { data } = await axiosRequest
    .get(`${api}/verifycronexecution/CRON_BASE_MINIMUN_PENSION_WORKER`)
    .catch(err => {
      console.error(err);
      return { data: { result: { alreadyExecuted: false } } };
    });
  dispatch(setBaseMinimumPensionExecuted(data?.result?.alreadyExecuted));
};
