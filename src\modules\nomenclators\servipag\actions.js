//------------------------------------------------------------------------------------------------
export const CREATE_NOM_SERVIPAG = 'CREATE_NOM_SERVIPAG';
export const createNomenclatorServipag = servipag => {
  return {
    type: CREATE_NOM_SERVIPAG,
    data: { servipag }
  };
};

//------------------------------------------------------------------------------------------------
export const DELETE_NOM_SERVIPAG = 'DELETE_NOM_SERVIPAG';
export const deleteNomenclatorServipag = servipag => {
  return {
    type: DELETE_NOM_SERVIPAG,
    data: { servipag }
  };
};

//------------------------------------------------------------------------------------------------
export const UPDATE_NOM_SERVIPAG = 'UPDATE_NOM_SERVIPAG';
export const updateNomenclatorServipag = (prevServipag, newServipag) => {
  return {
    type: UPDATE_NOM_SERVIPAG,
    data: { prevServipag, newServipag }
  };
};

//------------------------------------------------------------------------------------------------
export const GET_SERVIPAG = 'GET_SERVIPAG';
export const servipagBranchOffices = results => {
  return {
    type: GET_SERVIPAG,
    data: results || []
  };
};
