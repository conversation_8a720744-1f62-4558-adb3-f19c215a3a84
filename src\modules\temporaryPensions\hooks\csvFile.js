/* eslint-disable import/no-unresolved */
import { getEncoding } from '../../../utils/encodingFile';

const INVALID_FILE_EXTENSION = 'Formato de Archivo Incorrecto';
const FILE_SIZE_IS_TOO_BIG = 'Excede el tamaño permitido';
const VALID_EXTENSIONS = ['.csv'];
const MAX_FILE_SIZE = 10485760;
const NOT_VALID_CSV_CHARS = /[^\s\n+\-*/=@#&%$!¡¿?ºª.:;,_|><´`¨""{})'^[\]~áéíóúàèìòùãẽĩõñũỹg̃äöüëïâêîôûçğşa-z0-9]/im;
const SCIENTIFIC_NOTATION = /E\+\d+/i;

const checkIfLinesContainScientificNotation = rows =>
  rows
    .map(row => SCIENTIFIC_NOTATION.test(row))
    .some(hasScientificNotation => hasScientificNotation);

const readFile = async (file, createJSON, SCIENTIFIC_NOTATION_ERROR) => {
  const encoding = await getEncoding(file);

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async () => {
      const { result } = reader;
      if (NOT_VALID_CSV_CHARS.test(result)) {
        reject(new Error('Archivo dañado.'));
      }
      const splittedResult = result.split('\n');
      splittedResult.shift();

      const rows = splittedResult.filter(row => row && row.trim().length > 1);
      if (!rows.length) {
        reject(new Error('Archivo vacio.'));
      }

      if (checkIfLinesContainScientificNotation(rows)) {
        reject(new Error(SCIENTIFIC_NOTATION_ERROR));
      }
      resolve(rows.map(row => createJSON(row.split(/,|;/))));
    };
    reader.onerror = () => {
      return reject(new Error('Error al leer el archivo.'));
    };

    reader.readAsText(file, encoding);
  });
};

const isUnique = (arr, id, key) => {
  return arr.filter(item => item[key] === id);
};

const validateFile = ({ name, size }, fn = () => { /*any*/ }) => {
  let isValid = true;
  const fileExtension = name
    .split('.')
    .pop()
    .toLowerCase();

  if (VALID_EXTENSIONS.indexOf(`.${fileExtension}`) === -1) {
    fn(INVALID_FILE_EXTENSION);
    isValid = false;
  }

  if (size > MAX_FILE_SIZE) {
    fn(FILE_SIZE_IS_TOO_BIG);
    isValid = false;
  }
  return isValid;
};

export { readFile, isUnique, validateFile, VALID_EXTENSIONS };
