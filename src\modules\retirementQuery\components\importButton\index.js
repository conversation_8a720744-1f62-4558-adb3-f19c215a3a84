/* eslint-disable react/prop-types */
import React from 'react';
import { Grid, Tooltip } from '@material-ui/core';
import { withStyles } from '@material-ui/core/styles';
import FormHelperText from '@material-ui/core/FormHelperText';
import AttachFileIcon from '@material-ui/icons/AttachFile';
import useOnlineStatus from '@rehooks/online-status';
import { generalClasses as styles } from './importButtonStyles';

const IMPORT = 'Importar pdf';

const ImportButton = props => {
  const { classes, errorMessage = [], footer = '', onClick, disabled, text = IMPORT } = props;
  const onlineStatus = useOnlineStatus();
  return (
    <React.Fragment>
      <Grid className={classes.formControl} disabled={disabled || !onlineStatus}>
        <Tooltip title={text} aria-label={text}>
          <span>
            <AttachFileIcon onClick={onClick} className={classes.attachIcon} />
          </span>
        </Tooltip>
        <FormHelperText id="component-helper-text" error={errorMessage.length > 0}>
          {errorMessage.join(', ') || footer}
        </FormHelperText>
      </Grid>
    </React.Fragment>
  );
};

export default withStyles(styles)(ImportButton);
