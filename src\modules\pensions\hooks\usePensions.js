/* eslint-disable import/no-unresolved */
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useProgress } from 'components';
import { linkPensioners, pensionsLinkEnabledAndCancelable } from '../services/pensioner.service';

import { fetchTemporaryPensions } from '../../temporaryPensions/services/temporary.service';
import { temporaryPensions } from '../../temporaryPensions/actions';

export const initialRequest = async dispatch => {
  const [importedPensions, attrs] = await Promise.all([
    fetchTemporaryPensions(), // another option
    dispatch(pensionsLinkEnabledAndCancelable())
  ]);
  await dispatch(temporaryPensions(importedPensions)); // another option

  return { importedPensions, ...attrs };
};
export const useLink = () => {
  const dispatch = useDispatch();
  const [isBusyLinked, setBusyLinked] = useState(false);
  const [successLinkedSnackbar, setSuccessLinkedSnackbar] = useState(false);
  const [errorLinkedSnackbar, setErrorLinkedSnackbar] = useState(false);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  useEffect(() => {
    initialRequest(dispatch);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    onLink: async () => {
      if (!isBusyLinked) {
        setBusyLinked(true);
      } else {
        return;
      }
      const linkedPensioners = await dispatch(linkPensioners());
      if (linkedPensioners > 0) {
        progress.show();
        await initialRequest(dispatch);
        setSuccessLinkedSnackbar(true);
        setErrorLinkedSnackbar(false);
      } else {
        setErrorLinkedSnackbar(true);
        setSuccessLinkedSnackbar(false);
      }
      progress.hide();
      setBusyLinked(false);
    },
    successLinkedSnackbar,
    errorLinkedSnackbar,
    setSuccessLinkedSnackbar,
    setErrorLinkedSnackbar
  };
};
