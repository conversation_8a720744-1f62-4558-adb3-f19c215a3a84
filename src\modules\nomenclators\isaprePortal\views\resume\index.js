/* eslint-disable react/prop-types */
/* eslint-disable react/forbid-prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Typography, CardHeader, Card, Button } from '@material-ui/core';
import { useDispatch } from 'react-redux';
import useRouter from '../../../../../utils/useRouter';

import useStyles from '../styles';
import ResumeError from './resumeError';
import { cleanIsaprePortalResumeErrors } from '../../actions';

const Resume = props => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { fileDataError, isError } = props;
  const classes = useStyles();

  const handleOnClick = () => {
    if (isError) {
      dispatch(cleanIsaprePortalResumeErrors());
    }
    router.history.push('/mantenedores/isapres/portal');
  };

  return (
    <Card className={classes.content}>
      <CardHeader title="Resumen de importación" className={classes.headerImport} />
      <>
        <div className={classes.principalErrors}>
          <Typography>
            <b>Existen errores en el archivo que impidieron su importación</b>
          </Typography>
        </div>
        <ResumeError
          errors={fileDataError.errors}
          requireFields="Rut Afiliado, Total a descontar, Isapre."
        />
      </>
      <div className={classes.footerButton}>
        <Button onClick={handleOnClick} className={classes.acceptButton}>
          Aceptar
        </Button>
      </div>
    </Card>
  );
};

Resume.propTypes = {
  fileDataError: PropTypes.object,
  isError: PropTypes.bool
};
Resume.defaultProps = {
  fileDataError: { errors: [] },
  isError: false
};

export default Resume;
