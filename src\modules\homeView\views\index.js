/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable import/no-unresolved */
import React, { useState, useEffect } from 'react';
import { Card, Grid, Typography } from '@material-ui/core';
import { HeaderContent } from 'components';
import useStyles from './styles';

import RapidLinks from '../components/rapidLinks';
import { Page } from '../../../components';
import { listOfRapidLinks } from '../components/rapidLinks/listOfRapidLinks';
import SearchBar from '../components/searchPensioner';
import DueDates from '../components/table';
import StackedBarPlot from '../components/plotPensioners';
import Legend from '../components/plotPensioners/legend';
import colorObj from '../components/plotPensioners/sharedData';

import { queryDueDays, queryHistoricalData } from '../services/homeView.service';

const TITLE = 'Control pensiones';
const TITLE_RAPID_LINKS = 'Links rápidos';
const TITLE_SEARCH_PENSIONER = 'Búsqueda de pensionado';
const TITLE_TABLE_MONTHLY_ACTIVITIES = 'Listado de actividades mensuales';
const TITLE_STACKBAR_PLOT = 'Gasto mensual de pensiones vigentes';

const defaultData = [
  { activity: 'Enlace mes anterior' },
  { activity: 'Inactivar/Reactivar pensiones' },
  { activity: 'Validar pensión mínima' },
  { activity: 'Actualizar datos pensionados' },
  { activity: 'Carga masiva CCAF/IPS' },
  { activity: 'Carga masiva Fonasa/Los Andes' },
  { activity: 'Generación de liquidación' },
  { activity: 'Conciliación con IPS' },
  { activity: 'Upload de nómina bancaria' },
  { activity: 'Giro a terceros' },
  { activity: 'Generación de capitales vigentes' },
  { activity: 'Carga Enlace Mes N+1' }
];

const groups = ['0', 'spentSurvival', 'spentAccident', 'spentProfessionalDisease'];
const Home = () => {
  const classes = useStyles();
  const [dataDueDays, setDataDueDays] = useState([]);
  const [dataHistoricalPensioner, setDataHistoricalPensioner] = useState([]);

  useEffect(() => {
    queryDueDays(setDataDueDays);
    queryHistoricalData(setDataHistoricalPensioner);
  }, []);

  return (
    <Page className={classes.root} title="Home">
      <Grid>
        <HeaderContent title={TITLE} />
      </Grid>
      <Grid className={classes.searchGrid}>
        <SearchBar title={TITLE_SEARCH_PENSIONER} />
      </Grid>
      <Grid>
        <Typography className={classes.componentTitle}>{TITLE_STACKBAR_PLOT}</Typography>
      </Grid>
      <Grid container spacing={5}>
        <Grid item className={classes.plotGrid}>
          <Card className={classes.cardStackedBarPlot}>
            {dataHistoricalPensioner.length > 0 && (
              <StackedBarPlot data={dataHistoricalPensioner} groups={groups} colorObj={colorObj} />
            )}
          </Card>
        </Grid>
        <Grid item className={classes.plotGrid}>
          <Card className={classes.cardPlotLegend}>
            <Legend groups={groups} colorObj={colorObj} />
          </Card>
        </Grid>
      </Grid>
      <Grid container spacing={5}>
        <Grid item>
          <DueDates
            title={TITLE_TABLE_MONTHLY_ACTIVITIES}
            data={dataDueDays.length > 0 ? dataDueDays : defaultData}
          />
        </Grid>
        <Grid item>
          <RapidLinks linkList={listOfRapidLinks} componentTitle={TITLE_RAPID_LINKS} />
        </Grid>
      </Grid>
    </Page>
  );
};

export default Home;
