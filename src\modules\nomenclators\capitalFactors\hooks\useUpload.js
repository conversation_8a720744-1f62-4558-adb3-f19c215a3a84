import {
  setFactorsFileData,
  setConcurrenciesFileData,
  setFileError,
  setFileType,
  setIsDownloading,
  setFactorsFilenameError,
  setConcurrenciesFilenameError
} from '../actions';
import readFile from '../utils/readFile';
import {
  validateFile,
  createFactorsJSON,
  createConcurrenciesJSON,
  validateFactorsData,
  validateConcurrenciesData
} from '../validator/csvValidator';

const FACTORS_TYPE = 'factores';

const isFactor = type => type === FACTORS_TYPE;

const cleanErrors = (type, dispatch) => {
  dispatch(setFileError([]));
  if (isFactor(type)) {
    dispatch(setFactorsFilenameError(''));
    dispatch(setFactorsFileData([]));
  } else {
    dispatch(setConcurrenciesFilenameError(''));
    dispatch(setConcurrenciesFileData([]));
  }
};

const uploadFile = async ({
  file,
  type,
  dispatch,
  currentMonthYear,
  currentYear,
  router,
  progress
}) => {
  cleanErrors(type, dispatch);
  const isValidFile = validateFile({
    file,
    type,
    dispatch,
    currentMonthYear,
    currentYear,
    isFactor
  });
  if (isValidFile) {
    dispatch(setIsDownloading(true));
    progress.show();
    const { data, err: readError } = await readFile(
      file,
      isFactor(type) ? createFactorsJSON : createConcurrenciesJSON
    )
      .then(res => {
        return { data: res };
      })
      .catch(err => {
        return { data: [], err };
      });

    if (readError) {
      progress.hide();
      dispatch(setIsDownloading(false));
      return isFactor(type)
        ? dispatch(setFactorsFilenameError(readError.message))
        : dispatch(setConcurrenciesFilenameError(readError.message));
    }
    const errorRows = isFactor(type) ? validateFactorsData(data) : validateConcurrenciesData(data);
    dispatch(setIsDownloading(false));
    progress.hide();
    if (errorRows.length) {
      dispatch(setFileError(errorRows));
      dispatch(setFileType(type));
      router.history.push('/mantenedores/factores-capitales/errores');
      return false;
    }
    isFactor(type) ? dispatch(setFactorsFileData(data)) : dispatch(setConcurrenciesFileData(data));
    return true;
  }
  return false;
};

export default uploadFile;
