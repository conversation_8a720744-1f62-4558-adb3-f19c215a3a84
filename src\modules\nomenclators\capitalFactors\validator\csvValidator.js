import { setFactorsFilenameError, setConcurrenciesFilenameError } from '../actions';
import isValidRut from './rutValidator';

const CSV_FILE_REQUIRED = 'Debe subir un archivo CSV';
const FILE_SIZE_EXCEEDED = 'El archivo no debe pasar los 10 MB';
const REQUIRED_FIELDS_CSV = 'Existen campos obligatorios vacíos';
const NOT_NUMERIC_FACTOR = 'El factor debe ser un valor numérico';
const NOT_NUMERIC_CONCURRENCY = 'El porcentaje debe ser un valor numérico';

const MAX_FILE_SIZE = 10485760;

const isString = obj => Object.prototype.toString.call(obj) === '[object String]';

const transformDiacritic = value =>
  value
    .replace(/[aàáâãäå]/gi, '[aàáâãäå]')
    .replace(/[eèéêë]/gi, '[eè<PERSON><PERSON><PERSON>]')
    .replace(/[iìíîï]/gi, '[iì<PERSON><PERSON><PERSON>]')
    .replace(/[oòóôõö]/gi, '[oòóôõö]')
    .replace(/[uùúûü]/gi, '[uùúûü]')
    .replace(/[nñ]/gi, '[nñ]');

const escapeSpecialChars = value =>
  value && isString(value) ? value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') : '';

const escapeChars = value =>
  value && isString(value) ? transformDiacritic(escapeSpecialChars(value)) : value;

const validateFile = ({ file, type, dispatch, currentMonthYear, currentYear, isFactor }) => {
  const acceptedFactorsFilenameRegex = new RegExp(`^factores_${currentYear}.csv$`, 'i');
  const acceptedConcurrenciesFilenameRegex = new RegExp(
    `^concurrencias_${currentMonthYear}.csv$`,
    'i'
  );

  const validateName = (name, regex) => regex.test(name);

  const [fileType] = file?.name?.split('.')?.slice(-1) || [];

  dispatch(setFactorsFilenameError(''));
  dispatch(setConcurrenciesFilenameError(''));
  if (fileType !== 'csv') {
    dispatch(
      isFactor(type)
        ? setFactorsFilenameError(CSV_FILE_REQUIRED)
        : setConcurrenciesFilenameError(CSV_FILE_REQUIRED)
    );
    return false;
  }
  const isValidName = validateName(
    file.name,
    isFactor(type) ? acceptedFactorsFilenameRegex : acceptedConcurrenciesFilenameRegex
  );
  if (!isValidName) {
    isFactor(type)
      ? dispatch(setFactorsFilenameError(`El nombre debe ser ${type}_${currentYear}.csv`))
      : dispatch(
          setConcurrenciesFilenameError(`El nombre debe ser ${type}_${currentMonthYear}.csv`)
        );
    return false;
  }
  if (file.size > MAX_FILE_SIZE) {
    isFactor(type)
      ? dispatch(setFactorsFilenameError(FILE_SIZE_EXCEEDED))
      : dispatch(setConcurrenciesFilenameError(FILE_SIZE_EXCEEDED));
    return false;
  }
  return true;
};

const truncateFactors = value =>
  value.replace(/^(\d+)\.?(\d{0,3})?\d*$/, '$1.$2').replace(/(\.0+|\.)$/, '');

const truncateDecimals = value =>
  value.replace(/^(\d+)\.?(\d{0,2})?\d*$/, '$1.$2').replace(/(\.0+|\.)$/, '');

const createFactorsJSON = values => {
  const mappedKeys = ['key', 'factor'];
  return mappedKeys.reduce((o, k, i) => {
    const attrValue =
      typeof values[i] === 'string' ? values[i].replace(/(\r\n|\n|\r)/gm, '') : values[i];
    const truncatedFactor = k === 'factor' && truncateFactors(attrValue);
    return { ...o, [k]: truncatedFactor || attrValue };
  }, {});
};

const createConcurrenciesJSON = values => {
  const mappedKeys = ['beneficiaryRut', 'concurrencyPercentage','concurrencyReceivable','mutualPercentage','istPercentage','islPercentage'];
  return mappedKeys.reduce((o, k, i) => {
    const attrValue =
      typeof values[i] === 'string' ? values[i].replace(/(\r\n|\n|\r)/gm, '') : values[i];
    const truncatedConcurrencies = k === 'concurrencyPercentage' && truncateDecimals(attrValue);
    return { ...o, [k]: truncatedConcurrencies || attrValue };
  }, {});
};

const validateKeyUniqueness = (key, index, allRows = []) => {
  const rowRegex = new RegExp(`^${escapeChars(key)}$`, 'i');
  return allRows.some((row, i) => {
    if (index !== i) {
      return rowRegex.test(row.key);
    }
    return false;
  });
};

const MAX_VALUE_FACTORS = 999.999;
const MAX_VALUE_CONCURRENCIES = 100;
const isNumberInRange = (number, maxValue) => number >= 0 && number <= maxValue;

const validateFactorsData = jsonData => {
  const errors = [];
  jsonData.forEach((row, i) => {
    const rowError = [];
    allFieldsPresent([row.key, row.factor], rowError);
    if (isNaN(row.factor)) rowError.push(NOT_NUMERIC_FACTOR);
    if (validateKeyUniqueness(row.key, i, jsonData))
      rowError.push(`La llave ${row.key} debe ser única`);
    if (!isNumberInRange(row.factor, MAX_VALUE_FACTORS))
      rowError.push(`El factor ${row.factor} no esta en el rango de 0 a 999`);
    if (rowError.length) errors.push({ errors: rowError, row: i + 1 });
  });
  return errors;
};

const validateConcurrenciesData = jsonData => {
  const errors = [];
  jsonData.forEach((row, i) => {
    const rowError = [];
    const trimmedRut = row.beneficiaryRut
      .replace(/ /, '')
      .replace(/^0+/, '')
      .trim();
    trimmedRut && isValidRut(trimmedRut, rowError);
    if (rowError.length > 0) rowError.unshift(`Rut: ${row.beneficiaryRut}`);
    allFieldsPresent([trimmedRut, row.concurrencyPercentage], rowError);
    if (isNaN(row.concurrencyPercentage)) rowError.push(NOT_NUMERIC_CONCURRENCY);
    if (!isNumberInRange(row.concurrencyPercentage, MAX_VALUE_CONCURRENCIES))
      rowError.push(
        `El % de concurrencia ${row.concurrencyPercentage} no esta en el rango de 0 a 100`
      );
    if (isNaN(parseFloat(row.concurrencyReceivable))) rowError.push('La concurrencia por cobrar debe ser un valor numérico');
    if (isNaN(parseFloat(row.mutualPercentage))) rowError.push('El porcentaje Mutual debe ser un valor numérico');
    if (isNaN(parseFloat(row.istPercentage))) rowError.push('El porcentaje IST debe ser un valor numérico');
    if (isNaN(parseFloat(row.islPercentage))) rowError.push('El porcentaje ISL debe ser un valor numérico');
    if (rowError.length) errors.push({ errors: rowError, row: i + 1 });
  });
  return errors;
};

const allFieldsPresent = (fields, rowError) => {
  if (fields.some(field => !field || !field.trim())) rowError.push(REQUIRED_FIELDS_CSV);
};

export {
  validateFile,
  createFactorsJSON,
  createConcurrenciesJSON,
  validateFactorsData,
  validateConcurrenciesData
};
