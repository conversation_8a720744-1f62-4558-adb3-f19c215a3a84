import { GET_BENEFICIARIES_PENSIONS, LINK_PENSIONS, IS_PENSIONS_AVAILABLE } from './actions';

const initialState = { data: [], isLinked: true };

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case GET_BENEFICIARIES_PENSIONS:
      return {
        ...state,
        data: [...action.data.pensions]
      };
    case LINK_PENSIONS:
      return {
        ...state,
        isLinked: action.data
      };
    case IS_PENSIONS_AVAILABLE: {
      const attrs = JSON.parse(JSON.stringify(action.data));
      return {
        ...state,
        ...attrs
      };
    }
    default:
      return state;
  }
}
