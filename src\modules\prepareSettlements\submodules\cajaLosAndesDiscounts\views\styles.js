/* eslint-disable no-magic-numbers */
import { makeStyles } from '@material-ui/styles';

const styles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(3)
  },
  errorDisplay: {
    color: 'red',
    fontSize: 10
  },
  formControl: {
    marginTop: 20,
    marginRight: 20,
    marginBottom: 20,
    color: 'black',
    backgroundColor: '#00FFFFF'
  },
  spanFinalize: {
    display: 'inline-block',
    marginLeft: '33%',
    marginTop: 40
  },
  importText: {
    fontSize: 10,
    fontWeight: '1',
    marginLeft: 10,
    color: 'Gray'
  },
  subtitle: {
    fontSize: 11,
    fontWeight: 5
  },
  textfield: {
    width: '35%',
    marginTop: 20,
    marginRight: 20,
    minWidth: 210
  },
  textfieldLoaded: {
    width: '35%',
    marginTop: 20,
    marginRight: 20,
    minWidth: 210,
    '& .MuiInputBase-root.Mui-disabled': {
      color: '#212121'
    }
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15
  },
  divalert: {
    overflowY: 'auto',
    float: 'left',
    position: 'relative',
    maxHeight: '400px',
    width: '800px',
    fontSize: '15px'
  },
  tituloalert: {
    fontSize: '20px',
    height: '25px'
  }
}));

export default styles;
