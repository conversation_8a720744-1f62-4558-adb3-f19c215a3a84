/* eslint-disable react/forbid-prop-types */
/* eslint-disable react/prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Button as MUIButton, FormControl, FormHelperText, Tooltip } from '@material-ui/core';

import styleList from './styles';

const ButtonTooltip = (props) => {
  const {
    children,
    disabled,
    endIcon,
    errorMessage,
    footerMessage,
    href,
    onClick,
    startIcon,
    stylePreset,
    tooltipMessage
  } = props;

  const muiAttrs = {
    base: { size: 'small', variant: 'contained', color: 'primary' },
    login: { size: 'large' },
    submit: { size: 'small', variant: 'contained', color: 'primary' },
    cancel: { size: 'small', variant: 'contained', color: 'default' },
    attach: { variant: 'outlined' },
    search: { variant: 'outlined', size: 'medium' }
  };
  const selectedMuiAttrs = { ...muiAttrs.base, ...muiAttrs[stylePreset] };

  const useStyles = styleList[stylePreset] || styleList.base;
  const classes = useStyles();

  return (
    <FormControl classes={classes}>
      <Tooltip title={tooltipMessage} aria-label={tooltipMessage}>
        <span>
          <MUIButton
            color={selectedMuiAttrs.color}
            disabled={disabled}
            endIcon={endIcon}
            href={href}
            onClick={onClick}
            size={selectedMuiAttrs.size}
            startIcon={startIcon}
            variant={selectedMuiAttrs.variant}
          >
            {children}
          </MUIButton>
        </span>
      </Tooltip>
      <FormHelperText id="component-helper-text" error={!!errorMessage}>
        {errorMessage || footerMessage}
      </FormHelperText>
    </FormControl>
  );
};

ButtonTooltip.propTypes = {
  children: PropTypes.node,
  disabled: PropTypes.bool,
  endIcon: PropTypes.node,
  errorMessage: PropTypes.string,
  footerMessage: PropTypes.string,
  href: PropTypes.string,
  onClick: PropTypes.func,
  stylePreset: PropTypes.string,
  startIcon: PropTypes.node,
  tooltipMessage: PropTypes.string
};
ButtonTooltip.defaultProps = {
  children: null,
  disabled: false,
  endIcon: null,
  errorMessage: '',
  footerMessage: '',
  href: '',
  onClick: () => { /*any*/ },
  stylePreset: 'base',
  startIcon: null,
  tooltipMessage: ''
};

export default React.memo(ButtonTooltip);
