import { makeStyles } from '@material-ui/styles';

const styles = makeStyles(theme => ({
  root: {
    // eslint-disable-next-line no-magic-numbers
    padding: theme.spacing(3)
  },
  formControl: {
    marginTop: 20,
    marginRight: 20,
    marginBottom: 10,
    color: 'black',
    backgroundColor: '#00FFFFF'
  },
  importText: {
    fontSize: 10,
    fontWeight: '1',
    marginLeft: 10,
    marginBotton: 20,
    color: 'Gray'
  },
  spanFinalize: {
    display: 'inline-block',
    marginLeft: '33%',
    marginTop: 0
  },
  subtitle: {
    fontSize: 11,
    fontWeight: 5
  },
  textfield: {
    width: '34.5%',
    marginTop: 20,
    marginRight: 20,
    minWidth: 210
  },
  textfieldLoaded: {
    width: '34.5%',
    marginTop: 20,
    marginRight: 20,
    minWidth: 210,
    '& .MuiInputBase-root.Mui-disabled': {
      color: '#212121'
    }
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15
  },
  tooltip: {
    fontSize: 11,
    marginLeft: '33%',
    marginTop: 40
  }
}));

export default styles;
