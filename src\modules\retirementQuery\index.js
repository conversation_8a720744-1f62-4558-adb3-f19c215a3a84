/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React from 'react';
import PropTypes from 'prop-types';
import { renderRoutes } from 'react-router-config';
import { Page } from 'components';
import useStyles from './views/styles';
import DisplayRetired from './views/displayRetired';
import EditAssetsAndDiscounts from './views/editAssetsAndDiscounts';

const PensionsModule = ({ route }) => {
  const classes = useStyles();

  return (
    <Page className={classes.root} title="Pensionados">
      {renderRoutes(route.routes)}
    </Page>
  );
};

PensionsModule.propTypes = {
  route: PropTypes.shape({
    path: PropTypes.string,
    routes: PropTypes.array,
    component: PropTypes.func
  }).isRequired
};

// eslint-disable-next-line import/prefer-default-export
export { PensionsModule, DisplayRetired, EditAssetsAndDiscounts };
