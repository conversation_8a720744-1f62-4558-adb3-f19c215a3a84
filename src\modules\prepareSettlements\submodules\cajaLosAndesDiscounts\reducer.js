import {
  SET_CURRENT_MONTH_YEAR,
  SET_WAS_LOSANDES_EXECUTED,
  SET_IN_DAYS_LIMIT_RANGE,
  SET_IN_NUMBER_DAYS_LIMIT_RANGE,
  SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_NAME,
  SET_CAJA_LOS_ANDES_FILE_CREDITS_NAME,
  SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_NAME,
  CLEAN_CAJA_LOS_ANDES_FILE_MEMBERSHIPS,
  CLEAN_CAJA_LOS_ANDES_FILE_CREDITS,
  CLEAN_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS,
  SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_ERROR,
  SET_CAJA_LOS_ANDES_FILE_CREDITS_ERROR,
  SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_ERROR,
  SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_DATA,
  SET_CAJA_LOS_ANDES_FILE_CREDITS_DATA,
  SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_DATA,
  SET_CRON_BASE_MINIMUM_PENSION_EXECUTED,
  SET_SNACKBAR_MESSAGE_ERROR,
  SET_OPEN_SNACKBAR_ERROR
} from './actions';

const initialState = {
  currentMonthYear: '',
  wasLosAndesProcessExecuted: true,
  isInDaysLimitRange: false,
  cronBaseMinimumPensionExecuted: false,
  fileMembershipsName: '',
  fileMembershipsData: [],
  fileMembershipsErrors: [],
  fileCreditsName: '',
  fileCreditsData: [],
  fileCreditsErrors: [],
  fileAnotherDiscountsName: '',
  fileAnotherDiscountsData: [],
  fileAnotherDiscountsErrors: [],
  messageError: '',
  openSnackbarError: false
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case SET_CURRENT_MONTH_YEAR:
      return {
        ...state,
        currentMonthYear: action.data.currentMonthYear
      };
    case SET_WAS_LOSANDES_EXECUTED:
      return {
        ...state,
        wasLosAndesProcessExecuted: action.data
      };
    case SET_IN_DAYS_LIMIT_RANGE:
      return {
        ...state,
        isInDaysLimitRange: action.data.isInDaysLimitRange
      };
    case SET_IN_NUMBER_DAYS_LIMIT_RANGE:
      return {
        ...state,
        isInNumberDaysLimitRange: action.data.isInNumberDaysLimitRange
      };      
    case SET_CRON_BASE_MINIMUM_PENSION_EXECUTED:
      return {
        ...state,
        cronBaseMinimumPensionExecuted: action.data.cronBaseMinimumPensionExecuted
      };
    case CLEAN_CAJA_LOS_ANDES_FILE_MEMBERSHIPS:
      return {
        ...state,
        fileMembershipsName: '',
        fileMembershipsData: [],
        fileMembershipsErrors: []
      };
    case CLEAN_CAJA_LOS_ANDES_FILE_CREDITS:
      return {
        ...state,
        fileCreditsName: '',
        fileCreditsData: [],
        fileCreditsErrors: []
      };
    case CLEAN_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS:
      return {
        ...state,
        fileAnotherDiscountsName: '',
        fileAnotherDiscountsData: [],
        fileAnotherDiscountsErrors: []
      };
    case SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_NAME:
      return {
        ...state,
        fileMembershipsName: action.data.fileName
      };
    case SET_CAJA_LOS_ANDES_FILE_CREDITS_NAME:
      return {
        ...state,
        fileCreditsName: action.data.fileName
      };
    case SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_NAME:
      return {
        ...state,
        fileAnotherDiscountsName: action.data.fileName
      };
    case SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_DATA:
      return {
        ...state,
        fileMembershipsData: action.data.fileData
      };
    case SET_CAJA_LOS_ANDES_FILE_CREDITS_DATA:
      return {
        ...state,
        fileCreditsData: action.data.fileData
      };
    case SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_DATA:
      return {
        ...state,
        fileAnotherDiscountsData: action.data.fileData
      };
    case SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_ERROR:
      return {
        ...state,
        fileMembershipsErrors: [...state.fileMembershipsErrors, action.data.fileError]
      };
    case SET_CAJA_LOS_ANDES_FILE_CREDITS_ERROR:
      return {
        ...state,
        fileCreditsErrors: [...state.fileCreditsErrors, action.data.fileError]
      };
    case SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_ERROR:
      return {
        ...state,
        fileAnotherDiscountsErrors: [...state.fileAnotherDiscountsErrors, action.data.fileError]
      };
    case SET_SNACKBAR_MESSAGE_ERROR:
      return {
        ...state,
        messageError: action.data.messageError
      };      
    case SET_OPEN_SNACKBAR_ERROR:
      return {
        ...state,
        openSnackbarError: action.data.openSnackbarError
      };       
    default:
      return state;
  }
}
