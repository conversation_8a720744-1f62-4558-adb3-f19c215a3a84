/* eslint-disable no-magic-numbers */
/* eslint-disable import/no-unresolved */
/* eslint-disable react-hooks/rules-of-hooks */
import moment from 'moment';

import { readFile, validateFile } from './txt';
import {
  setFileAnotherDiscountsName,
  cleanFileAnotherDiscounts,
  setFileAnotherDiscountsError,
  setFileAnotherDiscountsData
} from '../actions';

const DEFAULT_DATE = '01-01-1900';

function compare(attr) {
  return (a, b) => {
    if (a[attr] > b[attr]) return 1;
    if (b[attr] > a[attr]) return -1;
    return 0;
  };
}

const datamodelSchema = [
  { name: 'rut', required: true, order: 1, position: 13, length: 9, type: 'rut' },
  {
    name: 'anotherDiscountAmount',
    required: true,
    order: 2,
    position: 89,
    length: 10,
    type: 'decimal'
  },
  { name: 'archivo', required: true, order: 3, type: 'string'  },
  { name: 'fila', required: true, order: 3, type: 'integer'  }
];
const attrs = datamodelSchema.sort(compare).map(o => o.name);
const rutAttrs = datamodelSchema.filter(o => o.type === 'rut').map(o => o.name);
const dateAttrs = datamodelSchema.filter(o => o.type === 'date').map(o => o.name);
const decimalAttrs = datamodelSchema.filter(o => o.type === 'decimal').map(o => o.name);
const integerAttrs = datamodelSchema.filter(o => o.type === 'integer').map(o => o.name);

const createJSON = (row, indice) => {
  const values = datamodelSchema.map(attr => row.substr(attr.position, attr.length));
  return attrs.reduce((o, k, i) => {
    let attrValue = `${values[i]}`;
    if (dateAttrs.includes(k)) {
      const dateValue = moment(attrValue, 'DD-MM-YYYY').isValid() ? attrValue : DEFAULT_DATE;
      attrValue = moment(dateValue, 'DD-MM-YYYY');
    } else if (decimalAttrs.includes(k)) {
      const integer = parseInt(attrValue.substr(0, 8), 10);
      const decimal = attrValue.substr(8, 2);
      attrValue = parseFloat(`${integer}.${decimal}`);
    } else if (rutAttrs.includes(k)) {
      attrValue = attrValue.replace(/^0*(\d+)([\dk])$/i, '$1-$2').toUpperCase();
    } else if (integerAttrs.includes(k)) {
      attrValue = parseInt(attrValue, 10);
    }
    if(k === "archivo"){      
      attrValue ="Archivo-Otros-Descuentos";
    }else if(k === "fila"){     
      attrValue = indice;
    } 
    return { ...o, [k]: attrValue };
  }, {});
};

const useDataLoadAnotherDiscounts = (dispatch, acceptedFilenameRegex, acceptedFilename = '') => {
  return async file => {
    dispatch(cleanFileAnotherDiscounts());
    dispatch(setFileAnotherDiscountsName(file.name));
    if (
      validateFile(file, acceptedFilenameRegex, acceptedFilename, err =>
        dispatch(setFileAnotherDiscountsError(err))
      )
    ) {
      const data = await readFile(file, createJSON, datamodelSchema).catch(() => {
        dispatch(setFileAnotherDiscountsError('Error al leer archivo'));
        return [];
      });

      if (data.length > 0) {
        dispatch(setFileAnotherDiscountsData(data));
      }
    }
  };
};

export default useDataLoadAnotherDiscounts;
