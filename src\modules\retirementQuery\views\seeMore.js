/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import useOnlineStatus from '@rehooks/online-status';
import { Page, HeaderContent, SnackbarManager } from 'components';
import { Grid, Typography, Tabs, Tab, Paper } from '@material-ui/core';
import { useHistory, Link as RouterLink } from 'react-router-dom';
import useStyles from './styles';
import TabPanel from '../components/tabPanelGeneral';
import PensionerDetailTab from '../components/pensionerDetail/pensionerDetailTab';
import AssetsAndDiscountsTable from '../components/assetsAndDiscounts/assetsAndDiscountsTable';
import NonFormulableAssetsAndDiscountsTable from '../components/nonFormulableAssetsAndDiscounts/table';
import PensionerDocuments from '../components/pensionerDocuments/PensionerDocuments';
import UseFetchTabGeneralData from '../hooks/useFetchTabGeneralData';
import { useSnackbar } from '../hooks/useSnackbar';

const TITLE = 'Ver más';
const SUBTITLE = 'Consulta Pensionado';

const SeeMore = ({ role }) => {
  const history = useHistory();
  const { tabIndex } = history.location.state;
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const [value, setValue] = useState(tabIndex || 0);

  const {
    text,
    error,
    setError,
    setText,
    successSnackbar,
    errorSnackbar,
    setSuccessSnackbar,
    setErrorSnackbar
  } = useSnackbar();

  const {
    location: { state }
  } = history;

  const onErrorSnackbar = (err = 'Hubo un error al mostrar el detalle') => {
    setError(err);
    setSuccessSnackbar(false);
    setErrorSnackbar(true);
  };

  const onSuccessSnackbar = (msg = 'El archivo fue importado exitosamente') => {
    setText(msg);
    setSuccessSnackbar(true);
    setErrorSnackbar(false);
  };

  const { fetch, settlementData, setSettlementData, isSuccess } = UseFetchTabGeneralData(
    onErrorSnackbar
  );

  useEffect(() => {
    if (!state) onErrorSnackbar();
    let { beneficiaryRut, causantRut, pensionCodeId } = state;
    if (!beneficiaryRut && causantRut) {
      const { beneficiary, causant } = state;
      beneficiaryRut = beneficiary.split('\n')[1].replace(/[^\dK-]/g, '');
      causantRut = causant.rut.replace(/[^\dK-]/g, '');
    }
    fetch({ rutBeneficiary: beneficiaryRut, rutCausant: causantRut, pensionCodeId });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <Page className={classes.root} title="Ver más">
      <Grid container spacing={2}>
        <Grid item xs={12}>
          {onlineStatus ? (
            <Typography className={classes.subtitle} color="textSecondary">
              {/* eslint-disable-next-line react/jsx-one-expression-per-line */}
              <RouterLink
                to={{
                  pathname: '/pensionados/consulta-pensionados/',
                  state: { isReturning: true }
                }}
              >
                {`${SUBTITLE} `}
              </RouterLink>
              / Pensionado
            </Typography>
          ) : (
            <Typography className={classes.subtitle} color="textSecondary">
              {/* eslint-disable-next-line react/jsx-one-expression-per-line */}
              {SUBTITLE} / Pensionado
            </Typography>
          )}
        </Grid>
      </Grid>
      <Grid container className={classes.gridStyle}>
        <Grid item xs={10}>
          <HeaderContent className={classes.title} title={TITLE} />
        </Grid>
      </Grid>
      {isSuccess && (
        <div>
          <Paper square>
            <Tabs
              value={value}
              indicatorColor="primary"
              textColor="primary"
              onChange={handleChange}
            >
              <Tab label="Datos generales del Beneficiario" />
              <Tab label="Haberes y descuentos" />
              <Tab label="Haberes y descuentos no formulables" />
              <Tab label="Documentos del Pensionado" />
            </Tabs>
            <TabPanel value={value} index={0}>
              <PensionerDetailTab
                userRole={role}
                data={settlementData}
                setData={setSettlementData}
                onErrorSnackbar={onErrorSnackbar}
                onSuccessSnackbar={onSuccessSnackbar}
              />
            </TabPanel>
            <TabPanel value={value} index={1}>
              <AssetsAndDiscountsTable
                userRole={role}
                data={settlementData}
                setData={setSettlementData}
                onErrorSnackbar={onErrorSnackbar}
                onSuccessSnackbar={onSuccessSnackbar}
              />
            </TabPanel>
            <TabPanel value={value} index={2}>
              <NonFormulableAssetsAndDiscountsTable
                userRole={role}
                data={settlementData}
                setData={setSettlementData}
              />
            </TabPanel>
            <TabPanel value={value} index={3}>
              <PensionerDocuments
                userRole={role}
                data={settlementData}
                onErrorSnackbar={onErrorSnackbar}
              />
            </TabPanel>
          </Paper>
        </div>
      )}
      <SnackbarManager
        handleRoute={false}
        error={error}
        text={text}
        successSnackbar={successSnackbar}
        errorSnackbar={errorSnackbar}
        setErrorSnackbar={setErrorSnackbar}
        setSuccessSnackbar={setSuccessSnackbar}
      />
    </Page>
  );
};

SeeMore.propTypes = {
  route: PropTypes.shape({
    path: PropTypes.string,
    routes: PropTypes.array,
    component: PropTypes.func
  }).isRequired
};

export default SeeMore;
