/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import { Grid } from '@material-ui/core';
import { HeaderContent } from 'components';
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getServipagBranchOffices, processServipag } from '../services/servipag.service';
import { deleteNomenclatorServipag } from '../actions';

import ServiPagTable from './components/serviPagTable';

const initialRequest = async dispatch => {
  await dispatch(getServipagBranchOffices());
};

const ServipagPage = props => {
  const dispatch = useDispatch();
  const [enabledButton, setEnabledButton] = useState(false);
  const state = {
    data: useSelector(store => store.servipag.data)
  };

  useEffect(() => {
    initialRequest(dispatch);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { onSuccessSnackbar, onErrorSnackbar, role } = props;

  const CONFLICT = 409;
  const handleConflict = oldData =>
    (!oldData.code || !oldData.name) && dispatch(deleteNomenclatorServipag(oldData));
  const keyValue = { code: 'Código', name: 'Nombre Sucursal' };
  const handleTranslate = key =>
    keyValue[key]
      ? `El ${keyValue[key]} que intenta guardar ya existe`
      : `El registro que intenta guardar ya existe`;

  const handleProcess = (okMessage, erroMessage, operation) => async (newData, oldData) => {
    try {
      if (operation === 'create' || (operation === 'update' && oldData) || operation === 'delete') {
        const result = await dispatch(processServipag(oldData, newData, operation))
          .then(() => {
            onSuccessSnackbar(okMessage);
            return true;
          })
          .catch(error => {
            let messageError;
            if (error.response.status === CONFLICT) {
              const key = Object.keys(error.response.data.error.keyValue);
              if (operation === 'update') handleConflict(oldData);
              messageError = handleTranslate(key);
              onErrorSnackbar(messageError);
              setEnabledButton(true);
            } else {
              messageError = erroMessage;
              onErrorSnackbar(messageError);
              setEnabledButton(true);
            }
            throw new Error(messageError);
          });
        if (!result) return false;
      }
      return true;
    } catch ({ message }) {
      onErrorSnackbar(message);
      setEnabledButton(true);
      throw new Error(message);
    }
  };

  return (
    <>
      <Grid container justify="space-between">
        <HeaderContent overline="Configuraciones / Mantenedores" title="Sucursales Servipag" />
      </Grid>
      <ServiPagTable
        userRole={role}
        enabledButton={enabledButton}
        data={state.data}
        onCreate={handleProcess(
          'Los valores se guardaron correctamente',
          'Error en actualización de registro',
          'create'
        )}
        onUpdate={handleProcess(
          'Los valores se guardaron correctamente',
          'Error en actualización de registro',
          'update'
        )}
        onDelete={handleProcess('Registro eliminado', 'Error en eliminación de registro', 'delete')}
      />
    </>
  );
};

// eslint-disable-next-line import/prefer-default-export
export { ServipagPage };
