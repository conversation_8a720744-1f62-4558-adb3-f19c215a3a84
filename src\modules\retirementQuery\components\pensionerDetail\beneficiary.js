/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-param-reassign */
/* eslint-disable no-useless-escape */
/* eslint-disable react/prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import { useSelector, useDispatch } from 'react-redux';
import useStyles from './style';
import createTable from '../../utils/createTable';
import {
  setBeneficiaryEmail,
  setModifiedFieldErrors,
  setBeneficiaryPhone,
  setCountry,
  setGender
} from '../../actions';

import {
  EMAIL_PATTERN,
  emailFormatter,
  PHONE_REGEX,
  numericOnlySanitizer
} from '../../utils/formatters';

import { nationalities } from '../../../../resources/nationalities.json';

const PHONE_MAX_LENGHT = 9;

let countries = [];
let genders = [];

const handleEmailChange = (
  e,
  {
    key,
    dispatch,
    action,
    actionError,
    modifiedFieldErrors,
    formatter,
    validation,
    isRequired,
    maxLength
  }
) => {
  const { value } = e.target;

  const formattedWord = formatter(value) || '';

  const isValidFormatAndLength =
    formattedWord.length <= maxLength && validation.test(formattedWord);

  const isCorrect = isRequired
    ? isValidFormatAndLength
    : formattedWord.length === 0 || isValidFormatAndLength;

  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));

  dispatch(action(formattedWord));
};

const handleFieldChange = (
  e,
  {
    key,
    dispatch,
    action,
    actionError,
    validation,
    modifiedFieldErrors,
    maxLength,
    formatter,
    isRequired
  }
) => {
  let formattedPhone = formatter(e?.target?.value);

  if (formattedPhone?.length > maxLength) {
    formattedPhone = formattedPhone.substring(0, maxLength);
  }

  const isValidFormatAndLength =
    formattedPhone?.length <= maxLength && validation.test(formattedPhone);

  const isCorrect = isRequired
    ? isValidFormatAndLength
    : formattedPhone.length === 0 || isValidFormatAndLength;

  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));

  dispatch(action(formattedPhone));
};

const handleSelectChange = (e, { key, dispatch, action }) => {
  const { value } = e.target;

  dispatch(action(value));
};

const Beneficiary = ({ values, editable }) => {
  countries = nationalities.map(curr => curr.name);
  genders = ['Masculino', 'Femenino'];
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();

  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);
  const beneficiaryEmail = useSelector(store => store.queryPensions.beneficiaryEmail);
  const beneficiaryPhone = useSelector(store => store.queryPensions.beneficiaryPhone);
  const gender = useSelector(store => store.queryPensions.gender);
  const country = useSelector(store => store.queryPensions.country);

  const rowFormation = [
    [
      { key: 'beneficiaryFullName', name: 'Nombre beneficiario' },
      { key: 'beneficiaryRut', name: 'RUT beneficiario' },
      { key: 'beneficiaryBirthDate', name: 'Fecha de nacimiento' }
    ],
    [
      {
        key: 'gender',
        name: 'Género',
        type: 'select',
        options: genders,
        handleInputChange: handleSelectChange,
        selectedValue: gender,
        condition: true,
        isRequired: true,
        dispatch,
        action: setGender,
        actionError: setModifiedFieldErrors,
        modifiedField: gender,
        modifiedFieldErrors
      },
      {
        key: 'beneficiaryPhone',
        name: 'Teléfono',
        type: 'text',
        toWrite: beneficiaryPhone,
        handleInputChange: handleFieldChange,
        condition: true,
        errorMessage: 'El valor es inválido',
        validation: PHONE_REGEX,
        dispatch,
        action: setBeneficiaryPhone,
        actionError: setModifiedFieldErrors,
        modifiedField: beneficiaryPhone,
        modifiedFieldErrors,
        maxLength: PHONE_MAX_LENGHT,
        formatter: numericOnlySanitizer
      },
      {
        key: 'country',
        name: 'Nacionalidad',
        type: 'select',
        options: countries,
        handleInputChange: handleSelectChange,
        selectedValue: country,
        condition: true,
        isRequired: true,
        dispatch,
        action: setCountry,
        actionError: setModifiedFieldErrors,
        modifiedField: country,
        modifiedFieldErrors
      }
    ],
    [
      {
        key: 'beneficiaryEmail',
        name: 'Correo electrónico',
        type: 'text',
        toWrite: beneficiaryEmail,
        handleInputChange: handleEmailChange,
        condition: true,
        validation: EMAIL_PATTERN,
        formatter: emailFormatter,
        errorMessage: 'Email incorrecto',
        dispatch,
        action: setBeneficiaryEmail,
        actionError: setModifiedFieldErrors,
        modifiedField: beneficiaryEmail,
        modifiedFieldErrors,
        maxLength: 120
      }
    ]
  ];

  return (
    <Card className={classes.cardContainer}>
      <CardHeader title="Información beneficiario" className={classes.cardHeader} />
      <Divider />
      <CardContent className={classes.content}>
        {createTable({
          data: values,
          format: rowFormation,
          editable: editable && onlineStatus,
          panelName: 'beneficiary'
        })}
      </CardContent>
    </Card>
  );
};

Beneficiary.propTypes = {
  values: PropTypes.shape({}).isRequired
};

export default Beneficiary;
