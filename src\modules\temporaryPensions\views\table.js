/* eslint-disable no-undef */
/* eslint-disable no-magic-numbers */
/* eslint-disable no-param-reassign */
import React from 'react';
import { TableCell, TableRow } from '@material-ui/core';

const headers = [
  { label: 'Código de pensión' },
  { label: 'RUT beneficiario' },
  { label: 'Nombre beneficiario' },
  { label: 'Tipo de pensión' },
  { label: 'Tipo de vigencia' },
  { label: 'Pensión base' },
  { label: 'Grado de incapacidad' },
  { label: 'Nº de resolución' }
];
export const renderTableHeader = () => {
  return headers.map((key, _index) => {
    return <TableCell key={key.label}>{key.label}</TableCell>;
  });
};

const clean = rut => {
  return typeof rut === 'string' ? rut.replace(/^0+|[^0-9kK]+/g, '').toUpperCase() : '';
};
const format = rut => {
  rut = clean(rut);
  let result = `${rut.slice(-4, -1)}-${rut.substr(rut.length - 1)}`;
  for (let i = 4; i < rut.length; i += 3) {
    result = `${rut.slice(-3 - i, -i)}.${result}`;
  }

  return result;
};

export const renderTableData = result => {
  if (result.length <= 0) {
    return (
      <TableRow hover style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <TableCell>No existen Registros</TableCell>
      </TableRow>
    );
  }

  const sortedResult = result.sort((a, b) => a.beneficiary.name.localeCompare(b.beneficiary.name));

  return sortedResult.map((item, index) => {
    const {
      id,
      country = '',
      pensionCodeId = '',
      beneficiary = {},
      pensionType = '',
      basePension = '',
      disabilityDegree = '',
      resolutionNumber = '',
      validityType = ''
    } = item;
    const { rut = '', mothersLastName = '', name = '', lastName = '' } = beneficiary;
    return (
      <TableRow hover key={id || `${rut} ${index}`}>
        <TableCell>{pensionCodeId}</TableCell>
        <TableCell>{country === 'CHI' ? format(rut) : rut}</TableCell>
        <TableCell>{`${name} ${lastName} ${mothersLastName}`}</TableCell>
        <TableCell>{pensionType}</TableCell>
        <TableCell>{validityType}</TableCell>
        <TableCell>{basePension}</TableCell>
        <TableCell>{disabilityDegree}</TableCell>
        <TableCell>{resolutionNumber}</TableCell>
      </TableRow>
    );
  });
};
