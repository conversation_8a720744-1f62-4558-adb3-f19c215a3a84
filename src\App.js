import React from 'react';
import { Router } from 'react-router-dom';
import { createBrowserHistory } from 'history';
import MomentUtils from '@date-io/moment';
import { ThemeProvider } from '@material-ui/styles';
import { MuiPickersUtilsProvider } from '@material-ui/pickers';
import { renderRoutes } from 'react-router-config';
import AppProvider from './provider/app';
import theme from './theme';

import routes from './routes';
import { ScrollReset, Progress } from './components';
import './mixins/chartjs';
import './mixins/validate';
import './mixins/prismjs';
import './assets/scss/index.scss';

const history = createBrowserHistory();

const App = () => {
  return (
    <ThemeProvider theme={theme}>
      <MuiPickersUtilsProvider utils={MomentUtils}>
        <AppProvider>
          <Router history={history}>
            <ScrollReset />
            <Progress selector={store => store.progress.isInProgress} />
            {renderRoutes(routes)}
          </Router>
        </AppProvider>
      </MuiPickersUtilsProvider>
    </ThemeProvider>
  );
};

export default App;
