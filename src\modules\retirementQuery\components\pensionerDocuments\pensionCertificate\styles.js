import { Font, StyleSheet } from '@react-pdf/renderer';

Font.register({
  family: '<PERSON>',
  src: 'https://fonts.gstatic.com/s/oswald/v13/Y_TKV6o8WovbUd3m_X9aAA.ttf',
  fonts: [
    {
      src: 'https://fonts.gstatic.com/s/oswald/v36/TK3_WkUHHAIjg75cFRf3bXL8LICs1y9ogUE.ttf',
      fontWeight: 600
    }
  ]
});

const styles = StyleSheet.create({
  image: {
    height: '1.78cm',
    width: '1.78cm',
    marginTop: '0px',
    marginBottom: '10px',
    marginLeft: 'auto',
    marginRight: '10px'
  },
  pensionCodeId: {
    fontSize: 11,
    fontFamily: '<PERSON>'
  },
  page: {
    backgroundColor: 'white',
    paddingTop: 35,
    paddingBottom: 35,
    paddingHorizontal: 35
  },
  section: {
    margin: 10,
    padding: 10,
    flexGrow: 1
  },
  headerTitle: {
    marginTop: '20px',
    textAlign: 'center'
  },
  headerDate: {
    marginTop: 40,
    marginBottom: 40,
    textAlign: 'left',
    fontSize: 11,
    fontFamily: '<PERSON>'
  },
  headerBody: {
    marginRight: 20,
    marginLeft: 20,
    textAlign: 'justify',
    marginTop: 20
  },
  tableHeaderSurvival: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: 20,
    marginLeft: 20,
    marginTop: 5
  },
  tableHeaderDisability: {
    display: 'flex',
    flexDirection: 'row',
    marginRight: 20,
    marginLeft: 20,
    marginTop: 5
  },
  tableBodySurvival: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTop: '3px',
    borderBottom: '3px',
    marginRight: 20,
    marginLeft: 20
  },
  tableBodyDisability: {
    display: 'flex',
    flexDirection: 'row',
    borderTop: '3px',
    borderBottom: '3px',
    marginRight: 20,
    marginLeft: 20
  },
  tableTotals: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTop: '3px',
    borderBottom: '3px',
    marginTop: 20,
    marginRight: 20,
    marginLeft: 20
  },
  textHeader: {
    margin: 12,
    fontSize: 11,
    textAlign: 'justify',
    fontFamily: 'Oswald'
  },
  text: {
    fontSize: 11,
    fontFamily: 'Oswald',
    textAlign: 'justify',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis'
  },
  textFooter: {
    fontSize: 11,
    fontFamily: 'Oswald',
    textAlign: 'center',
    fontWeight: 600
  },
  textTable: {
    fontSize: 11,
    fontFamily: 'Oswald',
    textAlign: 'center',
    width: 100,
    borderWidth: 1
  },
  bodyContainer: {
    marginTop: '40px',
    marginBottom: 0,
    textAlign: 'justify'
  },
  signatureContainer: {
    paddingTop: 20,
    textAlign: 'center'
  },
  upperRightContainer: {
    marginLeft: 'auto',
    marginRight: '10px'
  },
  upperLeftContainer: {
    marginTop: '45px',
    marginBottom: '1px'
  },
  upperContainer: {
    display: 'flex',
    flexDirection: 'row',
    borderBottomWidth: 2
  },
  tableContainerSurvival: {
    marginTop: 20,
    marginBottom: 20,
    textAlign: 'justify'
  },
  tableContainerDisability: {
    marginTop: 20,
    marginBottom: 20,
    alignItems: 'center'
  },
  highlight: {
    fontWeight: 600
  }
});

export default styles;
