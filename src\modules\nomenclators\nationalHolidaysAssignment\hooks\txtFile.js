import { getEncoding } from '../../../../utils/encodingFile';
const INVALID_FILE_EXTENSION = 'Formato de Archivo Incorrecto';
const FILE_SIZE_IS_TOO_BIG = 'Excede el tamaño permitido de archivo';
const ERROR_FILE_NO_DATA = 'Archivo sin data';
const VALID_EXTENSIONS = ['.txt'];
const MAX_FILE_SIZE = 10485760;
const NOT_VALID_TXT_CHARS = /[^\s\n+\-*/=@#&%$!¡¿?ºª.:;,_|><´`¨""{})'^[\]~áéíóúàèìòùãẽĩõñũỹg̃äöüëïâêîôûçğşa-z0-9]/im;

const readFile = async file => {
  const encoding = await getEncoding(file);
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async () => {
      const { result } = reader;
      if (NOT_VALID_TXT_CHARS.test(result)) {
        reject(new Error('Archivo dañado'));
      }
      const splittedResult = result.split('\n');
      const rows = splittedResult.filter(row => row && row.length);
      if (!rows.length) {
        reject(new Error(ERROR_FILE_NO_DATA));
      }

      resolve(false);
    };
    reader.onerror = () => {
      return reject(new Error('Error al leer el archivo'));
    };
    reader.readAsText(file, encoding);
  });
};

const validateFile = ({ name, size }, fn = () => { /*any*/ }) => {
  let isValid = true;
  const fileExtension = name
    .split('.')
    .pop()
    .toLowerCase();

  if (VALID_EXTENSIONS.indexOf(`.${fileExtension}`) === -1) {
    fn(INVALID_FILE_EXTENSION);
    isValid = false;
  }

  if (size > MAX_FILE_SIZE) {
    fn(FILE_SIZE_IS_TOO_BIG);
    isValid = false;
  }
  return isValid;
};

export { readFile, validateFile, VALID_EXTENSIONS };
