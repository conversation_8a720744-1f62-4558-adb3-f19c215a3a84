/* eslint-disable no-magic-numbers */
import { makeStyles } from '@material-ui/styles';

const styles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(3)
  },
  plotGrid: {
    marginTop: 10
  },
  searchGrid: {
    marginTop: 0
  },
  tableGrid: {
    marginLeft: 0
  },
  cardStackedBarPlot: {
    width: '600px'
  },
  cardPlotLegend: {
    width: '300px',
    height: '200px'
  },
  componentTitle: {
    color: 'grey',
    fontWeight: 'bold',
    fontSize: '14px',
    marginRight: '10px',
    marginBottom: '-10px',
    marginTop: '10px'
  }
}));

export default styles;
