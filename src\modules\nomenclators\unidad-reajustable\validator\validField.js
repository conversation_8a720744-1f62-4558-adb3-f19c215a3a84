// Taken from https://gist.github.com/rotvulpix/69a24cc199a4253d058c
import validator from 'validator';
import numbUtils from '../../../../utils/numberOperations';
import moment from 'moment';

const formatDecimal = number => {
  const fixedNumber = number && number !== undefined ? number.replace(',', '.') : number;
  return fixedNumber
    ? `$ ${new Intl.NumberFormat(['es', 'de'], {
        maximumFractionDigits: 2
      }).format(fixedNumber)}`
    : '';
};

const isValidDecimal = val => {
  const stringValue = `${val}`;
  if (stringValue.length === 0) return true;
  if (!validator.isDecimal(stringValue, { decimal_digits: '0,2', locale: 'es-ES' })) return false;

  const fixedValue = numbUtils.roundValue(stringValue.replace(',', '.'));
  if (fixedValue < 0.01 || fixedValue > 999999.99) return false;

  return true;
};

const defaultFormatter = (value = '') =>
  value
    .replace(/^\s|[^a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûçğş0-9.',´`^¨~-\s]/gi, '')
    .replace(/\s+/g, ' ');

const IsvalidDate = fecha => {
  if (!fecha) return 'El campo es obligatorio';
  var m = moment(fecha, 'DD-MM-YYYY');
  if (m.isValid() === false) {
    return 'Fecha invalida';
  }

  return '';
};

export { defaultFormatter, isValidDecimal, formatDecimal, IsvalidDate };
