/* eslint-disable react/forbid-prop-types */
/* eslint-disable import/no-unresolved */
import React from 'react';
import PropTypes from 'prop-types';
import { TableCell, TableRow } from '@material-ui/core';
import Table from 'components/Table';

import { makeStyles } from '@material-ui/styles';

const useStyles = makeStyles(_theme => ({
  content: {
    margin: 30
  },
  acceptButton: {
    display: 'flex',
    margin: 15,
    justifyContent: 'center'
  },
  requiredFields: {
    margin: 15
  }
}));

const errorHeaders = [{ label: 'Fila' }, { label: 'Mensaje de error' }];

const renderTableHeader = () =>
  errorHeaders.map(key => (
    <TableCell key={key.label} align="left">
      {key.label}
    </TableCell>
  ));

const renderTableData = result =>
  result.map(({ row, message }) => (
    <TableRow hover key={`${row}`}>
      <TableCell align="left">{row}</TableCell>
      <TableCell align="left">{message}</TableCell>
    </TableRow>
  ));

const ResumeError = props => {
  const classes = useStyles();
  const { errors, requireFields } = props;

  return (
    <>
      <Table renderHeader={renderTableHeader} renderTable={() => renderTableData(errors)} />
      <div className={classes.requiredFields}>
        <b>* Campos obligatorios: </b>
        {requireFields}
      </div>
    </>
  );
};

ResumeError.propTypes = {
  errors: PropTypes.array.isRequired,
  requireFields: PropTypes.string.isRequired
};
ResumeError.defaultProptypes = {
  errors: [],
  requireFields: ''
};

export default ResumeError;
