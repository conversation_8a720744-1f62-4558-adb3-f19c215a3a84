/* eslint-disable import/prefer-default-export */
/* eslint-disable import/no-unresolved */
import React from 'react';
import PropTypes from 'prop-types';
import { renderRoutes } from 'react-router-config';

import SnackbarManager from '../../components/SnackbarManager';
import { Page } from '../../components';

import useSnackbar from '../../components/useSnackbar';

import PermissionsAndRoles from './permissionsAndRoles/views';
import RoleDetails from './permissionsAndRoles/views/RoleDetails';
import Users from './users/views';
import MonitorJob from './monitorJobs/views';

const Systems = ({ route }) => {
  const {
    text,
    setText,
    error,
    setError,
    successSnackbar,
    errorSnackbar,
    setSuccessSnackbar,
    setErrorSnackbar
  } = useSnackbar();

  const onSuccessSnackbar = val => {
    setText(val);
    setSuccessSnackbar(true);
    setErrorSnackbar(false);
  };

  const onErrorSnackbar = err => {
    setError(err);
    setSuccessSnackbar(false);
    setErrorSnackbar(true);
  };

  return (
    <Page title="Sistemas">
      {renderRoutes(route.routes, { onSuccessSnackbar, onErrorSnackbar })}
      <SnackbarManager
        handleRoute={false}
        error={error}
        text={text}
        successSnackbar={successSnackbar}
        errorSnackbar={errorSnackbar}
        setErrorSnackbar={setErrorSnackbar}
        setSuccessSnackbar={setSuccessSnackbar}
      />
    </Page>
  );
};

Systems.propTypes = {
  route: PropTypes.shape({
    path: PropTypes.string,
    routes: PropTypes.array,
    component: PropTypes.func
  }).isRequired
};

export { Systems, PermissionsAndRoles, RoleDetails, Users, MonitorJob };
