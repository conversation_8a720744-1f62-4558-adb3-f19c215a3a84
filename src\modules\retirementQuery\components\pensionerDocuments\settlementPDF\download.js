/* eslint-disable react/prop-types */
import React from 'react';
import { PDFDownloadLink } from '@react-pdf/renderer';
import Template from './index';

const getDocumentName = name => `${name} Certificado de Pensiones Percibidas.pdf`;

const downloadPDF = ({
  blob,
  documentName,
  setSettlementData,
  progress,
  setIsCurrentlyDownloading
}) => {
  const PDF = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.setAttribute('download', getDocumentName(documentName));
  a.href = PDF;
  a.click();

  progress.hide();
  setSettlementData(null);
  setIsCurrentlyDownloading(false);
};

const DownloadPDFSettlement = ({
  settlementData,
  setSettlementData,
  progress,
  setIsCurrentlyDownloading,
  documentName
}) => {
  return (
    <>
      {settlementData && (
        <PDFDownloadLink document={<Template data={settlementData} />} fileName="Liquidaciones.pdf">
          {({ blob, loading }) =>
            loading
              ? ''
              : downloadPDF({
                  blob,
                  documentName,
                  setSettlementData,
                  progress,
                  setIsCurrentlyDownloading
                })
          }
        </PDFDownloadLink>
      )}
    </>
  );
};
export default DownloadPDFSettlement;
