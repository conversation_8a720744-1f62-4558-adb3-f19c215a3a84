import { getPensionsAccountingReport } from '../services/pensionsAccountingServices';

import { setIsDownloading } from '../actions';

const ON_ERROR_TEXT = 'Hubo un error con la descarga del reporte';
const downloadFiles = async ({ dispatch, progress, onErrorSnackbar }) => {
  let success;

  const reportFileName = 'Reporte de Conciliación';
  progress.show();
  dispatch(setIsDownloading(true));

  await getPensionsAccountingReport().then(({ isError, data }) => {
    success = !isError;
    if (success) {
      const url = window.URL.createObjectURL(data);
      const a = document.createElement('a');
      a.setAttribute('download', `${reportFileName}.xlsx`);
      a.href = url;
      a.click();
    }
  });
  if (!success) {
    dispatch(setIsDownloading(false));
    onErrorSnackbar(`${ON_ERROR_TEXT}`);
    progress.hide();
    return false;
  }

  dispatch(setIsDownloading(false));
  progress.hide();
  return true;
};

const UseDataDownload = ({ dispatch, progress, onErrorSnackbar }) => {
  return () => downloadFiles({ dispatch, progress, onErrorSnackbar });
};

export default UseDataDownload;
