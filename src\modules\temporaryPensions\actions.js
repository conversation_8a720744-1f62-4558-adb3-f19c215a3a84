//------------------------------------------------------------------------------------------------
export const SET_TEMPORARY_PENSIONS_FILE_ERROR = 'SET_TEMPORARY_PENSIONS_FILE_ERROR';
export const setFileError = error => {
  return {
    type: SET_TEMPORARY_PENSIONS_FILE_ERROR,
    data: { fileError: error }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_TEMPORARY_FILEDATA_ERROR = 'SET_TEMPORARY_FILEDATA_ERROR';
export const temporaryFileDataError = ({ warnings, errors }) => {
  return {
    type: SET_TEMPORARY_FILEDATA_ERROR,
    data: { errors, warnings, isError: errors.length > 0 }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_TEMPORARY_PENSIONS = 'GET_TEMPORARY_PENSIONS';
export const temporaryPensions = results => {
  return {
    type: SET_TEMPORARY_PENSIONS,
    data: { temporaryPensions: results || [] }
  };
};
//------------------------------------------------------------------------------------------------
export const CLEAN_TEMPORARY_FILEDATA_ERROR = 'CLEAN_TEMPORARY_FILEDATA_ERROR';
export const cleanTemporaryPensionsErrors = () => {
  return {
    type: CLEAN_TEMPORARY_FILEDATA_ERROR
  };
};
//------------------------------------------------------------------------------------------------
export const CLEAN_TEMPORARY_PENSIONS_FILE_ERROR = 'CLEAN_TEMPORARY_PENSIONS_FILE_ERROR';
export const cleanFileError = () => {
  return {
    type: CLEAN_TEMPORARY_PENSIONS_FILE_ERROR
  };
};
//------------------------------------------------------------------------------------------------
export const SET_HAS_SCIENTIFIC_NOTATION_ERROR = 'SET_HAS_SCIENTIFIC_NOTATION_ERROR';
export const setHasScientificNotationError = bool => {
  return {
    type: SET_HAS_SCIENTIFIC_NOTATION_ERROR,
    data: bool
  };
};
//------------------------------------------------------------------------------------------------
export const SET_TEMPORARY_PENSIONS_FILE_INFO = 'SET_TEMPORARY_PENSIONS_FILE_INFO';
export const setFileInfo = file => {
  return {
    type: SET_TEMPORARY_PENSIONS_FILE_INFO,
    data: { fileName: file.name }
  };
};
//------------------------------------------------------------------------------------------------
export const CLEAN_TEMPORARY_PENSIONS_FILE = 'CLEAN_TEMPORARY_PENSIONS_FILE';
export const cleanFile = () => {
  return {
    type: CLEAN_TEMPORARY_PENSIONS_FILE
  };
};
//----
