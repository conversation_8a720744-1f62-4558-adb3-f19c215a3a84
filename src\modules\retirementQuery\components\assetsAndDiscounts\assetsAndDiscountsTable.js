/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Grid, Button } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import { useSelector, useDispatch } from 'react-redux';

import { useProgress } from 'components';
import AlertInfo from './alertAssetsAndDiscounts';
import Discounts from './discounts';
import Assets from './assets';
import useStyles from '../pensionerDetail/style';

import { updatePension } from '../../../pensions/services/pensioner.service';
import { isReadyToUpdate } from '../../services/queryPensions.service';
import {
  formatMoney,
  formatForUpdateAmount
} from '../../../currentCapitalReports/hooks/formatters';
import { unformatRut } from '../../../homeView/utils/formatters';

import {
  setDataSuccessfullyUpdated,
  setModifiedFieldErrors,
  setForFamilyAssignment,
  setArticle40,
  setHealthLoan
} from '../../actions';
import { checkWritePermission } from '../../../../utils/checkUserPermission';

const workerIpsAlertText =
  'La modificación de haberes y descuentos se podrá realizar al finalizar la carga masiva de cajas de compensación';
const hasBusinessDayPassedAlertText =
  'La modificación de haberes y descuentos se puede realizar hasta el día hábil 14 del mes';

const setAlertText = ({ bulkLoadIpsWorkerExecuted }) => {
  if (!bulkLoadIpsWorkerExecuted) return workerIpsAlertText;

  return hasBusinessDayPassedAlertText;
};

const flatAmountsToPensionModel = ({ forFamilyAssignment, article40, healthLoan }) => {
  return {
    assets: { forFamilyAssignment },
    article40,
    discounts: { healthLoan }
  };
};

const rutSanitizer = data => {
  return {
    ...data,
    causantRut: unformatRut(data.causantRut),
    beneficiaryRut: unformatRut(data.beneficiaryRut)
  };
};

const formatMoneyToRender = ({ article40, forFamilyAssignment, healthLoan }) => {
  return {
    article40: `${formatMoney(article40)}`,
    forFamilyAssignment: `${formatMoney(forFamilyAssignment)}`,
    healthLoan: `${formatMoney(healthLoan)}`
  };
};

const AssetsAndDiscountsTable = ({
  data,
  setData,
  onErrorSnackbar,
  onSuccessSnackbar,
  userRole
}) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();
  const hasWritePermission = checkWritePermission(userRole);

  const [isEditable, setIsEditable] = useState(false);
  const [temporaryChanges, setTemporaryChanges] = useState({});
  const [isValidPeriod, setIsValidPeriod] = useState({
    bulkLoadIpsWorkerExecuted: false,
    hasBusinessDayPassed: false,
    unifiedAssetsAndDiscountsExecuted: false
  });

  const dataSuccessfullyUpdated = useSelector(store => store.queryPensions.dataSuccessfullyUpdated);
  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);
  const forFamilyAssignment = useSelector(store => store.queryPensions.forFamilyAssignment);
  const article40 = useSelector(store => store.queryPensions.article40);
  const healthLoan = useSelector(store => store.queryPensions.healthLoan);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const changeEditable = () => {
    setIsEditable(true);
  };

  const isEnabledEdit = () => {
    return (
      isValidPeriod.bulkLoadIpsWorkerExecuted &&
      !isValidPeriod.unifiedAssetsAndDiscountsExecuted &&
      !isValidPeriod.hasBusinessDayPassed
    );
  };
  const cancelEdit = async () => {
    await Promise.all([dispatch(setModifiedFieldErrors({}))]);
    setIsEditable(false);
    setInitialData();
  };

  const setInitialData = () => {
    dispatch(setForFamilyAssignment(formatForUpdateAmount(data?.forFamilyAssignment)));
    dispatch(setArticle40(formatForUpdateAmount(data?.article40)));
    dispatch(setHealthLoan(formatForUpdateAmount(data?.healthLoan)));
  };

  const disableSave = () => {
    if (Object.keys(modifiedFieldErrors).some(key => modifiedFieldErrors[key])) return true;
    return false;
  };

  useEffect(() => {
    async function fetchData() {
      const { data: result } = await isReadyToUpdate();
      setIsValidPeriod(result);
    }
    fetchData();
    setInitialData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setTemporaryChanges({
      forFamilyAssignment,
      article40,
      healthLoan
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [forFamilyAssignment, article40, healthLoan]);

  useEffect(() => {
    if (dataSuccessfullyUpdated) {
      setData({ ...data, ...formatMoneyToRender(temporaryChanges) });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSuccessfullyUpdated]);

  const save = async () => {
    progress.show();
    dispatch(setDataSuccessfullyUpdated(false));
    setIsEditable(false);

    const dataToUpdate = flatAmountsToPensionModel(temporaryChanges);
    const sanitizedDataRUTs = rutSanitizer(data);

    const complete = await updatePension(sanitizedDataRUTs, dataToUpdate);

    if (complete) {
      setIsEditable(false);
      progress.hide();
      dispatch(setDataSuccessfullyUpdated(true));
      return onSuccessSnackbar('Los cambios realizados se guardaron correctamente');
    }

    setIsEditable(true);
    progress.hide();
    dispatch(setDataSuccessfullyUpdated(false));
    return onErrorSnackbar('Los cambios realizados no se guardaron correctamente');
  };

  return (
    <div className={classes.containerActionButton}>
      <Grid container className={classes.actionButtons}>
        <Grid item>
          <Button
            className={classes.editSave}
            variant="contained"
            onClick={cancelEdit}
            size="small"
            color="primary"
            disabled={!hasWritePermission || !onlineStatus || !isEditable}
          >
            Cancelar
          </Button>
        </Grid>
        <Grid item>
          <Button
            className={classes.editSave}
            variant="contained"
            onClick={changeEditable}
            size="small"
            color="primary"
            disabled={!hasWritePermission || !onlineStatus || isEditable || !isEnabledEdit()}
          >
            Editar
          </Button>
        </Grid>
        <Grid item>
          <Button
            disabled={!hasWritePermission || !isEditable || disableSave()}
            className={classes.editSave}
            onClick={save}
            variant="contained"
            size="small"
            color="primary"
          >
            Guardar
          </Button>
        </Grid>
      </Grid>
      <Grid className={classes.grid}>
        <AlertInfo
          alertClass={classes.alertBar}
          severity="error"
          color="info"
          alertText={setAlertText(isValidPeriod)}
        />
      </Grid>
      <Grid container spacing={2}>
        <Grid item className={classes.gridStyle}>
          <Assets values={data} editable={isEditable} />
        </Grid>
        <Grid item className={classes.gridStyle}>
          <Discounts values={data} editable={isEditable} />
        </Grid>
      </Grid>
    </div>
  );
};

AssetsAndDiscountsTable.propTypes = {
  data: PropTypes.shape({}).isRequired,
  onErrorSnackbar: PropTypes.shape({}).isRequired,
  onSuccessSnackbar: PropTypes.shape({}).isRequired
};

export default AssetsAndDiscountsTable;
