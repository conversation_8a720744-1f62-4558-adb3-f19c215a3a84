/* eslint-disable import/prefer-default-export */
/* eslint-disable no-console */

import moment from 'moment';
import { axiosRequest, pdfRequest, fileRequest } from '../../../services/axiosRequest';
import { setDependencyExecuted, setCurrentDate } from '../actions';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

const errorHandler = err => {
  console.error(err);
  return {
    data: {},
    isError: true
  };
};

//------------------------------------------------------------------------------------------------
export const queryPensions = async queryValue => {
  const { data, isError = false } = await axiosRequest
    .post(`${api}/queryPensions/`, { query: queryValue })
    .catch(err => errorHandler(err));
  return { data, isError };
};
//------------------------------------------------------------------------------------------------
export const getSpecificPensioner = async ({ rutBeneficiary, rutCausant, pensionCodeId }) => {
  const { data, isError = false } = await axiosRequest
    .get(
      `${api}/queryPensions/fetch-pensioner-data?rutBeneficiary=${rutBeneficiary}&rutCausant=${rutCausant}&pensionCodeId=${pensionCodeId}`
    )
    .catch(err => errorHandler(err));

  return { data, isError };
};
//------------------------------------------------------------------------------------------------
export const getSpecificPensionerTemmporalTable = async ({
  rutBeneficiary,
  rutCausant,
  pensionCodeId
}) => {
  const { data = {}, isError = false } = await axiosRequest
    .get(
      `${api}/queryPensions/fetch-pensioner-data-temporal-table?rutBeneficiary=${rutBeneficiary}&rutCausant=${rutCausant}&pensionCodeId=${pensionCodeId}`
    )
    .catch(err => errorHandler(err));
  return { data, isError };
};
//------------------------------------------------------------------------------------------------
export const getSpecificPensionerTemporalPensionType = async ({
  rutBeneficiary,
  rutCausant,
  pensionCodeId
}) => {
  const { data = {}, isError = false } = await axiosRequest
    .get(
      `${api}/queryPensions/fetch-temporal-pension-type?rutBeneficiary=${rutBeneficiary}&rutCausant=${rutCausant}&pensionCodeId=${pensionCodeId}`
    )
    .catch(err => errorHandler(err));
  return { data, isError };
};
//------------------------------------------------------------------------------------------------
export const uploadPdfFile = async ({ pdfFile, beneficiaryRut, causantRut, pensionCodeId }) => {
  const formData = new FormData();
  formData.append(
    'pensionerDocument',
    pdfFile,
    `${beneficiaryRut} ${causantRut} ${pensionCodeId} ${pdfFile.name}`
  );
  const { isError = false } = await pdfRequest
    .post(`${api}/queryPensions/store-pensioner-files`, formData)
    .catch(() => {
      return { isError: true };
    });
  return { isError };
};
//------------------------------------------------------------------------------------------------
export const checkPensionerDocuments = async (
  { beneficiaryRut, causantRut, pensionCodeId, nameAndRegex },
  setAvailableFiles
) => {
  try {
    const {
      data: { fileExistenceDetails = nameAndRegex }
    } = await axiosRequest
      .post(`${api}/queryPensions/check-pensioner-files`, {
        beneficiaryRut,
        causantRut,
        pensionCodeId,
        nameAndRegex
      })
      .catch(_err => {
        setAvailableFiles(nameAndRegex);
      });
    setAvailableFiles(fileExistenceDetails);
  } catch {
    setAvailableFiles(nameAndRegex);
  }
};
//------------------------------------------------------------------------------------------------
export const getPensionerDocuments = async ({
  beneficiaryRut,
  causantRut,
  pensionCodeId,
  documentName
}) => {
  return fileRequest
    .get(
      `${api}/queryPensions/get-pensioner-files/${beneficiaryRut} ${causantRut} ${pensionCodeId} ${documentName}`
    )
    .then(response => {
      // handle success
      const data = response.data;
      const isError = false;
      const fileName = response.headers['namefile'];
      return { data, isError, fileName };
    })
    .catch(() => {
      return { data: [], isError: true };
    });
};
//------------------------------------------------------------------------------------------------
export const updatePensionerAssetsAndDiscounts = async discountsAndAssets => {
  const { data, error = null } = await axiosRequest
    .post(`${api}/discountsAndAssets/update`, discountsAndAssets)
    .catch(err => errorHandler(err));
  return { data, error };
};
//------------------------------------------------------------------------------------------------
export const updatePensionerDataToPensionModel = async ({
  beneficiaryRut,
  causantRut,
  pensionCodeId,
  ...pensionerInfo
}) => {
  if (pensionerInfo.basePension) {
    const baseAux = pensionerInfo.basePension.replace(',', '.');
    pensionerInfo.basePension = parseFloat(baseAux);
  }

  const { data, isError = false } = await axiosRequest
    .post(`${api}/queryPensions/update-pensioner`, {
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      pensionerInfo
    })
    .catch(err => errorHandler(err));
  return { data, isError };
};
//------------------------------------------------------------------------------------------------
export const updatePensionerDataToTemporalModel = async ({
  beneficiaryRut,
  causantRut,
  pensionCodeId,
  ...pensionerInfo
}) => {
  if (pensionerInfo.basePension) {
    let baseAux = pensionerInfo.basePension.replace(',', '.');
    pensionerInfo.basePension = parseFloat(baseAux);
  }

  const { data, isError = false } = await axiosRequest
    .post(`${api}/queryPensions/update-pensioner-temporally`, {
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      pensionerInfo
    })
    .catch(err => errorHandler(err));
  return { data, isError };
};
//------------------------------------------------------------------------------------------------
export const updatePensionTypeToTemporalModel = async (
  { beneficiaryRut, causantRut, pensionCodeId },
  pensionType,
  ChangeOfPensionTypeDueToCharges
) => {
  const { data, isError = false } = await axiosRequest
    .post(`${api}/queryPensions/update-temporal-pension-type`, {
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      pensionType,
      ChangeOfPensionTypeDueToCharges
    })
    .catch(err => errorHandler(err));
  return { data, isError };
};
//------------------------------------------------------------------------------------------------
export const businessDaysToUpdateTemporally = async (
  setUpdateTemporally = () => {
    /* any */
  },
  setUpdatePensionTypeToTemporary = () => {
    /* any */
  }
) => {
  const {
    data: { updateTemporally, updatePensionTypeToTemporally }
  } = await axiosRequest
    .get(`${api}/getBusinessDays/update-pensioner-temporally-and-pension-type`)
    .catch(() => {
      setUpdateTemporally(true);
      return { data: { updateTemporally: true, updatePensionTypeToTemporally: true } };
    });
  setUpdateTemporally(updateTemporally);
  setUpdatePensionTypeToTemporary(updatePensionTypeToTemporally);
  return { updateTemporally, updatePensionTypeToTemporally };
};
//-----------------------------------------------------------------------------------------------------------
export const loadAFPList = async (dispatch, setAFPList) => {
  const {
    data: { result = [{ name: '' }] }
  } = await axiosRequest.get(`${api}/nomenclators/afp`).catch(err => {
    console.error(err);
    return { data: {} };
  });
  const afpList = result.map(({ name }) => name);
  dispatch(setAFPList(afpList));
  return afpList;
};
//-----------------------------------------------------------------------------------------------------------
export const loadBankBranchOffices = async (dispatch, setBankBranchOffices) => {
  const {
    data: { result = [{ name: '' }] }
  } = await axiosRequest.get(`${api}/nomenclators/bank`).catch(err => {
    console.error(err);
    return { data: {} };
  });
  const banks = result.map(({ name }) => name);
  dispatch(setBankBranchOffices(banks));
  return banks;
};
//-----------------------------------------------------------------------------------------------------------
export const loadServipagBranchOffices = async (dispatch, setServipagBranchOffice) => {
  const { data = [{ name: '' }] } = await axiosRequest
    .get(`${api}/nomenclators/servipag`)
    .catch(err => {
      console.error(err);
      return {};
    });
  const servipags = data.map(({ name }) => name);
  dispatch(setServipagBranchOffice(servipags));
  return servipags;
};
//------------------------------------------------------------------------------------------------
export const isReadyToUpdate = async () => {
  const { data, error = null } = await axiosRequest
    .get(`${api}/discountsAndAssets/isReadyToUpdate`)
    .catch(() => {
      return {
        data: {
          result: {
            bulkLoadIpsWorkerExecuted: false,
            hasBusinessDayPassed: false,
            unifiedAssetsAndDiscountsExecuted: false
          }
        },
        error
      };
    });
  return { data: data.result, error };
};
//------------------------------------------------------------------------------------------------
export const isReadyToUpdateNonFormulable = async () => {
  const { data, error = null } = await axiosRequest
    .get(`${api}/discountsAndAssets/isReadyToUpdateNonFormulable`)
    .catch(() => {
      return {
        data: {
          result: {
            postLiquidationCheckpointReportWorkerExecuted: false,
            unifiedUnifiedAndGenerateBankUploadExecuted: false,
            hasBusinessDayPassed: false
          }
        },
        error
      };
    });
  return { data: data.result, error };
};
//------------------------------------------------------------------------------------------------
export const generateAndUploadBankFileExecuted = () => async dispatch => {
  const { data } = await axiosRequest
    .get(`${api}/verifycronexecution/GENERATE_AND_UPLOAD_BANK_FILE`)
    .catch(err => {
      console.error(err);
      dispatch(setDependencyExecuted(false));
    });

  dispatch(setDependencyExecuted(data.result.alreadyExecuted));
};

//------------------------------------------------------------------------------------------------
export const checkCronExecution = cronName => async setIsCronExecuted => {
  const {
    data: {
      result: { alreadyExecuted }
    }
  } = await axiosRequest
    .get(`${api}/verifycronexecution/${cronName}`)

    .catch(err => {
      console.error(err);
      return { data: { result: { alreadyExecuted: false, error: '' } } };
    });
  setIsCronExecuted(alreadyExecuted);
};
//------------------------------------------------------------------------------------------------
export const getCurrentDate = async dispatch => {
  const { data } = await axiosRequest.get(`${api}/getBusinessDays/currentDate`).catch(err => {
    console.error(err);
    return { data: '' };
  });

  const date = data?.currentDate;
  const currentDate = date ? moment(date, 'DD-MM-YYYY').toDate() : {};
  dispatch(setCurrentDate(currentDate));
};
//------------------------------------------------------------------------------------------------
export const getHistoricalSettlementsForPDFFile = async ({
  beneficiaryRut,
  causantRut,
  pensionCodeId,
  lowerDate,
  upperDate
}) => {
  const { data, isError } = await axiosRequest
    .post(`${api}/queryPensions/get-historical-settlements`, {
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      lowerDate,
      upperDate
    })
    .catch(err => {
      console.error(err);
      return { isError: true, data: { historicalSettlements: [], pecBoss: '' } };
    });
  return {
    data,
    isError
  };
};
//------------------------------------------------------------------------------------------------
export const getPensionCertificateData = async ({ beneficiaryRut, causantRut, _pensionCodeId }) => {
  const {
    data,
    error
  } = await axiosRequest
    .post(`${api}/queryPensions/get-pension-certificate-data`, { beneficiaryRut, causantRut })
    .catch(err => {
      console.error(err);
      return { error: err, data: { pecBoss: '' } };
    });

  return { data, error };
};
