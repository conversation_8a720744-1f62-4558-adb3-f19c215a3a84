import PropTypes from 'prop-types';
import React from 'react';
import TextField from '@material-ui/core/TextField';

const selectOptions = [
  {
    value: '',
    label: 'Se<PERSON><PERSON><PERSON>'
  },
  {
    value: 'Ha<PERSON>',
    label: '<PERSON><PERSON>'
  },
  {
    value: 'Descuento',
    label: 'Descuento'
  }
];
const SelectTextField = ({ name, handler, displayName, value, onChange, inputprops, ...props }) => {
  return (
    <>
      <TextField
        id="outlined-select-native"
        select
        value={value || ''}
        InputLabelProps
        onChange={onChange}
        SelectProps={{
          native: true
        }}
        placeholder="Seleccione"
        variant="outlined"
        margin="dense"
      >
        {selectOptions.map(opt => (
          <option key={opt.value} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </TextField>
    </>
  );
};
SelectTextField.propTypes = {
  name: PropTypes.string.isRequired,
  handler: PropTypes.func,
  displayName: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  type: PropTypes.oneOf(['text', 'number', 'email', 'date']),
  isValidListener: PropTypes.func,
  validators: PropTypes.arrayOf(PropTypes.string),
  errorMessages: PropTypes.arrayOf(PropTypes.string),
  inputprops: PropTypes.objectOf(PropTypes.string).isRequired
};
SelectTextField.defaultProps = {
  type: 'text',
  handler: () => {
    /* any */
  },
  isValidListener: () => {
    /* any */
  },
  validators: ['required', 'match'],
  errorMessages: ['El valor es requerido', 'El valor es inválido']
};

export default SelectTextField;
