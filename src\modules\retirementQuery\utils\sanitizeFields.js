import moment from 'moment';

const dateFormatter = date => {
  if (!date) return undefined;
  return moment(date, 'DD-MM-YYYY').toString();
};

const dateFields = ['accidentDate', 'endDateOfValidity', 'endDateOfTheoricalValidity'];

const sanitizeFields = (data = {}) => {
  const fields = Object.keys(data);
  const sanitizedFields = fields.reduce((obj, key) => {
    if (dateFields.includes(key)) return { ...obj, [key]: dateFormatter(data[key]) };
    if (!data[key]) return { ...obj, [key]: data[key] };
    return { ...obj, [key]: `${data[key]}`.trim() };
  }, data);
  return sanitizedFields;
};
export { sanitizeFields, dateFormatter };
