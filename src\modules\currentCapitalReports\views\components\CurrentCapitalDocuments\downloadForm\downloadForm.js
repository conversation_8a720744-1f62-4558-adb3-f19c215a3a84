/* eslint-disable react/prop-types */
// add propTypes later
import React from 'react';
import { Button, Grid, Tooltip, TextField } from '@material-ui/core';
import CloudDownloadIcon from '@material-ui/icons/CloudDownload';

import useOnlineStatus from '@rehooks/online-status';
import useStyles from './styles';

const DownloadForm = ({ label, value, helperText, errors, tooltipTitle, onClick, disabled }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();

  return (
    <>
      <Grid container spacing={2}>
        <Grid item>
          <TextField
            label={label}
            className={classes.textfield}
            value={value}
            helperText={helperText}
            error={errors}
            disabled
            variant="outlined"
            size="small"
          />
        </Grid>
        <Grid item>
          <Tooltip title={tooltipTitle}>
            <span className={classes.spanFinalize}>
              <Button
                variant="contained"
                className={classes.importDownloadButton}
                color="primary"
                disabled={disabled || !onlineStatus}
                onClick={onClick}
                size="medium"
              >
                <CloudDownloadIcon />
              </Button>
            </span>
          </Tooltip>
        </Grid>
      </Grid>
    </>
  );
};

export default DownloadForm;
