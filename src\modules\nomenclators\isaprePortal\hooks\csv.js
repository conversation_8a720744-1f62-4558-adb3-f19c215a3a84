/* eslint-disable import/no-unresolved */
import * as XLSX from 'xlsx';

const INVALID_FILE_EXTENSION = 'Formato de archivo incorrecto';
const FILE_SIZE_IS_TOO_BIG = 'Excede el tamaño permitido';
const VALID_EXTENSIONS = ['.xlsx', '.xls'];
const MAX_FILE_SIZE = 10485760;

const readFile = async (file, createJSON) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = _evt => {
      const { result } = reader;
      const wb = XLSX.read(result, { type: 'binary' });
      const wsname = wb.SheetNames[0];
      const ws = wb.Sheets[wsname];
      const data = XLSX.utils.sheet_to_json(ws, { header: 1 });
      data.shift();

      const rows = data.filter(row => row && row.length);
      if (!rows || !rows.length) {
        reject(new Error('Archivo vacío.'));
      }

      resolve(rows.map(row => createJSON(row)));
    };
    reader.onerror = () => {
      return reject(new Error('Error al leer el archivo.'));
    };

    reader.readAsBinaryString(file);
  });
};

const validateFile = (
  { name, size },
  fn = () => {
    /* any */
  }
) => {
  let isValid = true;
  const fileExtension = name
    .split('.')
    .pop()
    .toLowerCase();

  if (VALID_EXTENSIONS.indexOf(`.${fileExtension}`) === -1) {
    fn(INVALID_FILE_EXTENSION);
    isValid = false;
  }

  if (size > MAX_FILE_SIZE) {
    fn(FILE_SIZE_IS_TOO_BIG);
    isValid = false;
  }
  return isValid;
};

export { readFile, validateFile, VALID_EXTENSIONS };
