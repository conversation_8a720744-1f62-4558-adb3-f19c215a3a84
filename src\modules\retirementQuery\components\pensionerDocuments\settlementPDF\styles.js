import { Font, StyleSheet } from '@react-pdf/renderer';

Font.register({
  family: '<PERSON>',
  src: 'https://fonts.gstatic.com/s/oswald/v13/Y_TKV6o8WovbUd3m_X9aAA.ttf'
});

const styles = StyleSheet.create({
  image: {
    height: '1.78cm',
    width: '1.78cm',
    marginTop: 60,
    marginLeft: 'auto',
    marginRight: '10px'
  },
  pensionCodeId: {
    fontSize: 11,
    fontFamily: '<PERSON>'
  },
  page: {
    backgroundColor: 'white',
    paddingTop: 35,
    paddingBottom: 35,
    paddingHorizontal: 35
  },
  section: {
    margin: 10,
    padding: 10,
    flexGrow: 1
  },
  headerTitle: {
    marginTop: '20px',
    textAlign: 'center'
  },
  headerDate: {
    marginTop: 30,
    marginBottom: 30,
    textAlign: 'right',
    fontSize: 11,
    fontFamily: '<PERSON>'
  },
  headerBody: {
    marginRight: 20,
    marginLeft: 20,
    textAlign: 'justify'
  },
  tableHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTop: '3px',
    borderBottom: '3px',
    marginRight: 20,
    marginLeft: 20,
    marginTop: 5,
    marginBottom: 5,
    borderTopWidth: 1,
    borderBottomWidth: 1
  },
  tableBody: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTop: '3px',
    borderBottom: '3px',
    marginRight: 20,
    marginLeft: 20
  },
  tableTotals: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTop: '3px',
    borderBottom: '3px',
    marginTop: 20,
    marginRight: 20,
    marginLeft: 20
  },
  textHeader: {
    margin: 12,
    fontSize: 11,
    textAlign: 'justify',
    fontFamily: 'Oswald'
  },
  text: {
    fontSize: 11,
    fontFamily: 'Oswald'
  },
  textFooter: {
    fontSize: 11,
    fontFamily: 'Oswald',
    textAlign: 'right'
  },
  textTable: {
    fontSize: 11,
    fontFamily: 'Oswald',
    justifyContent: 'center',
    width: 100
  },
  lowerBodyContainer: {
    marginTop: 0,
    marginBottom: 0,
    textAlign: 'justify'
  },
  signatureContainer: {
    paddingTop: 20
  },
  upperContainer: {
    display: 'flex',
    flexDirection: 'row'
  },
  upperRightContainer: {
    marginLeft: 'auto',
    marginRight: '10px'
  },
  tableContainer: {
    marginTop: 20,
    marginBottom: 20,
    textAlign: 'justify'
  }
});

export default styles;
