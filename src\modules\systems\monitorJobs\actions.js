//------------------------------------------------------------------------------------------------
export const SKIP_MON_JOB = 'SKIP_MON_JOB';
export const skipMonitorJob = (prevJob) => {
  return {
    type: SKIP_MON_JOB,
    data: {  prevJob }
  };
};
//------------------------------------------------------------------------------------------------
export const EXEC_MON_JOB = 'EXEC_MON_JOB';
export const executeMonitorJob = (prevJob, newJob) => {
  return {
    type: EXEC_MON_JOB,
    data: { prevJob, newJob }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_MON_JOB = 'SET_MON_JOB';
export const loadMonitorJob = monitor => {
  return {
    type: SET_MON_JOB,
    data: { monitor }
  };
};