import {
  setCleanJson,
  setDisableFileUpload,
  setFilenameCleanErrors,
  setFileCleanErrors,
  setFileUpload,
  setWasFonasaProcessExecuted,
  isDataUploading
} from '../actions';
import { insertFonasaDiscount } from '../services/fonasaDiscounts.service';

const ON_SUCCESS_TEXT = 'Importación Exitosa';
const ON_ERROR_TEXT = 'Hubo un error al importar el archivo de';

const uploadSuccess = async discountData => {
  const { data, isError } = await insertFonasaDiscount(discountData);
  return !!data && !isError;
};

const uploadFiles = async (dispatch, progress, onSuccess, onError, data) => {
  progress.show();
  dispatch(isDataUploading(true));
  dispatch(setDisableFileUpload());
  if (!(await uploadSuccess(data))) {
    onError(`${ON_ERROR_TEXT} descuentos`);
    progress.hide();
    dispatch(setFileUpload());
    dispatch(isDataUploading(false));
    return false;
  }
  onSuccess(ON_SUCCESS_TEXT);
  progress.hide();
  dispatch(setCleanJson());
  dispatch(setFileCleanErrors());
  dispatch(setFilenameCleanErrors());
  dispatch(setWasFonasaProcessExecuted(true));
  dispatch(isDataUploading(false));
  return true;
};

const useDataUpload = (dispatch, progress, onSuccess, onError, data) => {
  return () => {
    if (data && data.length > 0) {
      uploadFiles(dispatch, progress, onSuccess, onError, data);
    }
  };
};

export default useDataUpload;
