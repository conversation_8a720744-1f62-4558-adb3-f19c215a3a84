//------------------------------------------------------------------------------------------------
export const SET_FAMILY_ASSIGNMENT_FILE_ERROR = 'SET_FAMILY_ASSIGNMENT_FILE_ERROR';
export const setFileError = error => {
  return {
    type: SET_FAMILY_ASSIGNMENT_FILE_ERROR,
    data: { fileError: error }
  };
};
//------------------------------------------------------------------------------------------------
export const CLEAN_FAMILY_ASSIGNMENT_FILE_ERROR = 'CLEAN_FAMILY_ASSIGNMENT_FILE_ERROR';
export const cleanFileError = () => {
  return {
    type: CLEAN_FAMILY_ASSIGNMENT_FILE_ERROR
  };
};
//------------------------------------------------------------------------------------------------
export const SET_FAMILY_ASSIGNMENT_RESUME_ERROR = 'SET_FAMILY_ASSIGNMENT_RESUME_ERROR';
export const updateFileDataError = ({ warnings, errors }) => {
  return {
    type: SET_FAMILY_ASSIGNMENT_RESUME_ERROR,
    data: { errors, warnings, isError: errors.length > 0 }
  };
};
//------------------------------------------------------------------------------------------------
export const CLEAN_FAMILY_ASSIGNMENT_RESUME_ERROR = 'CLEAN_FAMILY_ASSIGNMENT_RESUME_ERROR';
export const cleanFamilyAssignmentResumeErrors = _results => {
  return {
    type: CLEAN_FAMILY_ASSIGNMENT_RESUME_ERROR
  };
};
//------------------------------------------------------------------------------------------------
export const ENABLED_REACTIVATE = 'ENABLED_REACTIVATE';
export const enableReactivate = isEnabled => {
  return {
    type: ENABLED_REACTIVATE,
    data: { isReactivateEnable: isEnabled }
  };
};
//------------------------------------------------------------------------------------------------
export const ENABLED_REACTIVATE_IMPORT = 'ENABLED_REACTIVATE_IMPORT';
export const enableReactivateImport = (isEnabled, completed) => {
  return {
    type: ENABLED_REACTIVATE_IMPORT,
    data: { isEnableReactivateImport: isEnabled && completed }
  };
};
//------------------------------------------------------------------------------------------------
export const REACTIVATION_CRON_PROCESSED = 'REACTIVATION_CRON_PROCESSED';
export const setWasReactivationCronExecuted = wasExecuted => {
  return {
    type: REACTIVATION_CRON_PROCESSED,
    data: wasExecuted
  };
};
//------------------------------------------------------------------------------------------------
export const WAS_INACT_REACT_ALREADY_EXECUTED = 'WAS_INACT_REACT_ALREADY_EXECUTED';
export const setWasInacReactAlreadyExecuted = wasExecuted => {
  return {
    type: WAS_INACT_REACT_ALREADY_EXECUTED,
    data: wasExecuted
  };
};
//------------------------------------------------------------------------------------------------
export const INACT_REACT_PROCESS_AVAILABLE = 'INACT_REACT_PROCESS_AVAILABLE';
export const isInactReactAvailable = isAvailable => {
  return {
    type: INACT_REACT_PROCESS_AVAILABLE,
    data: isAvailable
  };
};
//------------------------------------------------------------------------------------------------
export const DAYS_TO_EXECUTE_INACT_REACT = 'DAYS_TO_EXECUTE_INACT_REACT';
export const numberOfDaysToExecute = isInRange => {
  return {
    type: DAYS_TO_EXECUTE_INACT_REACT,
    data: isInRange
  };
};
//------------------------------------------------------------------------------------------------
export const WAS_API_CALLED = 'WAS_API_CALLED';
export const apiCalled = wasCalled => {
  return {
    type: WAS_API_CALLED,
    data: wasCalled
  };
};

export const SET_ORPHANHOOD_FILE_ERROR = 'SET_ORPHANHOOD_FILE_ERROR';
export const setOrphanhoodFileError = error => {
  return {
    type: SET_ORPHANHOOD_FILE_ERROR,
    data: { fileError: error }
  };
};

export const CLEAN_ORPHANHOOD_FILE_ERROR = 'CLEAN_ORPHANHOOD_FILE_ERROR';
export const cleanOrphanhoodFileError = () => {
  return {
    type: CLEAN_ORPHANHOOD_FILE_ERROR
  };
};

export const CLEAN_ORPHANHOOD_RESUME_ERROR = 'CLEAN_ORPHANHOOD_RESUME_ERROR';
export const cleanOrphanhoodResumeErrors = _results => {
  return {
    type: CLEAN_ORPHANHOOD_RESUME_ERROR
  };
};

export const ENABLED_ORPHANHOOD_IMPORT = 'ENABLED_ORPHANHOOD_IMPORT';
export const enableOrphanhoodImport = (isEnabled, completed) => {
  return {
    type: ENABLED_ORPHANHOOD_IMPORT,
    data: { isEnableOrphanhoodImport: isEnabled && completed }
  };
};

export const ENABLED_ORPHANHOOD = 'ENABLED_ORPHANHOOD';
export const enableOrphanhood = isEnabled => {
  return {
    type: ENABLED_ORPHANHOOD,
    data: { isOrphanhoodEnable: isEnabled }
  };
};

export const ORPHANHOOD_CRON_PROCESSED = 'ORPHANHOOD_CRON_PROCESSED';
export const setWasOrphanhoodCronExecuted = wasExecuted => {
  return {
    type: ORPHANHOOD_CRON_PROCESSED,
    data: wasExecuted
  };
};

export const WAS_ORPHANHOOD_ALREADY_EXECUTED = 'WAS_ORPHANHOOD_ALREADY_EXECUTED';
export const setWasOrphanhoodAlreadyExecuted = wasExecuted => {
  return {
    type: WAS_ORPHANHOOD_ALREADY_EXECUTED,
    data: wasExecuted
  };
};

export const ORPHANHOOD_PROCESS_AVAILABLE = 'ORPHANHOOD_PROCESS_AVAILABLE';
export const isOrphanhoodAvailable = isAvailable => {
  return {
    type: ORPHANHOOD_PROCESS_AVAILABLE,
    data: isAvailable
  };
};

//------------------------------------------------------------------------------------------------
export const DAYS_TO_EXECUTE_ORPHANHOOD = 'DAYS_TO_EXECUTE_ORPHANHOOD';
export const numberOfDaysToExecuteOrphanhood = isInRange => {
  return {
    type: DAYS_TO_EXECUTE_ORPHANHOOD,
    data: isInRange
  };
};

export const ORPHANHOOD_REACTIVATE = 'ORPHANHOOD_REACTIVATE';
export const orphanhoodReactivate = isEnabled => {
  return {
    type: ORPHANHOOD_REACTIVATE,
    data: { isReactivateEnable: isEnabled }
  };
};

//------------------------------------------------------------------------------------------------
export const SET_HORPHANHOOD_RESUME_ERROR = 'SET_HORPHANHOOD_RESUME_ERROR';
export const updateOrphanhoodFileDataError = ({ warnings, errors }) => {
  return {
    type: SET_HORPHANHOOD_RESUME_ERROR,
    data: { errors, warnings, isError: errors.length > 0 }
  };
};

export const WAS_ORPHANHOOD_API_CALLED = 'WAS_ORPHANHOOD_API_CALLED';
export const OrphanhoodApiCalled = wasCalled => {
  return {
    type: WAS_ORPHANHOOD_API_CALLED,
    data: wasCalled
  };
};
