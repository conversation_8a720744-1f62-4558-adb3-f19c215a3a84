/* eslint-disable consistent-return */
/* eslint-disable no-underscore-dangle */
/* eslint-disable no-console */
import { axiosRequest } from '../../../../services/axiosRequest';
import { executeMonitorJob, loadMonitorJob, skipMonitorJob } from '../actions';
import React from 'react';
import LensIcon from '@material-ui/icons/Lens';

const { REACT_APP_API_URL } = process.env;

const api = REACT_APP_API_URL && REACT_APP_API_URL.replace(/(\/)$/, '');
const EXECUTED = 1;
const NOTEXECUTED = 2;
const PENDINGEXECUTED = 3;
const letterSize = 'small';

const displayStatus = job => {  
  switch (job.status) {
        case EXECUTED: {
          return { runFromWeb: false, color: "green", MsjStatus: "Se ejecutó correctamente"};
        }    
        case NOTEXECUTED: {
          return { runFromWeb: true, color: "red", MsjStatus: "No se ejecutó correctamente"};
        }   
        case PENDINGEXECUTED: {
          return { runFromWeb: false, color: "yellow", MsjStatus: "No se ha ejecutado aún"};
        }  
        default:
          return { runFromWeb: false, color: "black", MsjStatus: ""};
      }
}
const displayDependencies = dependencies => {  
  if( typeof dependencies === 'object' ) {
    return dependencies.toString().replaceAll(',', '\n');
  }
  return dependencies
}

const extractFirstCharacters = dependencies => {
    return (typeof dependencies === 'undefined' || dependencies === '') ? "": dependencies.slice(0,20)+'...';
}
//------------------------------------------------------------------------------------------------
export const executeJob = async job => {
  const { data, isError = false } = await axiosRequest
  .post(`${api}${job.endPoint}`)
  .catch(err => {
    return {
      data: {
        error: err.response.status,
        message: err.response.data.message,
        executionCompleted: false
      },
      isError: true
    };
  }); 

  job.cronDependency = isError ? <label title={data.message} style={{ fontSize: letterSize }}>
                                  {extractFirstCharacters(data.message)}
                                 </label>: job.cronDependency;
  job.status = isError ? <label style={{ color: 'red', fontSize: letterSize }}>
                         <LensIcon  style={{ color: 'red', margin: '0 0 -7px 0' }} /> No se ejecutó correctamente</label>:                          
                         <label style={{ color: 'green', fontSize: letterSize }}>
                         <LensIcon  style={{ color: 'green', margin: '0 0 -7px 0' }} /> Se ejecutó correctamente</label>;
  job.runFromWeb = isError ? true: false;
  return { data: job, error: isError }; 
};

//------------------------------------------------------------------------------------------------

export const skipJob = async job => {  
  // eslint-disable-next-line no-underscore-dangle  
  await axiosRequest.put(`${api}/jobsMonitor/${job.brandExecution.props.title}`).catch(err => {
    console.error(err);       
    job.status = <label style={{ color: 'red', fontSize: letterSize }}><LensIcon  style={{ color: 'red', margin: '0 0 -7px 0' }} /> No se ejecutó correctamente</label>;
    job.runFromWeb = true;
    return { data: job, error: true };
  });
  
  job.status = <label style={{ color: 'green', fontSize: letterSize }}><LensIcon  style={{ color: 'green', margin: '0 0 -7px 0' }} /> Se ejecutó correctamente</label>;
  job.runFromWeb = false;
  return { data: job, error: null };
};

//------------------------------------------------------------------------------------------------
export const processJob = (prevUser, newUser, operation) => async (dispatch, _getState) => {
  let createdUser = newUser;

  if (operation === 'ejecutar') {
    const { data, error } = await executeJob(prevUser);

    if (error) {
      throw new Error(error.code);
    }
    createdUser = data;    
    return dispatch(executeMonitorJob(prevUser, createdUser));
  }
  if (operation === 'saltar') {
    const { error } = await skipJob(newUser);
    if (error) {
      throw new Error(error.code);
    }
    return dispatch(skipMonitorJob(prevUser));
  }
};

//------------------------------------------------------------------------------------------------
export const loadMonitor = async (dispatch, roleName = '') => {
  const {
    data: { result }
  } = await axiosRequest.get(`${api}/jobsMonitor?role=${roleName}`).catch(err => {
    console.error(err);
    return { data: { result: [] } };
  });
  if (!result.length) {
    return dispatch(loadMonitorJob([]));
  }


  const grilla = result.map(fila => {
    const {
      brandExecution,
      description,
      endPoint,
      cronDependency,
    } = fila;

    let statusColor = displayStatus(fila);
    return {
      brandExecution: <label title={brandExecution} style={{ fontSize: letterSize }} >{extractFirstCharacters(brandExecution)}</label>,
      description:<label style={{ fontSize: letterSize }}>{description}</label>,
      endPoint,
      cronDependency: <label title={displayDependencies(cronDependency)} style={{ fontSize: letterSize }}>{extractFirstCharacters(displayDependencies(cronDependency))}</label>,
      status: <label style={{ fontSize: letterSize }}><LensIcon  style={{ color: statusColor.color, margin: '0 0 -7px 0' }} /> {statusColor.MsjStatus}</label>,
      runFromWeb: statusColor.runFromWeb,      
    }
  });
 

  return dispatch(loadMonitorJob(grilla));
};
