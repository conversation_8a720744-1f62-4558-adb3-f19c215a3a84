import React, { useState, useEffect, Fragment } from 'react';
import { Grid, Button, Tooltip } from '@material-ui/core';
import { useSelector, useDispatch } from 'react-redux';
import useOnlineStatus from '@rehooks/online-status';
import ImportButton from 'components/ImportButton';
import useStyles from './styles';
import useRouter from 'utils/useRouter';
import { useProgress } from 'components';
import { checkReportAvailable, UseDownloadReport } from '../hooks/useDownloadReport';
import useDataUpload from '../hooks/useDataUpload';
import { cleanFileError } from './../actions';

const NationalHoliDaysAssignment = ({ onErrorSnackbar, onSuccessSnackbar }) => {
  const fileUploadInput = React.createRef();
  const classes = useStyles();
  const router = useRouter();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();

  const [isReportAvailable, setIsReporteAvailable] = useState(false);

  useEffect(() => {
    dispatch(cleanFileError());
    checkReportAvailable(setIsReporteAvailable);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fileErrors = useSelector(store => store.nationalHolidays.fileErrors);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const handleUploadButton = () => {
    fileUploadInput.current.value = '';
    fileUploadInput.current.click();
  };

  const handleDownloadReport = () =>
    UseDownloadReport({
      dispatch,
      progress,
      onErrorSnackbar
    });

  const handleFileUpload = useDataUpload({
    dispatch,
    router,
    progress,
    onSuccessSnackbar,
    onErrorSnackbar
  });

  return (
    <Fragment>
      <Grid
        className={classes.formControl}
        container
        direction="row"
        justify="space-around"
        alignItems="center"
      >
        <Grid item>
          <ImportButton
            classes={{ formControl: classes.formControl }}
            errorMessage={fileErrors}
            onClick={handleUploadButton}
            disabled={!onlineStatus}
            footer="Formato TXT max 10MB"
          />
        </Grid>
        <Grid item>
          <Tooltip title={'Descargar'} aria-label="inactReactTooltip">
            <span>
              <Button
                variant="contained"
                disabled={!onlineStatus || !isReportAvailable}
                onClick={handleDownloadReport}
              >
                Descargar
              </Button>
            </span>
          </Tooltip>
        </Grid>
      </Grid>

      <input
        ref={fileUploadInput}
        type="file"
        style={{ display: 'none' }}
        id="fileUpload"
        disabled={!onlineStatus}
        onChange={v => v.target.files.length && handleFileUpload(v.target.files[0])}
        accept="*"
      />
    </Fragment>
  );
};

export default NationalHoliDaysAssignment;
