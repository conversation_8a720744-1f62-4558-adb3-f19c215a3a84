/* eslint-disable react/prop-types */
import React from 'react';
import { KeyboardDatePicker } from '@material-ui/pickers';
import 'moment/locale/es';
import { ThemeProvider } from '@material-ui/styles';

const Picker = ({
  className,
  disabled,
  label,
  value,
  error,
  helperText,
  minDate,
  maxDate,
  onChange,
  style,
  inputProps,
  onKeyDown,
  MuiStyle
}) => {
  return (
    <div>
      <ThemeProvider theme={MuiStyle}>
        <KeyboardDatePicker
          lang="es"
          autoOk
          className={className.calendar}
          disabled={disabled}
          allowKeyboardControl={false}
          emptyLabel={label}
          format="DD-MM-YYYY"
          value={value}
          initialFocusedDate={value}
          inputVariant="outlined"
          error={error}
          helperText={helperText}
          minDate={minDate}
          maxDate={maxDate}
          label={label}
          variant="inline"
          views={['date', 'month', 'year']}
          size="small"
          style={style}
          onChange={e => onChange(e)}
          onKeyDown={onKeyDown}
          InputLabelProps={{
            shrink: true
          }}
          inputProps={{ ...inputProps }}
        />
      </ThemeProvider>
    </div>
  );
};

export default Picker;
