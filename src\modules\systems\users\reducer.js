/* eslint-disable no-underscore-dangle */
import { CREATE_NOM_USER, DELETE_NOM_USER, SET_NOM_USER, UPDATE_NOM_USER } from './actions';

const initialState = {
  data: [],
  errors: []
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case CREATE_NOM_USER: {
      const { user } = action.data;
      return {
        ...state,
        data: [...state.data, user]
      };
    }
    case UPDATE_NOM_USER: {
      const data = [...state.data];
      data[data.findIndex(({ _id }) => action.data.newUser._id === _id)] = action.data.newUser;
      return { ...state, data };
    }
    case DELETE_NOM_USER: {
      const data = [...state.data];
      data.splice(data.indexOf(action.data.user), 1);
      return { ...state, data };
    }
    case SET_NOM_USER: {
      const data = [...action.data.user];
      return { ...state, data };
    }
    default:
      return state;
  }
}
