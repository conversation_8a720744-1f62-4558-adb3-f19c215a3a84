/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React, { Suspense } from 'react';
import { renderRoutes } from 'react-router-config';
import { HeaderContent, Page, SnackbarManager } from 'components';
import { LinearProgress } from '@material-ui/core';

import styles from './styles';
import TemporaryPensions from './temporaryPensions';
import TemporaryPensionsResume from './temporaryPensionsResume';
import { useLink } from '../../pensions/hooks/usePensions';

const TemporaryPensionsPage = props => {
  // eslint-disable-next-line react/prop-types
  const { route } = props;
  const classes = styles();
  const {
    successLinkedSnackbar,
    errorLinkedSnackbar,
    setErrorLinkedSnackbar,
    setSuccessLinkedSnackbar,
    onLink
  } = useLink();

  return (
    <Page className={classes.root} title="Nuevas Pensiones">
      <HeaderContent title="Nuevas Pensiones" />
      <Suspense fallback={<LinearProgress />}>
        {renderRoutes(route.routes, { onLink, setErrorLinkedSnackbar })}
      </Suspense>
      <SnackbarManager
        text="Los pensionados enlazados se verán reflejados como vigentes al iniciar el mes siguiente"
        error="Error al importar archivo"
        successSnackbar={successLinkedSnackbar}
        errorSnackbar={errorLinkedSnackbar}
        setErrorSnackbar={setErrorLinkedSnackbar}
        setSuccessSnackbar={setSuccessLinkedSnackbar}
      />
    </Page>
  );
};

export { TemporaryPensionsPage, TemporaryPensions, TemporaryPensionsResume };
