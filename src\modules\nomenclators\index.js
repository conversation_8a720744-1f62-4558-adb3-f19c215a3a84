/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import SnackbarManager from 'components/SnackbarManager';
import React from 'react';
import PropTypes from 'prop-types';
import { renderRoutes } from 'react-router-config';
import { Page } from '../../components';
import { useSnackbar } from './useSnackbar';
import useStyles from './style';
import MinimunPensionsAndBonus from './minimun-pensions-and-bonus/views';

const NomenclatorPage = ({ route }) => {
  const classes = useStyles();
  const {
    text,
    setText,
    error,
    setError,
    successSnackbar,
    errorSnackbar,
    setSuccessSnackbar,
    setErrorSnackbar
  } = useSnackbar();

  const onSuccessSnackbar = val => {
    setText(val);
    setSuccessSnackbar(true);
    setErrorSnackbar(false);
  };

  const onErrorSnackbar = err => {
    setError(err);
    setSuccessSnackbar(false);
    setErrorSnackbar(true);
  };

  return (
    <Page className={classes.root} title="Mantenedores">
      {renderRoutes(route.routes, { onSuccessSnackbar, onErrorSnackbar })}
      <SnackbarManager
        handleRoute={false}
        error={error}
        text={text}
        successSnackbar={successSnackbar}
        errorSnackbar={errorSnackbar}
        setErrorSnackbar={setErrorSnackbar}
        setSuccessSnackbar={setSuccessSnackbar}
      />
    </Page>
  );
};

NomenclatorPage.propTypes = {
  route: PropTypes.shape({
    path: PropTypes.string,
    routes: PropTypes.array,
    component: PropTypes.func
  }).isRequired
};

export { NomenclatorPage, MinimunPensionsAndBonus };
