/* eslint-disable react/prop-types */
/* eslint-disable react/forbid-prop-types */
import { v4 as uuidv4 } from 'uuid';
import { Tooltip, IconButton, FormLabel } from '@material-ui/core';
import { ArrowDownward, Clear, Check, DeleteOutline, Edit } from '@material-ui/icons';
import MaterialTable, { MTableEditRow } from 'material-table';
import PropTypes from 'prop-types';
import React, { forwardRef, useRef } from 'react';
import { ValidatorForm } from 'react-material-ui-form-validator';
import {
  defaultFormatter,
  formatChileanAmount,
  isValidDecimal,
  amountMatchRule
} from '../../validator/validField';

import SecureTextField, { matchRule } from './secureTextField';
import SelectTextField from './SelectTextField';
import { checkWritePermission } from 'utils/checkUserPermission';

const percentageValidator = ['required', 'matchPercentage', 'maxStringLength:4'];
const percentageErrorMessage = [
  'El valor es requerido',
  'Valores permitidos: [0.01 -  9.99]',
  'Sólo dos decimales permitidos'
];

const maxAmountValidator = ['required', 'matchAmount', 'maxStringLength:7'];
const maxAmountErrorMessage = [
  'El valor es requerido',
  'Valores permitidos: [1 - 999999] si Monto Maximo es [Si]',
  'Si Monto Maximo es [No] valor debe ser 0'
];

const tableIcons = {
  Check: forwardRef((props, ref) => <Check {...props} ref={ref} />),
  Edit: forwardRef((props, ref) => <Edit {...props} ref={ref} />),
  Delete: forwardRef((props, ref) => <DeleteOutline {...props} ref={ref} />),
  Clear: forwardRef((props, ref) => <Clear {...props} ref={ref} />),
  SortArrow: forwardRef((props, ref) => <ArrowDownward {...props} ref={ref} />)
};
const isRowValid = ({ name, percentage, isMaxAmount, maxAmount }) => {
  if (isMaxAmount === 'No' && parseInt(maxAmount) > 0) {
    return false;
  }
  if (isMaxAmount === 'Si' && parseInt(maxAmount) === 0) {
    return false;
  }

  return matchRule(name) && isValidDecimal(percentage) && amountMatchRule(maxAmount);
};

const lengthObject = { name: '255', percentage: '4', maxAmount: '6' };
const getMaxLength = fieldName => ({ maxLength: lengthObject[fieldName] || '255' });
const editableFn = (fieldName, labelName, validators, errorMessages, formater) => ({
  onChange,
  value
}) => (
  <SecureTextField
    name={fieldName}
    id={fieldName}
    size="small"
    displayName={labelName}
    inputprops={getMaxLength(fieldName)}
    validators={validators}
    errorMessages={errorMessages}
    onChange={e =>
      onChange(
        formater
          ? formater(e.target.value.replace('.', ','))
          : defaultFormatter(e.target.value.replace('.', ','))
      )
    }
    onBlur={e => onChange(e.target.value)}
    value={value || ''}
  />
);

const SelectFn = (fieldName, labelName, options) => ({ onChange, value = '' }) => {
  return (
    <SelectTextField
      name={fieldName}
      displayName={labelName}
      size="small"
      inputprops={getMaxLength(fieldName)}
      onChange={e => onChange(e.target.value)}
      value={value}
      options={options}
    />
  );
};

const LabelFn = (_fieldName, _labelName) => ({ onChange, value = '' }) => {
  return <FormLabel>{value}</FormLabel>;
};

export default function CajaTable(props) {
  const { data, onCreate, onUpdate, onSubmit, userRole } = props;
  const hasWritePermission = checkWritePermission(userRole);

  const formRef = useRef(null);

  const selectValues = [
    { id: 'Si', name: 'Si' },
    { id: 'No', name: 'No' }
  ];
  const selectComponent = SelectFn('isMaxAmount', 'Monto Máximo', selectValues);
  const labelComponent = LabelFn('name', 'Caja de Compensación');
  const percentageComponent = editableFn(
    'percentage',
    'Descuento [%]',
    percentageValidator,
    percentageErrorMessage
  );
  const maxAmountComponent = editableFn(
    'maxAmount',
    'Monto Máximo Descuento',
    maxAmountValidator,
    maxAmountErrorMessage
  );

  return (
    <ValidatorForm onSubmit={onSubmit} ref={formRef} instantValidate>
      <MaterialTable
        components={{
          Action: p => {
            let { action } = p;
            if (typeof p.action === 'function') {
              action = p.action();
            }
            return (
              <Tooltip title={!action.disabled && !p.disabled ? action.tooltip : ''}>
                <IconButton
                  onClick={event => {
                    action.onClick(event, p.data);
                  }}
                  color="inherit"
                  variant="contained"
                  style={{ textTransform: 'none' }}
                  size={p.size}
                  disabled={!hasWritePermission || action.disabled || p.disabled}
                >
                  {action.icon.render()}
                </IconButton>
              </Tooltip>
            );
          },
          EditRow: p => (
            <MTableEditRow
              {...p}
              onEditingCanceled={(mode, rowData) => {
                return p.onEditingCanceled(mode, rowData);
              }}
              onEditingApproved={async (mode, newData, oldData) => {
                if (!isRowValid(newData)) {
                  formRef.current.submit();
                  const { onErrorSnackbar } = props;
                  if (newData.isMaxAmount === 'No' && parseInt(newData.maxAmount) > 0) {
                    let messageError;
                    messageError = 'Si Monto maximo es [No] Monto maximo Descuento debe ser cero ';
                    onErrorSnackbar(messageError);
                    return null;
                  }
                  if (newData.isMaxAmount === 'Si' && parseInt(newData.maxAmount) === 0) {
                    let messageError;
                    messageError =
                      'Si Monto maximo es [Si] Monto maximo Descuento debe ser mayor cero';
                    onErrorSnackbar(messageError);
                    return null;
                  }
                }
                return p.onEditingApproved(mode, newData, oldData);
              }}
            />
          )
        }}
        icons={tableIcons}
        columns={[
          {
            title: 'Caja de Compensación',
            field: 'name',
            sorting: false,
            editComponent: labelComponent
          },
          {
            title: 'Descuento [%]',
            field: 'percentage',
            sorting: false,
            editComponent: percentageComponent
          },
          {
            title: 'Monto Máximo',
            field: 'isMaxAmount',
            sorting: false,
            editComponent: selectComponent
          },
          {
            title: 'Monto Máximo Descuento',
            field: 'maxAmount',
            sorting: false,
            render: ({ maxAmount }) => formatChileanAmount(maxAmount),
            editComponent: maxAmountComponent
          }
        ]}
        data={data}
        options={{
          addRowPosition: 'last',
          thirdSortClick: false,
          paging: false,
          search: false,
          showTitle: false,
          actionsColumnIndex: -1,
          toolbar: false
        }}
        localization={{
          body: {
            emptyDataSourceMessage: 'No existen registros ingresados',
            deleteTooltip: 'Eliminar',
            editTooltip: 'Editar',
            editRow: {
              deleteText: '¿Está seguro que desea eliminar el elemento?',
              saveTooltip: 'Aceptar',
              cancelTooltip: 'Cancelar'
            }
          },
          header: {
            actions: 'Acciones'
          }
        }}
        editable={{
          onRowAdd: newData =>
            new Promise(async (resolve, reject) => {
              console.log('P1-onRowAdd');
              if (!isRowValid(newData)) {
                return reject();
              }
              try {
                await onCreate({ ...newData, id: uuidv4() }, {});
                return resolve();
              } catch (error) {
                return resolve();
              }
            }),
          onRowUpdate: (newData, oldData) =>
            new Promise(async (resolve, reject) => {
              console.log('P1-onRowUpdate');
              if (!isRowValid(newData)) {
                return reject();
              }
              try {
                await onUpdate(newData, oldData);
                return resolve();
              } catch (error) {
                return resolve();
              }
            })
        }}
      />
    </ValidatorForm>
  );
}

CajaTable.propTypes = {
  data: PropTypes.oneOfType([PropTypes.array, PropTypes.func]).isRequired,
  onCreate: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  onSubmit: PropTypes.func,
  onErrorSnackbar: PropTypes.func.isRequired
};

CajaTable.defaultProps = {
  onSubmit: () => {
    /*any*/
  }
};
