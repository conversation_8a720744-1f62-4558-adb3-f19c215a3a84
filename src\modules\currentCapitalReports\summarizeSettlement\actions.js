//------------------------------------------------------------------------------------------------
export const SET_IS_DOWNLOADING = 'SET_IS_DOWNLOADING';
export const currentlyDownloadingReports = status => {
  return {
    type: SET_IS_DOWNLOADING,
    data: status
  };
};

//------------------------------------------------------------------------------------------------
export const SET_IS_VALID_DATE_RANGE = 'SET_IS_VALID_DATE_RANGE';
export const setIsValidDateRangeReports = valid => {
  return {
    type: SET_IS_VALID_DATE_RANGE,
    data: valid
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CURRENT_DATE_REPORTS = 'SET_CURRENT_DATE_REPORTS';
export const setCurrentDateReports = newDate => {
  return {
    type: SET_CURRENT_DATE_REPORTS,
    data: newDate
  };
};
//------------------------------------------------------------------------------------------------
export const SET_IS_VALID_DATE = 'SET_IS_VALID_DATE';
export const setIsValidDateReports = valid => {
  return {
    type: SET_IS_VALID_DATE,
    data: valid
  };
};
//------------------------------------------------------------------------------------------------
export const SET_IS_VALID_STARTING_DATE = 'SET_IS_VALID_STARTING_DATE';
export const setIsValidStartingDate = valid => {
  return {
    type: SET_IS_VALID_STARTING_DATE,
    data: valid
  };
};
//------------------------------------------------------------------------------------------------
export const SET_IS_VALID_ENDING_DATE = 'SET_IS_VALID_ENDING_DATE';
export const setIsValidEndingDate = valid => {
  return {
    type: SET_IS_VALID_ENDING_DATE,
    data: valid
  };
};
