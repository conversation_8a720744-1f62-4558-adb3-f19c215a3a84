import {
  AddBoxOutlined,
  HowToRegOutlined,
  Publish,
  SettingsApplicationsOutlined
} from '@material-ui/icons';
import PersonIcon from '@material-ui/icons/Person';
import LibraryBooksOutlinedIcon from '@material-ui/icons/LibraryBooksOutlined';
import SettingsSystemDaydreamIcon from '@material-ui/icons/SettingsSystemDaydream';

export default [
  {
    title: 'Consulta Pensionado',
    pages: [
      {
        title: 'Búsqueda',
        module: 'Consulta de pensionados',
        href: '/pensionados/consulta-pensionados',
        icon: PersonIcon
      }
    ]
  },
  {
    title: 'Enlazar',
    pages: [
      {
        title: 'Nuevas Pensiones',
        module: 'Enlazar',
        href: '/nuevas-pensiones/importar',
        icon: AddBoxOutlined
      }
    ]
  },
  {
    title: 'Regularizar Estado de Pensiones',
    pages: [
      {
        title: 'Inactivar y Reactivar',
        module: 'Asignaci[óo]n Familiar',
        href: '/regularizar-pensiones/inactivar-reactivar',
        icon: HowToRegOutlined,
        children: [
          {
            title: 'Asignación Familiar',
            href: '/regularizar-pensiones/inactivar-reactivar/reasignacion-familiar/',
            depth: 2
          },          
          {
              title: 'Orfandades',
              href: '/regularizar-pensiones/inactivar-reactivar/orfandades/',
              depth: 2
          }
        ]
      }
    ]
  },
  {
    title: 'Preparar Liquidación de Pago',
    pages: [
      {
        title: 'Cargar Descuentos',
        module: 'Cargar Descuentos',
        href: '/preparar-liquidacion',
        icon: Publish,
        children: [
          {
            title: 'Caja Los Andes',
            href: '/preparar-liquidacion/cargar-descuentos/caja-los-andes',
            depth: 2
          },
          {
            title: 'Fonasa',
            href: '/preparar-liquidacion/cargar-descuentos/fonasa',
            depth: 2
          }
        ]
      }
    ]
  },
  {
    title: 'Configuraciones',
    pages: [
      {
        title: 'Mantenedores',
        module: 'Mantenedor',
        href: '/mantenedores',
        icon: SettingsApplicationsOutlined,
        children: [
          {
            title: 'Pensiones mínimas y aguinaldos',
            href: '/mantenedores/pensiones-minimas-y-aguinaldos',
            depth: 2
          },
          {
            title: 'AFP',
            href: '/mantenedores/afp',
            depth: 2
          },
          {
            title: 'ISAPRES',
            href: '/mantenedores/isapres',
            depth: 2,
            children: [
              {
                title: 'Isapres',
                href: '/mantenedores/isapres',
                depth: 3
              },
              {
                title: 'Portal isapre',
                href: '/mantenedores/isapres/portal',
                depth: 3
              }
            ]
          },
          {
            title: 'Sucursales Banco de Chile',
            href: '/mantenedores/sucursales-banco-de-chile',
            depth: 2
          },
          {
            title: 'Sucursales Servipag',
            href: '/mantenedores/sucursales-servipag',
            depth: 2
          },
          {
            title: 'Motivos',
            href: '/mantenedores/motivos',
            depth: 2
          },
          {
            title: 'Factores Capitales Vigentes',
            href: '/mantenedores/factores-capitales/importar',
            depth: 2
          },
          {
            title: 'Asignación de aguinaldos',
            href: '/mantenedores/asignacion-aguinaldos',
            depth: 2
          },
          {
            title: 'Topes, Máximo y Unidades Reajustables',
            href: '/mantenedores/topes-maximo-and-unidades-reajustables',
            depth: 2
          },  
          {
            title: 'Unidad reajustable',
            href: '/mantenedores/unidad-reajustable',
            depth: 2
          },                    
        ]
      },
      {
        title: 'Reportería',
        module: 'Reporter[íi]a',
        href: '/reporteria',
        icon: LibraryBooksOutlinedIcon,
        children: [
          {
            title: 'Inactivaciones y Reactivaciones',
            href: '/reporteria/inactivaciones-y-reactivaciones',
            depth: 2
          },
          {
            title: 'Liquidaciones',
            href: '/reporteria/liquidaciones',
            depth: 2
          },
          {
            title: 'Capitales vigentes',
            href: '/reporteria/capitales-vigentes',
            depth: 2
          },
          {
            title: 'Contabilización de Pensiones',
            href: '/reporteria/contabilizacion-pensiones',
            depth: 2
          },
          {
            title: 'Archivo Banco',
            href: '/reporteria/archivo-banco',
            depth: 2
          },
          {
            title: 'Archivo Previred',
            href: '/reporteria/archivo-previred',
            depth: 2
          }
        ]
      },
      {
        title: 'Sistemas',
        module: 'Sistemas',
        href: '/sistemas',
        icon: SettingsSystemDaydreamIcon,
        children: [
          {
            title: 'Roles y Permisos',
            href: '/sistemas/roles-y-permisos',
            depth: 2
          },
          {
            title: 'Usuarios',
            href: '/sistemas/usuarios',
            depth: 2
          },
          {
            title: 'Visualización de estados de crons',
            href: '/sistemas/monitor',
            depth: 3
          }
        ]
      }
    ]
  }
];
