import React from 'react';
import { Box } from '@material-ui/core';

function TabPanelGeneral(props) {
  // eslint-disable-next-line react/prop-types
  const { children, value, index } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      style={{ padding: 3 }}
    >
      {value === index && <Box p={2}>{children}</Box>}
    </div>
  );
}

export default TabPanelGeneral;
