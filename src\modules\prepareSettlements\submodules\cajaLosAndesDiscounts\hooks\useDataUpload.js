/* eslint-disable no-param-reassign */
import { insertCajaLosAndesDiscounts } from '../services/cajaLosAndesDiscounts.service';
import {
  cleanFileMemberships,
  cleanFileCredits,
  cleanFileAnotherDiscounts,
  setWasLosAndesProcessExecuted,
  setSnackbarMessageError,
  setOpenSnackbarError
} from '../actions';

const ON_SUCCESS_TEXT = 'Importación Exitosa';

const uploadCajaLosAndesDiscounts = async cajaLosAndesDiscountsData => {
  const {
    data: { result }
  } = await insertCajaLosAndesDiscounts(cajaLosAndesDiscountsData);

  return result;
};

const upsertInto = ({ fromData, toData, id }) => {
  const keys = Object.keys(fromData[0]);
  const indexId = keys.indexOf(id);
  indexId !== -1 && keys.splice(indexId, 1);
  fromData.forEach(dataObject => {
    const index = toData.findIndex(cajaLosAndesObj => cajaLosAndesObj.rut === dataObject.rut);
    index === -1
      ? toData.push(dataObject)
      : keys.forEach(key => {
          toData[index][key] = dataObject[key];
        });
  });
};

const generateUploadData = ({ membershipsData, creditsData, anotherDiscountsData }) => {
  const cajaLosAndes = [];
  upsertInto({ fromData: membershipsData, toData: cajaLosAndes, id: 'rut' });
  upsertInto({ fromData: creditsData, toData: cajaLosAndes, id: 'rut' });
  upsertInto({ fromData: anotherDiscountsData, toData: cajaLosAndes, id: 'rut' });
  return { cajaLosAndes };
};

const uploadFiles = async (
  dispatch,
  membershipsData,
  creditsData,
  anotherDiscountsData,
  progress,
  onSuccess,
  onError
) => {
  progress.show();
  const cajaLosAndesDiscountsData = generateUploadData({
    membershipsData,
    creditsData,
    anotherDiscountsData
  });

  const result = await uploadCajaLosAndesDiscounts(cajaLosAndesDiscountsData);

  if (!!result && !result.completed) {
    let message = '';
    if (result.errors) {
      const err = result.errors.map(error => {
        return error.msg + ' </br>';
      });
      message = err.join('');
    } else {
      message = result.message;
    }

    dispatch(setSnackbarMessageError(message));
    dispatch(setOpenSnackbarError(true));
    progress.hide();
    return false;
  }
  dispatch(cleanFileMemberships());
  dispatch(cleanFileCredits());
  dispatch(cleanFileAnotherDiscounts());
  dispatch(setWasLosAndesProcessExecuted(true));
  onSuccess(ON_SUCCESS_TEXT);
  progress.hide();
  return true;
};

const useDataUpload = (
  dispatch,
  _router,
  progress,
  onSuccess,
  onError,
  membershipsData,
  creditsData,
  anotherDiscountsData
) => {
  return () => {
    if (
      (membershipsData && membershipsData.length > 0,
      creditsData && creditsData.length > 0,
      anotherDiscountsData && anotherDiscountsData.length > 0)
    ) {
      uploadFiles(
        dispatch,
        membershipsData,
        creditsData,
        anotherDiscountsData,
        progress,
        onSuccess,
        onError
      );
    }
  };
};

export default useDataUpload;
