/* eslint-disable no-magic-numbers */
import React from 'react';
import useStyles from './styles';
import { Card, CardContent, Typography, Divider } from '@material-ui/core';

import { Page } from '../../../components';
import LoginForm from './components/LoginForm';

const Login = () => {
  const classes = useStyles();

  return (
    <Page className={classes.root} title="Login">
      <div className={classes.achsBackground}>
        <Card className={classes.card}>
          <CardContent className={classes.content}>
            <img alt="Logo" className={classes.icon} src="/images/logos/Logo_ACHS.svg" />
            <Typography gutterBottom variant="h3">
              Bienvenido a Prestaciones Económicas
            </Typography>
            <Typography variant="subtitle2">Ingrese sus credenciales para continuar</Typography>
            <Divider className={classes.divider} />
            <LoginForm className={classes.loginForm} />
          </CardContent>
        </Card>
      </div>
    </Page>
  );
};

export default Login;
