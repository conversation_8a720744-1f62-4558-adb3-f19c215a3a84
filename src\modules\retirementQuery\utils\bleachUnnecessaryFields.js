import {
  branchOfficeRelatedFields,
  electronicCheckBancoDeChile,
  bankOnlyRelatedOptions
} from './paymentGatewayList';

const bleachUnnecesaryFields = data => {
  let bleachedObj;
  const { paymentGateway, bank, branchOffice, accountNumber, ...otherFields } = data;
  if (branchOfficeRelatedFields.includes(paymentGateway)) {
    bleachedObj = { ...otherFields, paymentGateway, branchOffice, bank: '', accountNumber: '' };
    return bleachedObj;
  }
  if (bankOnlyRelatedOptions.includes(paymentGateway)) {
    bleachedObj = { ...otherFields, paymentGateway, bank, accountNumber, branchOffice: '' };
    return bleachedObj;
  }
  if (electronicCheckBancoDeChile.includes(paymentGateway)) {
    bleachedObj = { ...otherFields, paymentGateway, accountNumber, branchOffice: '', bank: '' };
    return bleachedObj;
  }
  return data;
};

export default bleachUnnecesaryFields;
