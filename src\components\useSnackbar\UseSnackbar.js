import { useState } from 'react';

const UseSnackbar = () => {
  const [text, setText] = useState('');
  const [error, setError] = useState('');
  const [successSnackbar, setSuccessSnackbar] = useState(false);
  const [errorSnackbar, setErrorSnackbar] = useState(false);

  return {
    text,
    error,
    successSnackbar,
    errorSnackbar,
    setSuccessSnackbar,
    setErrorSnackbar,
    setText,
    setError
  };
};

export default UseSnackbar;
