/* eslint-disable no-unused-vars */
import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import { AppBar, Button, IconButton, Toolbar, Hidden, colors } from '@material-ui/core';

import InputIcon from '@material-ui/icons/Input';
import MenuIcon from '@material-ui/icons/Menu';
import HomeIcon from '@material-ui/icons/Home';
import useOnLineStatus from '@rehooks/online-status';
import LoadingNetwork from '../../../../components/LoadingNetwork';
import useRouter from '../../../../utils/useRouter';
import authService from '../../../../services/auth';
const useStyles = makeStyles(theme => ({
  root: {
    boxShadow: 'none',
    backgroundColor: '#B8E5E2'
  },
  flexGrow: {
    flexGrow: 1
  },
  search: {
    backgroundColor: 'rgba(255,255,255, 0.2)',
    borderRadius: 4,
    flexBasis: 300,
    height: 36,
    padding: theme.spacing(0, 2),
    display: 'flex',
    alignItems: 'center'
  },
  searchIcon: {
    marginRight: theme.spacing(2),
    color: 'inherit'
  },
  searchInput: {
    flexGrow: 1,
    color: 'inherit',
    '& input::placeholder': {
      opacity: 1,
      color: 'inherit'
    }
  },
  searchPopper: {
    zIndex: theme.zIndex.appBar + 100
  },
  searchPopperContent: {
    marginTop: theme.spacing(1)
  },
  trialButton: {
    marginLeft: theme.spacing(2),
    color: theme.palette.white,
    backgroundColor: colors.green[600],
    '&:hover': {
      backgroundColor: colors.green[900]
    }
  },
  logoAchs: { width: 45 },
  trialIcon: {
    marginRight: theme.spacing(1)
  },
  notificationsButton: {
    marginLeft: theme.spacing(1)
  },
  notificationsBadge: {
    backgroundColor: colors.orange[600]
  },
  logoutButton: {
    marginLeft: theme.spacing(1),
    color: '#006531'
  },
  logoutIcon: {
    // marginRight: theme.spacing(1)
  }
}));

const TopBar = props => {
  const { onOpenNavBarMobile, className, ...rest } = props;
  const isOnline = useOnLineStatus();
  const classes = useStyles();
  const router = useRouter();

  return (
    <>
      <AppBar {...rest} className={clsx(classes.root, className)} color="primary">
        <Toolbar>
          <RouterLink to="/">
            <img alt="Logo" className={classes.logoAchs} src="/images/logos/Logo_ACHS.svg" />
          </RouterLink>
          <div className={classes.flexGrow} />

          <Hidden mdDown>
            <Button className={classes.logoutButton} color="inherit" href="/home">
              <HomeIcon className={classes.logoutIcon} />
            </Button>
            <Button
              className={classes.logoutButton}
              color="inherit"
              onClick={() => {
                authService.logout(router);
              }}
            >
              <InputIcon className={classes.logoutIcon} />
              <span className={classes.notificationsButton}>Salir</span>
            </Button>
          </Hidden>
          <Hidden lgUp>
            <IconButton color="inherit" onClick={onOpenNavBarMobile}>
              <MenuIcon />
            </IconButton>
          </Hidden>
        </Toolbar>
      </AppBar>
      {!isOnline && <LoadingNetwork />}
    </>
  );
};

TopBar.propTypes = {
  className: PropTypes.string,
  onOpenNavBarMobile: PropTypes.func
};

TopBar.defaultProps = {
  className: '',
  onOpenNavBarMobile: () => { /*any*/ }
};

export default TopBar;
