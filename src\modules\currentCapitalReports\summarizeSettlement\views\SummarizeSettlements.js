/* eslint-disable no-undef */
/* eslint-disable react/prop-types */
/* eslint-disable import/prefer-default-export */
/* eslint-disable no-underscore-dangle */
import React, { useEffect, useState } from 'react';
import { Button, Grid, Typography } from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import useOnlineStatus from '@rehooks/online-status';
import { HeaderContent, useProgress } from 'components';
import useRouter from 'utils/useRouter';
import { useSelector, useDispatch } from 'react-redux';
import useMediaQuery from '@material-ui/core/useMediaQuery';
import useStyles from './styles';
import validateDate from '../hooks/validateDate';
import useDataDownload from '../hooks/useDataDownload';
import { getCurrentDate } from '../services/summarizeSettlements.service';
import Picker from '../components/datePicker/picker';
import { datePickerStyles } from '../components/datePicker/datePickerStyles';
import {
  setIsValidDateReports,
  currentlyDownloadingReports,
  setCurrentDateReports,
  setIsValidStartingDate,
  setIsValidEndingDate
} from '../actions';

const TITLE = 'Liquidaciones';
const SUBTITLE = 'Configuraciones / Reportería';
const minDate = new Date('01-01-2020');
const maxDate = new Date('01-01-2028');
const startingLabel = 'Fecha de inicio';
const endingLabel = 'Fecha de término';

const SummarizeSettlements = ({ onErrorSnackbar }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();
  const router = useRouter();
  const matches = useMediaQuery('(min-width:600px)');

  const isValidDateRange = useSelector(store => store.summarizeSettlements.isValidDateRange);
  const isDownloading = useSelector(store => store.summarizeSettlements.isDownloading);
  const isValidDate = useSelector(store => store.summarizeSettlements.validDate);
  const isValidStartingDate = useSelector(store => store.summarizeSettlements.isValidStartingDate);
  const isValidEndingDate = useSelector(store => store.summarizeSettlements.isValidEndingDate);
  const currentDate = useSelector(store => store.summarizeSettlements.currentDate);

  const [startingDate, setStartingDate] = useState(currentDate);
  const [endingDate, setEndingDate] = useState(currentDate);
  const [doesStartExist, setDoesStartExists] = useState(true);
  const [doesEndExists, setDoesEndExists] = useState(true);

  useEffect(() => {
    dispatch(setCurrentDateReports(''));
    dispatch(currentlyDownloadingReports(false));
    dispatch(setIsValidDateReports(true));
    getCurrentDate(dispatch);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (currentDate) {
      setStartingDate(currentDate);
      setEndingDate(currentDate);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentDate]);

  useEffect(() => {
    if (currentDate && startingDate && endingDate) {
      validateDate(
        startingDate,
        endingDate,
        currentDate,
        minDate,
        doesStartExist,
        doesEndExists
      )(dispatch);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startingDate, endingDate]);

  const handleEndingDateChange = date => {
    if (!date || !date._isValid) {
      dispatch(setIsValidDateReports(false));
      dispatch(setIsValidEndingDate(false));
      setDoesEndExists(false);
    }
    if (date && date._isValid) {
      setDoesEndExists(true);
      setEndingDate(date._d);
    }
  };
  const handleStartingDateChange = date => {
    if (!date || !date._isValid) {
      dispatch(setIsValidDateReports(false));
      dispatch(setIsValidStartingDate(false));
      setDoesStartExists(false);
    }
    if (date && date._isValid) {
      setDoesStartExists(true);
      setStartingDate(date._d);
    }
  };

  const warningDate = (isStartingLEQ, isInValidRange, isCurrentDate, doesExist) => {
    if ((!isInValidRange && isCurrentDate) || !doesExist) return 'Fecha fuera de rango permitido';
    if (!isStartingLEQ && isCurrentDate)
      return 'Fecha de término debe ser igual o mayor a Fecha de inicio';
    return '';
  };

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const handleRequestSettlements = useDataDownload(
    dispatch,
    router,
    progress,
    onErrorSnackbar,
    startingDate,
    endingDate,
    currentDate
  );

  return (
    <>
      <Grid container>
        <Grid item xs={12}>
          <Alert className={classes.alertBar} severity="error" color="info">
            Ingrese rango de fechas para exportar histórico de liquidaciones
          </Alert>
        </Grid>
        <Grid item xs={12}>
          <Typography className={classes.subtitle} color="textSecondary">
            {SUBTITLE}
          </Typography>
        </Grid>
        <Grid item xs={12}>
          <HeaderContent title={TITLE} />
        </Grid>
        <Grid container justify="flex-end">
          <Button
            className={classes.export}
            color="primary"
            disabled={
              !onlineStatus ||
              !isValidDateRange ||
              isDownloading ||
              !currentDate ||
              !isValidDate ||
              !doesStartExist ||
              !doesEndExists
            }
            onClick={handleRequestSettlements}
            size="medium"
            variant="contained"
          >
            Exportar
          </Button>
        </Grid>
        <Grid />
        <Grid container spacing={3}>
          <Grid item style={{ flexDirection: matches ? 'row' : 'column' }}>
            <Picker
              className={classes}
              disabled={!onlineStatus || !currentDate || isDownloading}
              label={startingLabel}
              value={startingDate}
              error={(!isValidStartingDate || !isValidDateRange || !doesStartExist) && currentDate}
              helperText={warningDate(true, isValidStartingDate, currentDate, doesStartExist)}
              minDate={minDate}
              maxDate={maxDate}
              onChange={handleStartingDateChange}
              MuiStyle={datePickerStyles}
            />
          </Grid>

          <Grid item style={{ flexDirection: matches ? 'row' : 'column' }}>
            <Picker
              className={classes}
              disabled={!onlineStatus || !currentDate || isDownloading}
              label={endingLabel}
              value={endingDate}
              error={(!isValidEndingDate || !isValidDateRange || !doesEndExists) && currentDate}
              helperText={warningDate(
                isValidDateRange,
                isValidEndingDate,
                currentDate,
                doesEndExists
              )}
              minDate={minDate}
              maxDate={maxDate}
              onChange={handleEndingDateChange}
              MuiStyle={datePickerStyles}
            />
          </Grid>
        </Grid>
      </Grid>
    </>
  );
};

export { SummarizeSettlements };
