import React from 'react';
import { Grid } from '@material-ui/core';
import { Header, DownloadReport } from './components';

// eslint-disable-next-line react/prop-types
const InactivationReactivation = ({ onErrorSnackbar }) => {
  return (
    <>
      <Grid>
        <Header subtitle="Configuraciones / Reportería" title="Inactivaciones y Reactivaciones" />
      </Grid>
      <Grid container direction="row" justify="flex-end" spacing={1}>
        <Grid item>
          <Grid container spacing={2}>
            <Grid item>
              <DownloadReport onErrorSnackbar={onErrorSnackbar} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  );
};

export default InactivationReactivation;
