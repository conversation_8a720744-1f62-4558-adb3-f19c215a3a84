/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import SnackbarManager from 'components/SnackbarManager';
import React from 'react';
import PropTypes from 'prop-types';
import { renderRoutes } from 'react-router-config';
import { Page } from '../../components';
import { useSnackbar } from './hooks/useSnackbar';
import useStyles from './styles';
import CajaLosAndesDiscountsPage from './submodules/cajaLosAndesDiscounts/views';
import FonasaDiscountsPage from './submodules/fonasaDiscounts/views/FonasaDiscount';

const PrepareSettlementPage = ({ route }) => {
  const classes = useStyles();
  const {
    text,
    setText,
    error,
    setError,
    successSnackbar,
    errorSnackbar,
    setSuccessSnackbar,
    setErrorSnackbar
  } = useSnackbar();

  const onSuccess = (val = 'El archivo se cargó correctamente') => {
    setText(val);
    setSuccessSnackbar(true);
    setErrorSnackbar(false);
  };

  const onError = (err = 'Error al importar archivo') => {
    setError(err);
    setSuccessSnackbar(false);
    setErrorSnackbar(true);
  };

  return (
    <Page className={classes.root} title="Preparar Liquidaciones de Pago">
      {renderRoutes(route.routes, { onSuccess, onError })}
      <SnackbarManager
        handleRoute={false}
        error={error}
        text={text}
        successSnackbar={successSnackbar}
        errorSnackbar={errorSnackbar}
        setErrorSnackbar={setErrorSnackbar}
        setSuccessSnackbar={setSuccessSnackbar}
      />
    </Page>
  );
};

PrepareSettlementPage.propTypes = {
  route: PropTypes.shape({
    path: PropTypes.string,
    routes: PropTypes.array,
    component: PropTypes.func
  }).isRequired
};

export { PrepareSettlementPage, CajaLosAndesDiscountsPage, FonasaDiscountsPage };
