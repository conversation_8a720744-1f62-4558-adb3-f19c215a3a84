/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import React, { useState, useEffect } from 'react';
import useOnlineStatus from '@rehooks/online-status';
import ImportButton from 'components/ImportButton';
import Table from 'components/Table';
import { Grid, Fade } from '@material-ui/core';
import MuiAlert from '@material-ui/lab/Alert';
import useRouter from 'utils/useRouter';
import { useSelector, useDispatch } from 'react-redux';

import { useProgress } from 'components';
import styles from './styles';
import LinkButton from './linkButton';
import { renderTableData, renderTableHeader } from './table';
import ScientificNotationError from './scientificNotationWarning';
import AnnulateButton from './AnnulateButton';
import useDataUpload from '../hooks/useDataUpload';
import { cancelLinkedPensions } from '../../pensions/services/pensioner.service';
import { checkCronExecution } from '../services/temporary.service';
import { cleanFileError, setHasScientificNotationError, cleanFile } from '../actions';
import { checkWritePermission } from '../../../utils/checkUserPermission';

const CronMark = 'TRANSFER_PENSIONS';

const Alert = props => <MuiAlert elevation={6} variant="filled" {...props} />;

const renderAlert = () => {
  const variant = 'filled';
  const severity = 'info';
  const message = `El enlace de nuevos pensionados se puede realizar hasta el último día del mes actual`;

  return (
    <div>
      <Fade in timeout={{ enter: 1000 }}>
        <Alert severity={severity} variant={variant}>
          {message}
        </Alert>
      </Fade>
    </div>
  );
};

const TemporaryPensions = ({ setErrorLinkedSnackbar, role }) => {
  const fileUploadInput = React.createRef();
  const router = useRouter();
  const classes = styles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();
  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const result = useSelector(store => store.temporaryPensions.data);
  const fileName = useSelector(store => store.temporaryPensions.fileName);
  const fileErrors = useSelector(store => store.temporaryPensions.fileErrors);
  const hasScientificNotationError = useSelector(
    store => store.temporaryPensions.hasScientificNotationError
  );
  const disabledLink = useSelector(store => store.pensions.isLinked);
  const actionsAllowed = useSelector(store => store.pensions.actionIsAllowed);

  const hasWritePermission = checkWritePermission(role);

  const [isCronExecuted, setIsCronExecuted] = useState(false);

  const handleLinkPensions = async () => {
    router.history.push('/nuevas-pensiones/resumen?link=true');
  };

  const handleUploadButton = () => {
    fileUploadInput.current.value = '';
    fileUploadInput.current.click();
  };

  const getLinkBtnTooltipText = () => {
    let text = disabledLink
      ? 'El enlace de nuevos pensionados del mes ya fue realizado, al iniciar el próximo mes se habilitará nuevamente el enlace'
      : 'Enlazar pensiones';
    if (!isCronExecuted && !disabledLink)
      text = 'No se realizó el enlace de pensiones del mes actual';
    return text;
  };

  const handleFileUpload = useDataUpload(dispatch, router, progress, setErrorLinkedSnackbar);

  const handleCancelPensions = () => {
    dispatch(cancelLinkedPensions());
    dispatch(cleanFile());
  };

  useEffect(() => {
    dispatch(setHasScientificNotationError(false));
    dispatch(cleanFileError());
    checkCronExecution(CronMark)(setIsCronExecuted);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {renderAlert()}
      <Grid
        className={classes.formControl}
        container
        direction="row"
        justifyContent="space-around"
        alignItems="center"
      >
        <Grid item>
          <ImportButton
            classes={{ formControl: classes.formControl }}
            errorMessage={fileErrors}
            onClick={handleUploadButton}
            footer={fileName ? `Archivo importado: ${fileName}` : 'Formato CSV max 10MB'}
            disabled={!hasWritePermission || !onlineStatus || !actionsAllowed || disabledLink}
          />
        </Grid>
        <Grid item>
          <Grid container justifyContent="space-around" spacing={2}>
            {disabledLink && (
              <Grid item>
                <AnnulateButton
                  linkText="Anular enlace de nuevos pensionados"
                  disabledLink={
                    !hasWritePermission || !onlineStatus || !actionsAllowed || !disabledLink
                  }
                  handleCancelPensions={handleCancelPensions}
                />
              </Grid>
            )}
            <Grid item>
              <LinkButton
                linkText={getLinkBtnTooltipText()}
                disabledLink={
                  !hasWritePermission || !onlineStatus || !actionsAllowed || disabledLink
                }
                data={result}
                handleLinkPensions={handleLinkPensions}
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {hasScientificNotationError && <ScientificNotationError />}
      <Table renderHeader={renderTableHeader} renderTable={() => renderTableData(result)} />
      <input
        ref={fileUploadInput}
        type="file"
        style={{ display: 'none' }}
        id="fileUpload"
        disabled={!hasWritePermission || !onlineStatus}
        onChange={v => v.target.files.length && handleFileUpload(v.target.files[0])}
        accept=".csv"
      />
    </>
  );
};

export default TemporaryPensions;
