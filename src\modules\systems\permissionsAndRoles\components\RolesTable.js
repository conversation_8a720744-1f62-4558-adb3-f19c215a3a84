import React from 'react';
import PropTypes from 'prop-types';

import { useHistory } from 'react-router-dom';
import useOnlineStatus from '@rehooks/online-status';

import MaterialTable from 'material-table';
import { RemoveRedEye } from '@material-ui/icons';

const Roles = ({ data, title }) => {
  const onlineStatus = useOnlineStatus();

  const history = useHistory();

  const seeMore = rowData => {
    const { roleName } = rowData;
    history.push({
      pathname: `/sistemas/roles-y-permisos/${roleName}`,
      state: rowData
    });
  };

  return (
    <MaterialTable
      columns={[
        {
          title: 'Nombre del Rol',
          field: 'roleName',
          sorting: false
        }
      ]}
      title={title}
      data={data}
      options={{
        search: false,
        showTitle: !!title,
        actionsColumnIndex: -1,
        paging: false,
        toolbar: false,
        draggable: false,
        maxBodyHeight: 700
      }}
      localization={{
        body: {},
        header: {
          actions: 'Permisos'
        }
      }}
      editable={{
        isEditable: () => false
      }}
      actions={[
        () => ({
          icon: RemoveRedEye,
          tooltip: 'Permisos',
          onClick: (event, rowData) => seeMore(rowData),
          disabled: !onlineStatus
        })
      ]}
    />
  );
};

Roles.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  title: PropTypes.string
};

Roles.defaultProps = {
  title: ''
};

export default Roles;
