import { axiosRequest } from '../../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

export const getNationalHolidaysReport = async () => {
  const { data, isError } = await axiosRequest
    .get(`${api}/temporaryBonusPensioners/download/`)
    .catch(_err => {
      return { data: null, isError: true };
    });
  return { data, isError };
};

export const sendBonusPensioners = file =>
  axiosRequest
    .post(`${api}/temporaryBonusPensioners/import`, { fileString: file })
    .catch(error => ({ data: {}, isError: true, error }));
