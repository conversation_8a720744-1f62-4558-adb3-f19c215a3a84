import { makeStyles } from '@material-ui/styles';
import { colors } from '@material-ui/core';

const styles = makeStyles(theme => ({
  root: {
    // eslint-disable-next-line no-magic-numbers
    padding: theme.spacing(3)
  },
  tabs: {
    // eslint-disable-next-line no-magic-numbers
    marginTop: theme.spacing(3)
  },
  headerButtons: {
    margin: 12
  },
  formControl: {
    margin: 30,
    paddingTop: 15
  },
  divider: {
    // eslint-disable-next-line no-magic-numbers
    backgroundColor: colors.grey[300]
  },
  content: {
    // eslint-disable-next-line no-magic-numbers
    marginTop: theme.spacing(3)
  },
  footerButton: {
    display: 'flex',
    justifyContent: 'center',
    margin: '1rem'
  },
  headerImport: {
    background: '#006531',
    '& div': {
      '& span': {
        color: 'white'
      }
    }
  },
  principalErrors: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: '1rem',
    marginBottom: '1rem'
  },
  acceptButton: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.primary.light,
      color: theme.palette.primary.contrastText
    }
  },
  cancelButton: {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.error.light,
      color: theme.palette.error.contrastText
    }
  }
}));

export default styles;
