import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Button, Grid } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import { HeaderContent, Table } from '../../../../components';
import { renderTableHeader, renderTableData } from './table';
import Bonus from './bonus';
import useStyles from '../../style';
import {
  fetchMinimunPensionsAndBonuses,
  modifyMinimunPensionsAndBonuses
} from '../service/minimun-pensions-and-bonus.service';
import { checkWritePermission } from '../../../../utils/checkUserPermission';

const findErrors = errs => Object.values(errs).some(v => /true/i.test(JSON.stringify(v)));

// eslint-disable-next-line react/prop-types
const MinimunPensionsAndBonus = ({ onSuccessSnackbar, role }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const [state, setState] = useState([]);
  const [hasError, setHasError] = useState({});
  const [isEditable, setIsEditable] = useState(false);
  const [reload, setReload] = useState(true);

  const hasWritePermission = checkWritePermission(role);
  const handleSave = async () => {
    try {
      setIsEditable(false);
      const fixedState = state.map(v => {
        if (v.type !== 'pension') return v;
        const { value } = v;
        const valueList = Object.keys(value);
        valueList.forEach(item => {
          const fixedValue = value[item].replace(',', '.');
          value[item] = new Intl.NumberFormat(['es', 'de'], {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
            useGrouping: false
          })
            .format(fixedValue)
            .replace(',', '.');
        });
        return { ...v, value };
      });

      const { completed, message } = await modifyMinimunPensionsAndBonuses(fixedState);
      if (completed) {
        onSuccessSnackbar(message);
      }
    } catch (error) {
      console.log('err handleSave', error);
    }
    setReload(true);
  };

  useEffect(() => {
    async function getData() {
      try {
        setReload(false);
        const fetchResult = await fetchMinimunPensionsAndBonuses();
        if (!fetchResult.length) {
          throw new Error('Servicio no disponible.');
        }
        const fixedResults = fetchResult.map(v => {
          if (v.type !== 'pension') return v;
          const { value } = v;
          const valueList = Object.keys(value);
          valueList.forEach(item => {
            value[item] = value[item] ? value[item].replace('.', ',') : value[item];
          });
          return { ...v, value };
        });

        setState([...fixedResults]);
      } catch (error) {
        console.log('err getData', error);
      }
    }
    if (reload) {
      getData();
    }
  }, [reload]);

  return (
    <>
      <Grid>
        <HeaderContent title="Pensiones mínimas y aguinaldos" />
      </Grid>
      <Grid container direction="row" justify="flex-end" spacing={1}>
        <Grid item>
          <Grid container spacing={2}>
            <Grid item>
              <Button
                variant={isEditable ? 'text' : 'contained'}
                size="small"
                color="primary"
                onClick={() => setIsEditable(true)}
                disabled={!hasWritePermission || !onlineStatus || isEditable || !state.length}
              >
                Editar
              </Button>
            </Grid>
            <Grid item>
              <Button
                variant={isEditable ? 'contained' : 'text'}
                size="small"
                color="primary"
                onClick={() => handleSave()}
                disabled={!onlineStatus || !isEditable || findErrors(hasError)}
              >
                Guardar
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Table
        className={classes.formControl}
        renderHeader={renderTableHeader}
        renderTable={() =>
          renderTableData(
            state.filter(({ type }) => type === 'pension'),
            setState,
            hasError,
            setHasError,
            isEditable
          )
        }
      />
      <Grid className={classes.formControl}>
        <HeaderContent title="Bonos y aguinaldos" />
      </Grid>
      <Grid container  justify="space-around" className={classes.inputContainer} spacing={3}>
        <Bonus
          values={state.filter(({ type }) => type === 'bonus')}
          setState={setState}
          hasError={hasError}
          setHasError={setHasError}
          isEditable={isEditable}
          
        />
      </Grid>
    </>
  );
};

MinimunPensionsAndBonus.propTypes = {
  onSuccessSnackbar: PropTypes.func.isRequired
};

export default MinimunPensionsAndBonus;
