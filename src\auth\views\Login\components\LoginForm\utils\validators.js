/* eslint-disable no-magic-numbers */
import { isValidEmail, hasValue, validate } from '../../../../../../utils/validators';

const MSG_ERROR = 'El valor es inválido';
const MSG_ERROR_REQUIRED = 'El campo es obligatorio';

const validatorsOnChange = {
  email: [
    { fn: hasValue, error: MSG_ERROR_REQUIRED },
    { fn: isValidEmail, error: MSG_ERROR }
  ],
  password: [{ fn: hasValue, error: MSG_ERROR_REQUIRED }]
};

const validatorsOnBlur = {
  email: [
    { fn: hasValue, error: MSG_ERROR_REQUIRED },
    { fn: isValidEmail, error: MSG_ERROR }
  ],
  password: [{ fn: hasValue, error: MSG_ERROR_REQUIRED }]
};

const validateOnChange = validate(validatorsOnChange);
const validateOnBlur = validate(validatorsOnBlur);

export { validateOnChange, validateOnBlur };
