/* eslint-disable no-magic-numbers */
import { makeStyles } from '@material-ui/styles';

const styles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(0)
  },
  containerTable: {
    width: '600px'
  },
  gridStyleSearchPensioner: {
    flexDirection: 'row'
  },
  textField: {
    marginRight: 10,
    width: 245
  },
  componentTitle: {
    color: 'grey',
    fontWeight: 'bold',
    fontSize: '14px',
    marginRight: '10px',
    marginBottom: '10px',
    marginTop: '10px'
  },
  link: {
    color: 'blue'
  }
}));

export default styles;
