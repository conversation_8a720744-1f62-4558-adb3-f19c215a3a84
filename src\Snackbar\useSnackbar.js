import { useState } from 'react';

// eslint-disable-next-line import/prefer-default-export
export const useSnackbar = () => {
  const [open, setOpen] = useState(false);
  const [stylePreset, setStylePreset] = useState('info');
  const [message, setMessage] = useState('');
  const [onClose, setOnClose] = useState(() => { /*any*/ });
  const [actions, setActions] = useState();

  return {
    open,
    setOpen,
    stylePreset,
    setStylePreset,
    message,
    setMessage,
    onClose,
    setOnClose,
    actions,
    setActions
  };
};
