import React from 'react';
import { Card, Grid, Typography } from '@material-ui/core';

import styles from './styles';

export const ScientificNotationWarning = () => {
  const classes = styles();

  const MAIN_MESSAGE =
    'Valor inválido (E+n) en las columnas NUMERO DE CUENTA y/o CUN, seguir los siguientes pasos para corregir:';
  const STEP_ONE = '1.- Abrir archivo original en formato Excel';
  const STEP_TWO = '2.- Aplicar formato tipo número a las columnas NUMERO DE CUENTA y/o CUN';
  const STEP_THREE = '3.- Guardar archivo en formato csv (delimitado por comas) ';

  return (
    <Grid container justify="center">
      <Card className={classes.scientificWarningContainer}>
        <Typography className={classes.scientificWarningMessage}>{MAIN_MESSAGE}</Typography>
        <Typography className={classes.scientificBulletPoints}>{STEP_ONE}</Typography>
        <Typography className={classes.scientificBulletPoints}>{STEP_TWO}</Typography>
        <Typography className={classes.scientificBulletPoints}>{STEP_THREE}</Typography>
      </Card>
    </Grid>
  );
};

export default ScientificNotationWarning;
