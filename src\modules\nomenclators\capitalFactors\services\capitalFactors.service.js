/* eslint-disable import/prefer-default-export */
/* eslint-disable no-console */

import {
  setCurrentMonthYear,
  setCurrentYear,
  setHasFactorsFile,
  setHasConcurrenciesFile
} from '../actions';

import getErrorMessage from '../utils/errorManager';
import { axiosRequest, axiosCSV } from '../../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

//------------------------------------------------------------------------------------------------
export const getMonthYear = async dispatch => {
  const { data } = await axiosRequest.get(`${api}/quadrature/time`).catch(err => {
    console.error(err);
    return { data: '-', isError: true };
  });
  const [month, year] = data.split('-');
  dispatch(setCurrentMonthYear(`${month}${year}`));
  dispatch(setCurrentYear(year));
  return data;
};
//------------------------------------------------------------------------------------------------
export const wasDataUploaded = async dispatch => {
  const {
    data: {
      wasDataUploaded: { factors, concurrencies }
    }
  } = await axiosRequest.get(`${api}/quadrature/was-data-uploaded`).catch(err => {
    console.error(err);
    return { data: { wasDataUploaded: { factors: false, concurrencies: false } }, isError: true };
  });
  dispatch(setHasFactorsFile(factors));
  dispatch(setHasConcurrenciesFile(concurrencies));
  return { factors, concurrencies };
};
//------------------------------------------------------------------------------------------------
export const sendFactorData = async factors => {
  const {
    data: { dataInsertion },
    errorMessage
  } = await axiosRequest
    .post(`${api}/quadrature/insert-quadrature-data/factors`, { data: factors })
    .catch(err => {
      console.error(err);
      return { data: { dataInsertion: false }, errorMessage: getErrorMessage(err) };
    });
  return [dataInsertion, errorMessage];
};
//------------------------------------------------------------------------------------------------
export const sendConcurencyData = async concurrencies => {
  const {
    data: { dataInsertion }
  } = await axiosRequest
    .post(`${api}/quadrature/insert-quadrature-data/concurrencies`, { data: concurrencies })
    .catch(err => {
      console.error(err);
      return { data: { dataInsertion: false } };
    });
  return dataInsertion;
};
//------------------------------------------------------------------------------------------------
export const downloadFactorCSV = async _dispatch => {
  const { data, isError } = await axiosCSV
    .get(`${api}/quadrature/get-quadrature-data/factors`)
    .catch(err => {
      console.error(err);
      return { data: {}, isError: true };
    });
  return { data, isError };
};
//------------------------------------------------------------------------------------------------
export const downloadConcurrenciesCSV = async _dispatch => {
  const { data, isError } = await axiosCSV
    .get(`${api}/quadrature/get-quadrature-data/concurrencies`)
    .catch(err => {
      console.error(err);
      return { data: {}, isError: true };
    });
  return { data, isError };
};
