#!/bin/bash

COMMANDS="cd $FOLDER-$BITBUCKET_DEPLOYMENT_ENVIRONMENT;"
COMMANDS="$COMMANDS pm2 restart $PM2_CONFIGURATION-$BITBUCKET_DEPLOYMENT_ENVIRONMENT.json --env $BITBUCKET_DEPLOYMENT_ENVIRONMENT --update-env";
COMMANDS="$COMMANDS exit;";

scp $PM2_CONFIGURATION-$BITBUCKET_DEPLOYMENT_ENVIRONMENT.json $USERNAME@$SERVER_IP:$FOLDER-$BITBUCKET_DEPLOYMENT_ENVIRONMENT
scp -r build $USERNAME@$SERVER_IP:$FOLDER-$BITBUCKET_DEPLOYMENT_ENVIRONMENT
ssh $USERNAME@$SERVER_IP "${COMMANDS}"

echo "Purging Cloudflare cache"
curl -X DELETE "https://api.cloudflare.com/client/v4/zones/$CLOUDFLARE_ZONE_ID/purge_cache" \
-H "X-Auth-Email: $CLOUDFLARE_EMAIL" \
-H "X-Auth-Key: $CLOUDFLARE_KEY" \
-H "Content-Type: application/json" \
--data '{"purge_everything":true}'

echo "Deployment finished in $BRANCH :)"