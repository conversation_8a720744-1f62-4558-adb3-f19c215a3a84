import {
  WAS_<PERSON><PERSON>A_SEARCHED,
  SET_PENSIONS_DATA,
  SET_WAS_A_FIELD_MODIFIED,
  SET_MODIFIED_FIELD,
  SET_PENSIONER_PDF_FILE,
  SET_WAS_PDF_UPLOADED,
  SET_AFP_LIST,
  SET_BAN<PERSON><PERSON><PERSON>CH_LIST,
  SET_SERVIPAG_LIST,
  SET_INSTITUTIONAL_PATIENT,
  SET_AFP_AFFILIATION,
  SET_PENSION_TYPE,
  SET_PENSION_TYPE_DUE_TO_CHARGES,
  SET_BENEFICIARY_EMAIL,
  SET_DATA_SUCCESSFULLY_UPDATED,
  SET_PAYMENT_GATEWAY,
  SET_<PERSON>NK,
  SET_ACCOUNT_NUMBER,
  SET_BRANCH_OFFICE,
  SET_MODIFIED_FIELD_ERRORS,
  SET_COLLECTOR_ADDRESS,
  SET_COLLECTOR_CITY,
  SET_COLLECTOR_COMMUNE,
  SET_COLLECTOR_NAME,
  SET_COLLECTOR_LASTNAME,
  SET_COLLECTOR_MOTHERS_LASTNAME,
  SET_COLLECTOR_RUT,
  SET_WAS_INFO_LOADED,
  SET_BENEFICIARY_PHONE,
  SET_CUN,
  SET_GENDER,
  SET_COUNTRY,
  SET_ACCIDENT_DATE,
  SET_IS_FIRST_CHANGE_IN_COLLECTOR_RUT,
  SET_AGREES_TO_CHANGE_COLLECTOR_RUT,
  SET_FOR_FAMILY_ASSIGNMENT,
  SET_ARTICLE_40,
  SET_HEALTH_LOAN,
  SET_BANK_REJECTED,
  SET_PAYCHECK_REFUNDED,
  SET_DEPENDENCY_EXECUTED,
  SET_VALIDITY_TYPE,
  SET_INACTIVATION_REASON,
  SET_END_DATE_OF_VALIDITY,
  SET_END_DATE_OF_THEORICAL_VALIDITY,
  SET_INACTIVATION_DATE,
  SET_EVALUATION_DATE,
  SET_REACTIVATE_MANUALLY,
  SET_REACTIVATION_DATE,
  SET_CURRENT_DATE,
  SET_IS_INACTIVATION_REASON_CHANGE,
  SET_COLLECTOR_RETENTION_RUT,
  SET_COLLECTOR_RETENTION_ADDRESS,
  SET_COLLECTOR_RETENTION_COMMUNE,
  SET_COLLECTOR_RETENTION_NAME,
  SET_COLLECTOR_RETENTION_LASTNAME,
  SET_COLLECTOR_RETENTION_CITY,
  SET_COLLECTOR_RETENTION_MOTHERS_LASTNAME,
  SET_RETENTION_PAYMENT_GATEWAY,
  SET_RETENTION_BANK,
  SET_RETENTION_ACCOUNT_NUMBER,
  SET_RETENTION_BRANCH_OFFICE,
  SET_FORMULABLE_LABEL,
  SET_FORMULABLE_ASSET_TYPE,
  SET_FORMULABLE_AMOUNT,
  SET_FORMULABLE_REASON,
  SET_FORMULABLE_TYPE_RETENTION,
  SET_FORMULABLE_VALIDITY,
  SET_FORMULABLE_AMOUNT_RETENTION,
  SET_FORMULABLE_START_DATE,
  SET_FORMULABLE_END_DATE,
  SET_RETENCION_VISIBILITY,
  SET_RETENCION_DISCOUNT_AND_ASSETS,
  SET_BASEPENSION,  
  SET_PAYMENT_GATEWAY_OLD,
  ATTACH_NEUROLOGICAL_CERTIFICATE
} from './actions';

const initialState = {
  wasDataSearched: false,
  data: [],
  pensionerInfo: {},
  wasModifiedAFieldThatRequiresAFile: false,
  modifiedField: {},
  pensionerPdfFile: {},
  successfulFileUpload: false,
  afpList: [],
  bankBranchOfficeList: [],
  servipagBranchOfficeList: [],
  institutionalPatient: '',
  afpAffiliation: '',
  pensionType: '',
  ChangeOfPensionTypeDueToCharges: false,
  beneficiaryEmail: '',
  paymentGateway: '',
  bank: '',
  branchOffice: '',
  accountNumber: '',
  collectorRut: '',
  collectorName: '',
  collectorLastName: '',
  collectorMothersLastName: '',
  collectorCity: '',
  collectorAddress: '',
  collectorCommune: '',
  isFirstChangeInCollectorRut: true,
  agreesToChangeCollectorRut: false,
  modifiedFieldErrors: {},
  dataSuccessfullyUpdated: false,
  wasInfoLoaded: false,
  forFamilyAssignment: '',
  article40: '',
  healthLoan: '',
  beneficiaryPhone: '',
  cun: '',
  gender: '',
  country: '',
  bankRejected: 'No',
  paycheckRefunded: 'No',
  accidentDate: new Date(),
  dependencyExecuted: false,
  validityType: '',
  inactivationReason: '',
  endDateOfValidity: new Date(),
  endDateOfTheoricalValidity: new Date(),
  inactivationDate: new Date(),
  evaluationDate: new Date(),
  reactivationDate: new Date(),
  manuallyReactivated: false,
  currentDate: new Date(),
  isInactivationReasonChange: false,
  collectorRetentionRut: '',
  collectorRetentionName: '',
  collectorRetentionLastName: '',
  collectorRetentionMothersLastName: '',
  collectorRetentionCity: '',
  collectorRetentionAddress: '',
  collectorRetentionCommune: '',
  retentionPaymentGateway: '',
  retentionBank: '',
  retentionAccountNumber: '',
  retentionBranchOffice: '',
  formulableLabel: '',
  formulableAssetType: '',
  formulableAmount: 0,
  formulableReason: '',
  formulableTypeRetention: '',
  formulableValidity: '',
  formulableAmountRetention: 0,
  formulableStartDate: '',
  formulableEndDate: '',
  setRetentionVisibility: false,
  discountsAndAssets: '',
  basePension : 0,
  paymentGatewayOld: undefined,
  attachNeurologicalCertificate: false
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case WAS_DATA_SEARCHED:
      return {
        ...state,
        wasDataSearched: action.data
      };
    case SET_PENSIONS_DATA:
      return {
        ...state,
        data: action.data
      };
    case SET_WAS_A_FIELD_MODIFIED:
      return {
        ...state,
        wasModifiedAFieldThatRequiresAFile: action.data
      };
    case SET_MODIFIED_FIELD:
      return {
        ...state,
        modifiedField: action.data
      };
    case SET_PENSIONER_PDF_FILE:
      return {
        ...state,
        pensionerPdfFile: action.data
      };
    case SET_WAS_PDF_UPLOADED:
      return {
        ...state,
        successfulFileUpload: action.data
      };
    case SET_AFP_LIST:
      return {
        ...state,
        afpList: action.data
      };
    case SET_BANKBRANCH_LIST:
      return {
        ...state,
        bankBranchOfficeList: action.data
      };
    case SET_SERVIPAG_LIST:
      return {
        ...state,
        servipagBranchOfficeList: action.data
      };
    case SET_INSTITUTIONAL_PATIENT:
      return {
        ...state,
        institutionalPatient: action.data
      };
    case SET_AFP_AFFILIATION:
      return {
        ...state,
        afpAffiliation: action.data
      };
    case SET_PENSION_TYPE:
      return {
        ...state,
        pensionType: action.data
      };

    case SET_PENSION_TYPE_DUE_TO_CHARGES:
      return {
        ...state,
        ChangeOfPensionTypeDueToCharges: action.data
      };
    case SET_DATA_SUCCESSFULLY_UPDATED:
      return {
        ...state,
        dataSuccessfullyUpdated: action.data
      };
    case SET_PAYMENT_GATEWAY:
      return {
        ...state,
        paymentGateway: action.data
      };
    case SET_BANK:
      return {
        ...state,
        bank: action.data
      };
    case SET_ACCOUNT_NUMBER:
      return {
        ...state,
        accountNumber: action.data
      };
    case SET_BRANCH_OFFICE:
      return {
        ...state,
        branchOffice: action.data
      };
    case SET_BENEFICIARY_EMAIL:
      return {
        ...state,
        beneficiaryEmail: action.data
      };
    case SET_COLLECTOR_RUT:
      return {
        ...state,
        collectorRut: action.data
      };
    case SET_COLLECTOR_ADDRESS:
      return {
        ...state,
        collectorAddress: action.data
      };
    case SET_COLLECTOR_COMMUNE:
      return {
        ...state,
        collectorCommune: action.data
      };
    case SET_COLLECTOR_NAME:
      return {
        ...state,
        collectorName: action.data
      };
    case SET_COLLECTOR_LASTNAME:
      return {
        ...state,
        collectorLastName: action.data
      };
    case SET_COLLECTOR_CITY:
      return {
        ...state,
        collectorCity: action.data
      };
    case SET_COLLECTOR_MOTHERS_LASTNAME:
      return {
        ...state,
        collectorMothersLastName: action.data
      };
    case SET_IS_FIRST_CHANGE_IN_COLLECTOR_RUT:
      return {
        ...state,
        isFirstChangeInCollectorRut: action.data
      };
    case SET_AGREES_TO_CHANGE_COLLECTOR_RUT:
      return {
        ...state,
        agreesToChangeCollectorRut: action.data
      };
    case SET_MODIFIED_FIELD_ERRORS:
      return {
        ...state,
        modifiedFieldErrors: action.data
      };
    case SET_WAS_INFO_LOADED:
      return {
        ...state,
        wasInfoLoaded: action.data
      };
    case SET_FOR_FAMILY_ASSIGNMENT:
      return {
        ...state,
        forFamilyAssignment: action.data
      };
    case SET_ARTICLE_40:
      return {
        ...state,
        article40: action.data
      };
    case SET_HEALTH_LOAN:
      return {
        ...state,
        healthLoan: action.data
      };
    case SET_BENEFICIARY_PHONE:
      return {
        ...state,
        beneficiaryPhone: action.data
      };
    case SET_CUN:
      return {
        ...state,
        cun: action.data
      };
    case SET_GENDER:
      return {
        ...state,
        gender: action.data
      };
    case SET_COUNTRY:
      return {
        ...state,
        country: action.data
      };
    case SET_ACCIDENT_DATE:
      return {
        ...state,
        accidentDate: action.data
      };
    case SET_BANK_REJECTED:
      return {
        ...state,
        bankRejected: action.data
      };
    case SET_PAYCHECK_REFUNDED:
      return {
        ...state,
        paycheckRefunded: action.data
      };
    case SET_DEPENDENCY_EXECUTED:
      return {
        ...state,
        dependencyExecuted: action.data
      };
    case SET_VALIDITY_TYPE:
      return {
        ...state,
        validityType: action.data
      };
    case SET_INACTIVATION_REASON:
      return {
        ...state,
        inactivationReason: action.data
      };
    case SET_END_DATE_OF_VALIDITY:
      return {
        ...state,
        endDateOfValidity: action.data
      };
    case SET_END_DATE_OF_THEORICAL_VALIDITY:
      return {
        ...state,
        endDateOfTheoricalValidity: action.data
      };
    case SET_INACTIVATION_DATE:
      return {
        ...state,
        inactivationDate: action.data
      };
    case SET_EVALUATION_DATE:
      return {
        ...state,
        evaluationDate: action.data
      };
    case SET_REACTIVATE_MANUALLY:
      return {
        ...state,
        manuallyReactivated: action.data
      };
    case SET_REACTIVATION_DATE:
      return {
        ...state,
        reactivationDate: action.data
      };
    case SET_CURRENT_DATE:
      return {
        ...state,
        currentDate: action.data
      };
    case SET_IS_INACTIVATION_REASON_CHANGE:
      return {
        ...state,
        isInactivationReasonChange: action.data
      };
    case SET_COLLECTOR_RETENTION_RUT:
      return {
        ...state,
        collectorRetentionRut: action.data
      };
    case SET_COLLECTOR_RETENTION_ADDRESS:
      return {
        ...state,
        collectorRetentionAddress: action.data
      };
    case SET_COLLECTOR_RETENTION_COMMUNE:
      return {
        ...state,
        collectorRetentionCommune: action.data
      };
    case SET_COLLECTOR_RETENTION_NAME:
      return {
        ...state,
        collectorRetentionName: action.data
      };
    case SET_COLLECTOR_RETENTION_LASTNAME:
      return {
        ...state,
        collectorRetentionLastName: action.data
      };
    case SET_COLLECTOR_RETENTION_CITY:
      return {
        ...state,
        collectorRetentionCity: action.data
      };
    case SET_COLLECTOR_RETENTION_MOTHERS_LASTNAME:
      return {
        ...state,
        collectorRetentionMothersLastName: action.data
      };
      case SET_RETENTION_PAYMENT_GATEWAY:
        return {
          ...state,
          retentionPaymentGateway: action.data
        };
      case SET_RETENTION_BANK:
        return {
          ...state,
          retentionBank: action.data
        };
      case SET_RETENTION_ACCOUNT_NUMBER:
        return {
          ...state,
          retentionAccountNumber: action.data
        };
      case SET_RETENTION_BRANCH_OFFICE:
        return {
          ...state,
          retentionBranchOffice: action.data
        };
      case SET_FORMULABLE_LABEL:
        return {
          ...state,
          formulableLabel: action.data
        };   
      case SET_FORMULABLE_ASSET_TYPE:
        return {
          ...state,
          formulableAssetType: action.data
        };  
      case SET_FORMULABLE_AMOUNT:
        return {
          ...state,
          formulableAmount: action.data
        }; 
      case SET_FORMULABLE_REASON:
        return {
          ...state,
          formulableReason: action.data
        }; 
      case SET_FORMULABLE_TYPE_RETENTION:
        return {
          ...state,
          formulableTypeRetention: action.data
        };
      case SET_FORMULABLE_VALIDITY:
        return {
          ...state,
          formulableValidity: action.data
        };
      case SET_FORMULABLE_AMOUNT_RETENTION:
        return {
          ...state,
          formulableAmountRetention: action.data
        };
      case SET_FORMULABLE_START_DATE:
        return {
          ...state,
          formulableStartDate: action.data
        };   
      case SET_FORMULABLE_END_DATE:
        return {
          ...state,
          formulableEndDate: action.data
        };   
      case SET_RETENCION_VISIBILITY:
        return {
          ...state,
          retentionVisibility: action.data
        };  
      case SET_RETENCION_DISCOUNT_AND_ASSETS:
        return {
          ...state,
          discountsAndAssets: action.data
        }; 
      case SET_BASEPENSION:
        return {
          ...state,
          basePension: action.data
        };   
      case SET_PAYMENT_GATEWAY_OLD:
        return {
          ...state,
          paymentGatewayOld: action.data
        };   
      case ATTACH_NEUROLOGICAL_CERTIFICATE:
        return {
          ...state,
          attachNeurologicalCertificate: action.data
        };                                                
    default:
      return state;
  }
}
