/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import MaterialTable from 'material-table';
import PropTypes from 'prop-types';
import { Typography } from '@material-ui/core';
import useStyles from '../styles';

const headerStyles = {
  fontWeight: 'bold',
  borderWidth: '1px',
  borderColor: 'black',
  textAlign: 'center'
};

const cellStyles = {
  padding: 3,
  borderColor: 'black',
  textAlign: 'center'
};

const statusCellStyles = (statusColor = 'black') => {
  return { padding: 3, borderColor: 'black', textAlign: 'center', color: statusColor };
};

export default function DueDates({ title, data }) {
  const classes = useStyles();

  return (
    <div className={classes.containerTable}>
      <Typography className={classes.componentTitle}>{title}</Typography>

      <MaterialTable
        columns={[
          {
            title: 'Actividad',
            field: 'activity',
            sorting: false,
            headerStyle: headerStyles,
            cellStyle: cellStyles
          },
          {
            title: 'Fecha comprometida',
            field: 'dueDate',
            sorting: false,
            emptyValue: '-',
            headerStyle: headerStyles,
            cellStyle: cellStyles
          },
          {
            title: 'Días restantes',
            field: 'remainingDays',
            sorting: false,
            emptyValue: '-',
            headerStyle: headerStyles,
            cellStyle: cellStyles
          },
          {
            title: 'Estado',
            field: 'statusText',
            sorting: false,
            emptyValue: '-',
            headerStyle: headerStyles,
            cellStyle: (_, rowData) => statusCellStyles(rowData?.statusColor)
          }
        ]}
        title={title}
        data={data}
        options={{
          search: false,
          showTitle: !!title,
          actionsColumnIndex: -1,
          paging: false,
          toolbar: false,
          draggable: false,
          maxBodyHeight: 600
        }}
        localization={{
          header: {
            actions: ''
          }
        }}
        editable={{
          isEditable: () => false
        }}
      />
    </div>
  );
}

DueDates.propTypes = {
  title: PropTypes.string,
  data: PropTypes.arrayOf(PropTypes.shape({}))
};

DueDates.defaultProps = {
  title: null,
  data: [{}]
};
