/* eslint-disable no-console */

import { isPensionsAvailable } from '../actions';
import { axiosRequest } from '../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const apiURL = REACT_APP_API_URL;

//------------------------------------------------------------------------------------------------
const isInAvailableDays = ({ api, axiosRequest }) =>
  axiosRequest
    .get(`${api}/pensions/actionisallowed`)
    .then(({ data: values }) => ({ ...values }))
    .catch(err => {
      console.error('err:', err);
      return { data: { actionIsAllowed: false, inDayRange: false, totalDays: -1 } };
    });

const isAvailableLink = ({ api, axiosRequest }) =>
  axiosRequest
    .get(`${api}/pensions/link`)
    .then(({ data: { linked } }) => linked)
    .catch(err => {
      console.error(err);
      return { data: { linked: false } };
    });

export const pensionsLinkEnabledAndCancelable = () => async (
  dispatch,
  _getState,
  { api, axiosRequest }
) => {
  const [isLinked, { actionIsAllowed, inDayRange, totalDays }] = await Promise.all([
    isAvailableLink({ api, axiosRequest }),
    isInAvailableDays({ api, axiosRequest })
  ]);
  const attrs = { isLinked, actionIsAllowed, inDayRange, totalDays };
  dispatch(isPensionsAvailable(attrs));
  return attrs;
};

//------------------------------------------------------------------------------------------------
export const linkPensioners = () => async (_dispatch, _getState, { api, axiosRequest }) => {
  const {
    data: { linked }
  } = await axiosRequest.post(`${api}/pensions/link`).catch(err => {
    // eslint-disable-next-line no-console
    console.error('err:', err);
    return { data: { linked: 0 } };
  });
  return linked;
};
//------------------------------------------------------------------------------------------------
export const cancelLinkedPensions = () => async (dispatch, _getState, { api, axiosRequest }) => {
  const {
    data: { isCancelled }
  } = await axiosRequest.delete(`${api}/pensions/link/annulment`).catch(err => {
    console.error('err:', err);
    return { data: { isCancelled: false } };
  });
  dispatch(isPensionsAvailable({ isLinked: false }));
  return isCancelled;
};
//------------------------------------------------------------------------------------------------
export const updatePension = async (
  { beneficiaryRut, causantRut, pensionCodeId },
  dataToUpdate
) => {
  const {
    data: { completed }
  } = await axiosRequest
    .put(`${apiURL}/pensions/updateAssetsAndDiscountOfPension`, {
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      ...dataToUpdate
    })
    .catch(err => {
      // eslint-disable-next-line no-console
      console.error('err:', err);
      return { data: { completed: false } };
    });
  return completed;
};
