const CODE_LENGTH = 3;
const codeRegex = /(\d{3})/;

const isValidCode = (code = '') => {
  const number = code.toString().replace(/[^0-9]/gi, '');
  return codeRegex.test(number) && number.length === CODE_LENGTH ? number : '';
};
const codeFormatter = code =>
  code
    ? code
        .toString()
        .replace(/[^0-9]+/gi, '')
        .replace(/(\d{3})/gi, '$1')
    : '';

const codeMatchRule = code => isValidCode(code);

const defaultFormatter = (value = '') =>
  value
    .replace(/^\s|[^a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûçğş0-9.',´`^¨~-\s]/gi, '')
    .replace(/\s+/g, ' ');

export { codeFormatter, codeMatchRule, defaultFormatter };
