### Introduction

We like to think that Devias Kit Pro is more of a starter kit or the foundation to build your project upon. Devias Kit Pro is written in React with hooks that uses some really important technologies:

##### Material-UI

<a href="https://material-ui.com/" target="_blank">Material-UI</a> is a React ui components library that implements Google's Material Design design specifications.

##### Create React App (CLI)

<a href="https://facebook.github.io/create-react-app/docs/getting-started" target="_blank">Create React App</a> is an officially supported way to create single-page React applications. It offers a modern build setup with no configuration.

##### Node JS

To install and use Devias Kit Pro, you will need <a  href="https://nodejs.org/en/" target="_blank">Node.js</a> installed on your machine. We highly encourage to have the latest node version to as we are developing & updateing only for the latest releases of Node Js.

##### Package manager

We use <a href="https://yarnpkg.com/en/" target="_blank">Yarn</a> to install and manage third-party libraries. You can use any alternatives ( <a href="https://www.npmjs.com/"  target="_blank">npm</a> ).

### Installation

##### A. Installing Prerequisites

- Download and install the latest version of Node.js from their official website.
- Download and install the latest Yarn version from their official website.

##### B. Installing Devias Kit Pro

1. Unzip the zip file that you have downloaded from Material-Ui. Inside the zip file, you will find the the source file (exactly this demo project) (react-material-kit-pro-x.x.x-.zip) and design folder where you will find two more folders for figma and sketch source files.
2. Extract the contents of the zip file (react-material-kit-pro-x.x.x-.zip) into a folder that you will work within. For this documentation, we will refer that as "your work folder".
3. Open your machine console application (Terminal, Command Prompt etc.), navigate into your work folder and run the following command and wait for it to finish:

```bash
yarn
```

4. After the installation is complete write in the terminal

```bash
yarn start
```

5. Open your browser and navigate to `localhost:3000`
