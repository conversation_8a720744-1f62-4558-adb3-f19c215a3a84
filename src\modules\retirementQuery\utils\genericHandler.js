const genericHandle = (
  e,
  {
    dispatch,
    action,
    actionError,
    modifiedFieldErrors,
    key,
    validation,
    maxLength,
    data,
    isRequired
  }
) => {
  const { value } = e.target;
  const keepErrors = isRequired && value.length === 0;
  if (value === data[key]) {
    const { [key]: removedError, ...otherErrorFields } = modifiedFieldErrors;
    dispatch(action(value));
    keepErrors && dispatch(actionError({ ...otherErrorFields }));
    return value;
  }
  dispatch(action(value));
  if (validation && (!validation.test(value) || value.length > maxLength)) {
    actionError && dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));
    return value;
  }
  actionError && dispatch(actionError({ ...modifiedFieldErrors, [key]: false }));
  return value;
};

export default genericHandle;
