/* eslint-disable import/prefer-default-export */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  select,
  scaleBand,
  scaleLinear,
  scaleOrdinal,
  max,
  axisBottom,
  stack,
  symbol,
  symbolTriangle
} from 'd3';
import { millionFormat } from '../../utils/formatters';

const numberFormatter = number =>
  new Intl.NumberFormat('es-CL', {
    maximumFractionDigits: 0
  }).format(number);

const calculatePercentageDiff = (secondLastMonthSpent, lastMonthSpent) => {
  if (!secondLastMonthSpent) return '0,0 %';
  const percentageChange =
    // eslint-disable-next-line no-magic-numbers
    ((+lastMonthSpent - +secondLastMonthSpent) / +secondLastMonthSpent) * 100;
  return `${parseFloat(isNaN(percentageChange) ? '0' : percentageChange)
    .toFixed(1)
    .replace(/\./, ',')} %`;
};

const isEmptyData = data => !data.some(({ totalSpent }) => totalSpent);

const calcDiffBetweeLastTwoMonths = monthlyData => {
  const positionsToGet = -2;
  const [secondLastMonthData, lastMonthData] = monthlyData.slice(positionsToGet);

  const absDiff = lastMonthData.totalSpent - secondLastMonthData.totalSpent;
  const percentageDiff = calculatePercentageDiff(
    secondLastMonthData.totalSpent,
    lastMonthData.totalSpent
  );
  const secondLastMonth = secondLastMonthData.month;
  const lastMonth = lastMonthData.month;
  const radius = 25;

  return [{ absDiff, percentageDiff, secondLastMonth, lastMonth, radius }];
};

const StackBarPlot = ({ data, groups, colorObj }) => {
  const d3Container = useRef();

  const stackColor = colorObj.filter((_item, index) => index > 0).reverse();

  const svgWidth = 600;
  const svgHeight = 250;
  const margin = { top: 120, right: 50, bottom: 35, left: 50 };
  const graphWidth = svgWidth - margin.left - margin.right;
  const graphHeight = svgHeight - margin.top - margin.bottom;
  const innerPadding = 0.3;
  const outerPadding = 0.1;
  const half = 0.5;

  useEffect(() => {
    const svg = select(d3Container.current)
      .append('svg')
      .attr('width', svgWidth)
      .attr('height', svgHeight);

    const graph = svg
      .append('g')
      .attr('width', graphWidth)
      .attr('height', graphHeight)
      .attr('transform', `translate(${margin.left}, ${margin.top})`);

    const xAxisGroup = graph.append('g').attr('transform', `translate(0, ${graphHeight})`);

    const x = scaleBand()
      .domain(data.map(d => d.month))
      .range([0, graphWidth])
      .paddingInner(innerPadding)
      .paddingOuter(outerPadding);

    const y = scaleLinear()
      .domain([0, max(data, d => d.totalSpent)])
      .range([graphHeight, 0]);

    const listOfColors = stackColor.map(item => item.color);

    const color = scaleOrdinal()
      .domain(groups.slice(1))
      .range(listOfColors);

    const lineNumberSeparation = 10;
    const dotPlot = svg.selectAll('.line').data(data);

    const enterDotPlot = dotPlot
      .enter()
      .append('g')
      .attr('transform', `translate(${margin.left},40)`);

    enterDotPlot
      .append('circle')
      .attr('cx', d => x(d.month) + x.bandwidth() * half)
      .attr('cy', -lineNumberSeparation)
      .attr('r', '3')
      .attr('fill', '#000000');

    enterDotPlot
      .append('text')
      .attr('x', d => x(d.month) + x.bandwidth() * half)
      .attr('y', '-17')
      .style('text-anchor', 'middle')
      .text(d => numberFormatter(`${d.validPensioners}`));

    const [first] = data;
    const [last] = data.slice(-1);

    enterDotPlot
      .append('line')
      .attr('x1', x(first.month) + x.bandwidth() * half)
      .attr('y1', -lineNumberSeparation)
      .attr('x2', x(last.month) + x.bandwidth() * half)
      .attr('y2', -lineNumberSeparation)
      .attr('stroke', 'black')
      .attr('stroke-width', '2')
      .attr('fill', 'none')
      .style('stroke-dasharray', ('1', '3'));

    const stackedData = stack().keys(groups)(data);
    const rects = graph.selectAll('g').data(stackedData);

    rects
      .enter()
      .append('g')
      .attr('fill', (_d, i) => color(stackColor[i - 1].color))
      .selectAll('rect')
      .data(d => d)
      .enter()
      .append('rect')
      .attr('x', d => x(d.data.month))
      .attr('y', d => y(d[1]))
      .attr('height', d => y(d[0]) - y(d[1]))
      .attr('width', x.bandwidth());

    rects
      .enter()
      .append('g')
      .selectAll('text')
      .data(d => d)
      .enter()
      .append('text')
      .attr('x', d => x(d.data.month) + x.bandwidth() * half)
      .attr('y', d => y(d[0] - (d[0] - d[1]) * half))
      .attr('dy', '0.3em')
      .style('text-anchor', 'middle')
      .style('fill', 'white')
      .attr('font-family', 'arial')
      .style('font-size', '11px')
      .text(d => (d[1] - d[0] > 0 ? millionFormat(`${d[1] - d[0]}`) : ''));

    const diffBetweenLastTwo = calcDiffBetweeLastTwoMonths(data);
    const verticalLineLength = 30;

    const circleText = svg.selectAll('g myCircleText').data(diffBetweenLastTwo);

    const circleEnter = circleText
      .enter()
      .append('g')
      .attr(
        'transform',
        _d => `translate(${margin.left + x.bandwidth() * half},${margin.top * half})`
      );

    circleEnter
      .append('line')
      .attr('x1', d => x(d.secondLastMonth))
      .attr('y1', 0)
      .attr('x2', d => x(d.lastMonth))
      .attr('y2', 0)
      .attr('stroke', 'black')
      .attr('stroke-width', 1)
      .attr('fill', 'none');

    circleEnter
      .append('line')
      .attr('x1', d => x(d.secondLastMonth))
      .attr('y1', 0)
      .attr('x2', d => x(d.secondLastMonth))
      .attr('y2', verticalLineLength)
      .attr('stroke', 'black')
      .attr('stroke-width', 1)
      .attr('fill', 'none');

    circleEnter
      .append('line')
      .attr('x1', d => x(d.lastMonth))
      .attr('y1', 0)
      .attr('x2', d => x(d.lastMonth))
      .attr('y2', verticalLineLength)
      .attr('stroke', 'black')
      .attr('stroke-width', 1)
      .attr('fill', 'none');

    circleEnter
      .append('ellipse')
      .attr('cy', 0)
      .attr('cx', d => (x(d.lastMonth) + x(d.secondLastMonth)) * half)
      .attr('rx', d => d.radius)
      .attr('ry', d => d.radius * '0.8')
      .attr('stroke', 'black')
      .attr('fill', 'white');

    circleEnter
      .append('path')
      .attr(
        'd',
        symbol()
          .type(symbolTriangle)
          .size('50')
      )
      .attr('transform', d => `translate(${x(d.lastMonth)},${verticalLineLength}) rotate(180)`);

    circleEnter
      .append('text')
      .attr('x', d => (x(d.lastMonth) + x(d.secondLastMonth)) * half)
      .text(d => numberFormatter(`${d.absDiff}`))
      .style('text-anchor', 'middle')
      .attr('dy', '-4')
      .attr('font-family', 'arial')
      .style('font-size', '11px');

    circleEnter
      .append('text')
      .attr('x', d => (x(d.lastMonth) + x(d.secondLastMonth)) * half)
      .text(d => d.percentageDiff)
      .style('text-anchor', 'middle')
      .attr('dy', '9')
      .attr('font-family', 'arial')
      .style('font-size', '11px');

    const textOfTotalValues = svg.selectAll('.totalSpent').data(data);
    textOfTotalValues
      .enter()
      .append('text')
      .attr('class', 'totalSpent')
      .attr('x', d => x(d.month) + x.bandwidth() * half)
      .attr('y', d => (isEmptyData(data) ? '93' : y(d.totalSpent) - 1))
      .style('text-anchor', 'middle')
      .attr('font-family', 'arial')
      .style('font-size', '13px')
      .text(d => (!isNaN(d.totalSpent) ? millionFormat(`${d.totalSpent}`) : null))
      .attr('transform', `translate(${margin.left}, ${margin.top})`);

    const xAxis = axisBottom(x);
    xAxisGroup.call(xAxis);
  }, []);

  return <div ref={d3Container} />;
};

StackBarPlot.propTypes = {
  data: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  groups: PropTypes.arrayOf(PropTypes.string).isRequired,
  colorObj: PropTypes.arrayOf(PropTypes.shape({})).isRequired
};

export default StackBarPlot;
