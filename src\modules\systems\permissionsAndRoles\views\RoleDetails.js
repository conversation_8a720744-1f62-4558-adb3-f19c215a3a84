/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { Fragment } from 'react';

import { useParams, Link as RouterLink } from 'react-router-dom';
import useOnlineStatus from '@rehooks/online-status';
import { Grid, Typography } from '@material-ui/core';

import Header from '../../components/Header';
import RoleDetailsTable from '../components/RoleDetailsTable';
import styles from './styles';
import { updateRole } from '../services/roles.service';

const TITLE = 'Modulos y permisos del sistema';
const PARENT_DOMAIN = 'Configuraciones';
const CHILD_DOMAIN = 'Sistemas';
const PARENT_CHILD_DOMAIN = `${PARENT_DOMAIN} / ${CHILD_DOMAIN}`;

const RoleDetails = ({ onSuccessSnackbar, onErrorSnackbar, role: userRole }) => {
  const { role } = useParams();
  const onlineStatus = useOnlineStatus();
  const classes = styles();

  const handleProcess = (okMessage, errorMessage) => async (newData, newPermission) => {
    const {
      view: { _id }
    } = newData;
    const { completed } = await updateRole({ roleName: role, view: _id, newPermission });
    if (completed) {
      onSuccessSnackbar(okMessage);
      return { completed };
    }
    onErrorSnackbar(errorMessage);
    return { completed };
  };

  return (
    <Fragment>
      <Grid className={classes.root}>
        <Grid item xs={12}>
          {onlineStatus ? (
            <Typography className={classes.subtitle} color="textSecondary">
              {/* eslint-disable-next-line react/jsx-one-expression-per-line */}

              {`${PARENT_DOMAIN} `}

              <RouterLink
                to={{
                  pathname: '/sistemas/roles-y-permisos'
                }}
              >
                {`/ ${CHILD_DOMAIN}`}
              </RouterLink>
            </Typography>
          ) : (
            <Typography className={classes.subtitle} color="textSecondary">
              {/* eslint-disable-next-line react/jsx-one-expression-per-line */}
              {PARENT_CHILD_DOMAIN}
            </Typography>
          )}
        </Grid>
        <Grid>
          <Header title={TITLE} />
          <Typography className={classes.role}>{`rol: ${role}`}</Typography>
        </Grid>

        <Grid>
          <RoleDetailsTable
            role={role}
            userRole={userRole}
            onUpdate={handleProcess('Rol actualizado correctamente', 'Error al actualizar el rol')}
          />
        </Grid>
      </Grid>
    </Fragment>
  );
};

export default RoleDetails;
