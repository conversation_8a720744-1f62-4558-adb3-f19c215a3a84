/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { Fragment, useState, useEffect } from 'react';
import { Grid } from '@material-ui/core';
import { useDispatch, useSelector } from 'react-redux';
import Header from '../../components/Header';
import MonitorTable from '../components/MonitorTable';
import { processJob, loadMonitor } from '../services/monitor.service';
import { getRoles } from '../../permissionsAndRoles/services/roles.service';

import styles from './styles';

const TITLE = 'Visualización de estados de crons';
const SUBTITLE = 'Configuraciones / Sistemas';

const Monitor = ({ onSuccessSnackbar, onErrorSnackbar, role: userRole }) => {
  const dispatch = useDispatch();
  const classes = styles();

  const data = useSelector(store => store.monitorJobs.data);

  const [enabledButton, setEnabledButton] = useState(false);
  const [roles, setRoles] = useState([]);
  const [role, setRole] = useState('');

  const onRoleFilterChange = ({ target }) => {
    const { value } = target;
    if (!value) return setRole('');
    return setRole(value);
  };

  useEffect(() => {
    const fetchData = async () => {
      await getRoles(setRoles);
      loadMonitor(dispatch);
    };
    fetchData();
  }, []);

  const handleProcess = (okMessage, erroMessage, operation) => async (newData, oldData) => {
    try {
      if (operation === 'ejecutar' || operation === 'saltar') {
        const result = await dispatch(processJob(oldData, newData, operation))
          .then(() => {
            onSuccessSnackbar(okMessage);
            return true;
          })
          .catch(error => {
            let messageError;
            messageError = erroMessage;
            onErrorSnackbar(messageError);
            setEnabledButton(true);
            throw new Error(messageError);
          });
        console.log(result);
        if (!result) return false;
      }
      return true;
    } catch (error) {
      onErrorSnackbar(error.message);
      setEnabledButton(true);
      return false;
    }
  };

  return (
    <Fragment>
      <Grid className={classes.root}>
        <Header title={TITLE} subtitle={SUBTITLE} />
        <Grid className={classes.table}>
          <MonitorTable
            userRole={userRole}
            enabledButton={enabledButton}
            data={data}
            roles={roles}
            onRoleFilterChange={onRoleFilterChange}
            selectedRole={role}
            onExecuteJob={handleProcess(
              'Trabajo se ejecuto correctamente',
              'Error al ejecutar trabajo',
              'ejecutar'
            )}
            onSkipJob={handleProcess(
              'Se registra marca de ejecución',
              'Error al registrar marca de ejecución',
              'saltar'
            )}
          />
        </Grid>
      </Grid>
    </Fragment>
  );
};

export default Monitor;
