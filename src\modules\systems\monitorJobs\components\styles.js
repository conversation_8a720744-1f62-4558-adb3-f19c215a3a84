import { makeStyles } from '@material-ui/styles';

const styles = makeStyles(_theme => ({
  iconButtonStyle: {
    textTransform: 'none'
  },
  gridContainerStyle: {
    height: '0px'
  },
  gridStyle: {
    height: '0px',
    marginTop: '-60px'
  },
  role: { fontSize: 16, fontWeight: 500, marginRight: '20px' },
  table: {
    paddingTop: '60px'
  },
  divEditRow: {
    width: '267px'
  },
  matTable: {
    //overflow-x: scroll
    overflowX: 'scroll'
  },
  labelSmall: {
    fontSize: 'xx-small'
  }
}));

export default styles;
