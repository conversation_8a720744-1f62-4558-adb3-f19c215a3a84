const httpStatus = {
  BAD_REQUEST: 400,
  INTERNAL_SERVER_ERROR: 500
};

const SERVER_ERROR_MESSAGE = 'Importación fallida';
const BAD_REQUEST_MESSAGE = 'Existen datos duplicados en archivo de factores';

const isBadRequest = status => status === httpStatus.BAD_REQUEST;

const getErrorMessage = error =>
  isBadRequest(error.response.status) ? BAD_REQUEST_MESSAGE : SERVER_ERROR_MESSAGE;

export default getErrorMessage;
