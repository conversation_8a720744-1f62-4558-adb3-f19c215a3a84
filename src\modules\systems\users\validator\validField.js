// Taken from https://gist.github.com/rotvulpix/69a24cc199a4253d058c

const word = '0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäöüëïâêîôûçğş';
const regex = `^([${word}\\.\\-',])+(\\s[${word}\\.\\-',]+)*$`;
// eslint-disable-next-line no-misleading-character-class
const textFieldRegRule = new RegExp(regex, 'i');
const EMAIL_PATTERN = /(^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$)/i;

const defaultFormatter = (value = '') =>
  value
    .replace(/^\s|[^a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûçğş0-9.',´`^¨~-\s]/gi, '')
    .replace(/\s+/g, ' ');

const emailFormatter = (value = '') => value.replace(/[^a-z0-9\\@.\\_\\-]/gi, '');

const matchRequired = (value = '') => !!value;
const matchName = (value = '') => textFieldRegRule.test(value);
const matchEmail = (value = '') => EMAIL_PATTERN.test(value);
const matchRole = (value = '') => !!`${value}`.trim();

export {
  textFieldRegRule,
  defaultFormatter,
  emailFormatter,
  matchRequired,
  matchName,
  matchEmail,
  matchRole
};
