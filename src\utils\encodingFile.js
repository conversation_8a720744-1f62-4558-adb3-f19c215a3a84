const NOT_VALID_CSV_CHARS = /[^\s\n+\-*/=@#&%$!¡¿?ºª.:;,_|><´`¨""{})'^[\]~áéíóúàèìòùãẽĩõñũỹg̃äöüëïâêîôûçğşa-z0-9]/im;
const getEncoding = file => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async () => {
      const { result } = reader;
      const encoding = NOT_VALID_CSV_CHARS.test(result) ? 'ISO-8859-1' : 'UTF-8';
      resolve(encoding);
    };
    reader.onerror = () => {
      return reject(new Error('Error al leer el archivo.'));
    };
    reader.readAsText(file);
  });
};

export { getEncoding };
