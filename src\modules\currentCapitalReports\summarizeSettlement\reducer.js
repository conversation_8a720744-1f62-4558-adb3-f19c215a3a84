import {
  SET_IS_DOWNLOADING,
  SET_IS_VALID_DATE_RANGE,
  SET_CURRENT_DATE_REPORTS,
  SET_IS_VALID_DATE,
  SET_IS_VALID_STARTING_DATE,
  SET_IS_VALID_ENDING_DATE
} from './actions';

const initialState = {
  isDownloading: false,
  isValidDateRange: true,
  currentDate: '',
  isValidStartingDate: true,
  isValidEndingDate: true,
  validDate: true
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case SET_IS_DOWNLOADING:
      return {
        ...state,
        isDownloading: action.data
      };
    case SET_IS_VALID_DATE_RANGE:
      return {
        ...state,
        isValidDateRange: action.data
      };
    case SET_CURRENT_DATE_REPORTS:
      return {
        ...state,
        currentDate: action.data
      };
    case SET_IS_VALID_DATE:
      return {
        ...state,
        validDate: action.data
      };
    case SET_IS_VALID_STARTING_DATE:
      return {
        ...state,
        isValidStartingDate: action.data
      };
    case SET_IS_VALID_ENDING_DATE:
      return {
        ...state,
        isValidEndingDate: action.data
      };
    default:
      return state;
  }
}
