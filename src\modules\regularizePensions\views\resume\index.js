/* eslint-disable react/prop-types */
/* eslint-disable react/forbid-prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Typography, CardHeader, Card, Button } from '@material-ui/core';
import { useDispatch } from 'react-redux';
import useRouter from '../../../../utils/useRouter';

import useStyles from '../styles';
import ResumeError from './resumeError';
import { cleanFamilyAssignmentResumeErrors, cleanOrphanhoodResumeErrors } from '../../actions';

const Resume = props => {
  const router = useRouter();
  const dispatch = useDispatch();
  const { fileDataError, isError, source } = props;
  const classes = useStyles();

  const req = source === 'horphanhood' ? 'Pension, Causante, Beneficiario, Vigencia' 
  : 'Rut carga, Rut causante, <PERSON>ut cobrante, Tipo de vigencia de la carga, Tipo de pensión, Monto por asignación familiar, Retroactivo asignación familiar';

  const handleOnClick = () => {
    if (isError) {
      if (source === 'horphanhood')
      {
        dispatch(cleanOrphanhoodResumeErrors());
        router.history.push('/regularizar-pensiones/inactivar-reactivar/orfandad');
      }
      else{
        dispatch(cleanFamilyAssignmentResumeErrors());
        router.history.push('/regularizar-pensiones/inactivar-reactivar/reasignacion-familiar');
      }
      
    }
    
  };

  return (
    <Card className={classes.content}>
      <CardHeader title="Resumen de importación" className={classes.headerImport} />
      <>
        <div className={classes.principalErrors}>
          <Typography>
            <b>Existen errores en el archivo que impidieron su importación</b>
          </Typography>
        </div>
        <ResumeError
          errors={fileDataError.errors}
          requireFields={req}
        />
      </>
      <div className={classes.footerButton}>
        <Button onClick={handleOnClick} className={classes.acceptButton}>
          Aceptar
        </Button>
      </div>
    </Card>
  );
};

Resume.propTypes = {
  fileDataError: PropTypes.object,
  isError: PropTypes.bool,
  source : PropTypes.string
};
Resume.defaultProps = {
  fileDataError: { errors: [] },
  isError: false,
  source : ''
};

export default Resume;
