import { getPensionCertificateData } from '../services/queryPensions.service';
import formatPDFData from '../utils/formatPDFData';

const errorMesage = 'Hubo un error al descargar el archivo';

const getPDFData = async ({
  beneficiaryRut,
  causantRut,
  pensionCodeId,
  setIsCurrentlyDownloading,
  progress,
  onErrorSnackbar,
  setPensionCertificateData,
  pensionerData
}) => {
  progress.show();
  setIsCurrentlyDownloading(true);
  const { data, isError } = await getPensionCertificateData({
    beneficiaryRut,
    causantRut,
    pensionCodeId
  });
  if (isError) {
    onErrorSnackbar(errorMesage);
    progress.hide();
    setIsCurrentlyDownloading(false);
    return;
  }
  setPensionCertificateData(formatPDFData({ ...pensionerData, ...data }));
};

const handleDownloadPDFData = ({
  beneficiaryRut,
  causantRut,
  pensionCodeId,
  setIsCurrentlyDownloading,
  progress,
  onErrorSnackbar,
  setPensionCertificateData,
  pensionerData
}) => {
  return () =>
    getPDFData({
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      setIsCurrentlyDownloading,
      progress,
      onErrorSnackbar,
      setPensionCertificateData,
      pensionerData
    });
};

export default handleDownloadPDFData;
