import {
  CREATE_NOM_SERVIPAG,
  DELETE_NOM_SERVIPAG,
  UPDATE_NOM_SERVIPAG,
  GET_SERVIPAG
} from './actions';

const initialState = {
  data: [],
  errors: []
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case CREATE_NOM_SERVIPAG: {
      const { servipag } = action.data;
      return {
        ...state,
        data: [...state.data, servipag]
      };
    }
    case UPDATE_NOM_SERVIPAG: {
      const data = [...state.data];
      data[data.indexOf(action.data.prevServipag)] = action.data.newServipag;
      return { ...state, data };
    }

    case GET_SERVIPAG: {
      const results = action.data;
      return {
        ...state,
        data: results
      };
    }
    case DELETE_NOM_SERVIPAG: {
      const data = [...state.data];
      data.splice(data.indexOf(action.data.servipag), 1);
      return { ...state, data };
    }
    default:
      return state;
  }
}
