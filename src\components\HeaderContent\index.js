import React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import { Typography } from '@material-ui/core';

const useStyles = makeStyles(theme => ({
  root: {},
  dates: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end'
  },
  startDateButton: {
    marginRight: theme.spacing(1)
  },
  endDateButton: {
    marginLeft: theme.spacing(1)
  },
  calendarTodayIcon: {
    marginRight: theme.spacing(1)
  }
}));

const HeaderContent = props => {
  const { className, title, subtitle, overline, ...rest } = props;

  const classes = useStyles();

  return (
    <div {...rest} className={clsx(classes.root, className)}>
      <Typography component="h2" gutterBottom variant="overline">
        {overline}
      </Typography>
      <Typography component="h1" gutterBottom variant="h3">
        {title}
      </Typography>
      <Typography variant="subtitle1">{subtitle}</Typography>
    </div>
  );
};

HeaderContent.propTypes = {
  className: PropTypes.string,
  title: PropTypes.string,
  subtitle: PropTypes.string,
  overline: PropTypes.string
};

HeaderContent.defaultProps = {
  className: '',
  title: 'Default Title',
  overline: '',
  subtitle: ''
};

export default HeaderContent;
