/* eslint-disable react/forbid-prop-types */
import React, { useEffect } from 'react';

import { useSelector } from 'react-redux';
// eslint-disable-next-line import/no-unresolved
import useRouter from 'utils/useRouter';

import Resume from '../resume';

const FamilyAssignmentResume = _props => {
  const router = useRouter();

  const isError = useSelector(store => store.regularizedPensions.isError);
  const errors = useSelector(store => store.regularizedPensions.errors);

  useEffect(() => {
    if (errors.length === 0 && !isError) {
      router.history.push('/regularizar-pensiones/inactivar-reactivar/reasignacion-familiar/');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [errors, isError]);

  return <Resume isError={isError} fileDataError={{ errors }} />;
};

export default FamilyAssignmentResume;
