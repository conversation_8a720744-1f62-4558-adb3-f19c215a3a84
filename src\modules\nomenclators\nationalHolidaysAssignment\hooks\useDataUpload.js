import { validateFile, readFile } from './txtFile';
import { cleanFileError, setFileError, setHolidaysFileDataError } from '../actions';
import { sendBonusPensioners } from '../services/nationalHolidaysService';

const ERROR_FILE_BASE64 = 'Ocurrió un error al leer el archivo';
const IMPORT_SUCCESS = 'Archivo importado correctamente';

const formatedErrors = [];

const getContentFileBase64 = async file => {
  let dataBase64 = null;
  let isErrorBase64 = false;

  const getBase64 = file => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };

  await getBase64(file)
    .then(result => ([, dataBase64] = result.split(',')))
    .catch(_error => (isErrorBase64 = true));

  return { dataBase64, isErrorBase64 };
};

const getErrors = errors => {
  errors.forEach(error => {
    getErrorDetail(error);
  });
  return formatedErrors;
};

const getErrorDetail = error => {
  error.errorFields.forEach((field, index) => {
    formatedErrors.push({
      id: index + error.lineNumber.toString(),
      row: error.lineNumber,
      message: field.message
    });
  });
};

const useDataUpload = ({
  dispatch,
  router,
  progress,
  onSuccessSnackbar,
  onErrorSnackbar
}) => async file => {
  dispatch(cleanFileError());
  formatedErrors.length = 0;
  if (validateFile(file, err => dispatch(setFileError(err)))) {
    const errorsFile = await readFile(file).catch(err => {
      dispatch(setFileError(err.message));
      return true;
    });

    if (errorsFile) return;

    const { dataBase64, isErrorBase64 } = await getContentFileBase64(file);
    if (isErrorBase64) {
      onErrorSnackbar(ERROR_FILE_BASE64);
      return;
    }
    progress.show();

    const { isError, error } = await sendBonusPensioners(dataBase64);
    if (isError) {
      const errors = error?.response?.data?.result;
      const formatedErrors = getErrors(errors);
      dispatch(setHolidaysFileDataError(formatedErrors));
      progress.hide();
      router.history.push('/mantenedores/asignacion-aguinaldos/resumen');
      return;
    }

    onSuccessSnackbar(IMPORT_SUCCESS);
    progress.hide();
  }
};
export default useDataUpload;
