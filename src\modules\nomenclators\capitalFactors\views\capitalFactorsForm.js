/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable no-console */
/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import useOnlineStatus from '@rehooks/online-status';
import { Button, Grid, TextField } from '@material-ui/core';
import { useProgress } from 'components';
import React, { createRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import useRouter from 'utils/useRouter';
import { useHistory } from 'react-router-dom';

import useStyles from './styles';
import ActionButton from '../components/ActionButton';
import {
  getMonthYear,
  wasDataUploaded,
  downloadFactorCSV,
  downloadConcurrenciesCSV
} from '../services/capitalFactors.service';
import uploadFile from '../hooks/useUpload';
import UseSendData from '../hooks/useSendData';
import useDownloadCSV from '../hooks/useDownloadCSV';
import {
  setFactorsFilenameError,
  setConcurrenciesFilenameError,
  setFactorsFileData,
  setConcurrenciesFileData
} from '../actions';
import { checkWritePermission } from '../../../../utils/checkUserPermission';

const EXTENSION = '.csv';
const FACTORS_TYPE = 'factores';
const CONCURRENCIES_TYPE = 'concurrencias';

const CapitalFactorsForm = ({ onSuccessSnackbar, onErrorSnackbar, role }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const router = useRouter();
  const dispatch = useDispatch();
  const history = useHistory();

  const hasWritePermission = checkWritePermission(role);
  const {
    location: { state }
  } = history;

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const currentMonthYear = useSelector(store => store.capitalFactors.currentMonthYear);
  const currentYear = useSelector(store => store.capitalFactors.currentYear);

  const factorsFilenameError = useSelector(store => store.capitalFactors.factorsFilenameError);
  const concurrenciesFilenameError = useSelector(
    store => store.capitalFactors.concurrenciesFilenameError
  );
  const fileError = useSelector(store => store.capitalFactors.fileError);

  const factorsFileData = useSelector(store => store.capitalFactors.factorsFileData);
  const concurrenciesFileData = useSelector(store => store.capitalFactors.concurrenciesFileData);

  const hasFactorsFile = useSelector(store => store.capitalFactors.hasFactorsFile);
  const hasConcurrenciesFile = useSelector(store => store.capitalFactors.hasConcurrenciesFile);

  const isCurrentlyDownloading = useSelector(store => store.capitalFactors.isCurrentlyDownloading);

  const factorsFileInput = createRef();
  const concurrenciesFileInput = createRef();

  const buttonIsDisabled = () => !onlineStatus || isCurrentlyDownloading;

  const handleClickFactorsButton = () => {
    factorsFileInput.current.value = '';
    factorsFileInput.current.click();
  };

  const handleClickConcurrenciesButton = () => {
    concurrenciesFileInput.current.value = '';
    concurrenciesFileInput.current.click();
  };

  const handleFactorsFileUpload = file =>
    uploadFile({
      file,
      type: FACTORS_TYPE,
      dispatch,
      currentMonthYear,
      currentYear,
      router,
      progress
    });

  const handleConcurrenciesFileUpload = file =>
    uploadFile({
      file,
      type: CONCURRENCIES_TYPE,
      dispatch,
      currentMonthYear,
      currentYear,
      router,
      progress
    });

  const handleSendData = UseSendData({
    factorsFileData,
    concurrenciesFileData,
    progress,
    dispatch,
    onSuccessSnackbar,
    onErrorSnackbar
  });

  const handleDownloadFactorCSV = useDownloadCSV({
    dispatch,
    factorsFileData,
    progress,
    service: downloadFactorCSV,
    filename: `Factores_${currentYear}`
  });

  const handleDownloadConcurrencyCSV = useDownloadCSV({
    dispatch,
    concurrenciesFileData,
    progress,
    service: downloadConcurrenciesCSV,
    filename: `Concurrencias_${currentMonthYear}`
  });

  const cleanState = () => {
    dispatch(setFactorsFilenameError(''));
    dispatch(setConcurrenciesFilenameError(''));
    dispatch(setFactorsFileData([]));
    dispatch(setConcurrenciesFileData([]));
  };

  useEffect(() => {
    const { isReturning = '' } = state || {};
    if (!isReturning) {
      cleanState();
    }
    getMonthYear(dispatch);
    wasDataUploaded(dispatch);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Grid item>
        <TextField
          label={currentYear && 'Importar Factores'}
          className={factorsFileData.length ? classes.textfieldLoaded : classes.textfield}
          value={currentYear ? `Factores_${currentYear}.csv` : 'Importar Factores'}
          helperText={factorsFilenameError}
          error={factorsFilenameError}
          variant="outlined"
          size="small"
          disabled
        />
        <ActionButton
          classes={{ formControl: classes.formControl }}
          onClick={handleClickFactorsButton}
          disabled={!hasWritePermission || buttonIsDisabled()}
          footer=""
          isDownload
          text={!hasWritePermission || buttonIsDisabled() ? '' : 'Importar'}
        />
        <input
          ref={factorsFileInput}
          type="file"
          style={{ display: 'none' }}
          id="factorsFileInput"
          disabled={buttonIsDisabled()}
          onChange={v => v.target.files.length && handleFactorsFileUpload(v.target.files[0])}
          accept={EXTENSION}
        />
        <ActionButton
          classes={{ formControl: classes.formControl }}
          onClick={handleDownloadFactorCSV}
          disabled={buttonIsDisabled() || !hasFactorsFile}
          footer=""
          text={buttonIsDisabled() ? '' : 'Descargar'}
        />
      </Grid>
      <Grid item>
        <TextField
          label={currentMonthYear && 'Importar Concurrencias'}
          className={concurrenciesFileData.length ? classes.textfieldLoaded : classes.textfield}
          value={
            currentMonthYear ? `Concurrencias_${currentMonthYear}.csv` : 'Importar Concurrencias'
          }
          helperText={concurrenciesFilenameError}
          error={concurrenciesFilenameError}
          variant="outlined"
          size="small"
          disabled
        />
        <ActionButton
          classes={{ formControl: classes.formControl }}
          onClick={handleClickConcurrenciesButton}
          disabled={!hasWritePermission || buttonIsDisabled()}
          footer=""
          isDownload
          text={!hasWritePermission || buttonIsDisabled() ? '' : 'Importar'}
        />
        <input
          ref={concurrenciesFileInput}
          type="file"
          style={{ display: 'none' }}
          id="concurrenciesFileInput"
          disabled={buttonIsDisabled()}
          onChange={v => v.target.files.length && handleConcurrenciesFileUpload(v.target.files[0])}
          accept={EXTENSION}
        />
        <ActionButton
          classes={{ formControl: classes.formControl }}
          onClick={handleDownloadConcurrencyCSV}
          disabled={buttonIsDisabled() || !hasConcurrenciesFile}
          footer=""
          text={buttonIsDisabled() ? '' : 'Descargar'}
        />
      </Grid>
      <Grid className={classes.finalize}>
        <Button
          className={classes.finalize}
          onClick={handleSendData}
          disabled={
            buttonIsDisabled() ||
            (!factorsFileData.length && !concurrenciesFileData.length) ||
            factorsFilenameError.length ||
            concurrenciesFilenameError.length ||
            fileError.length
          }
          variant="contained"
          size="medium"
          color="primary"
        >
          Finalizar
        </Button>
      </Grid>
    </>
  );
};

export default CapitalFactorsForm;
