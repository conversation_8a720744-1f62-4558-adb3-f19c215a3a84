# This is a sample build configuration for JavaScript.
# Check our guides at https://confluence.atlassian.com/x/14UWN for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: node:10.15.3

clone:
  depth: full # SonarCloud scanner needs the full history to assign issues properly

definitions:
  caches:
    sonar: ~/.sonar/cache # Caching SonarCloud artifacts will speed up your build
  steps:
    - step: &build-test
        name: Build achs-prestaciones-frontend App
        script:
          - npm ci
          - npm run build
    - step: &build-deploy
        name: build and Deploy to Environment
        script:
          - echo $BITBUCKET_DEPLOYMENT_ENVIRONMENT
          - npm ci
          - npm run build:$BITBUCKET_DEPLOYMENT_ENVIRONMENT
          - chmod u+x ./scripts/pipeline.sh
          - ./scripts/pipeline.sh
          - echo "Deploy in :" $URL_NAME

pipelines:
  default:
    - step: *build-test
  custom:
    deploy-to-qa-1:
      - step:
          <<: *build-deploy
          deployment: qa1
    deploy-to-qa-2:
      - step:
          <<: *build-deploy
          deployment: qa2
    deploy-to-qa:
      - step:
          <<: *build-deploy
          deployment: qa
    pull-requests:
    '**':
      - step:
          <<: *build-test
  branches:
    master:
      - step:
          <<: *build-test
          deployment: production
