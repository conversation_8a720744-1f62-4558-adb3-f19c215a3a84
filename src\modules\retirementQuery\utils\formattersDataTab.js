/* eslint-disable no-param-reassign */
import { formatRut, formatDate, letterDiacriticsPunctuationMarkSanitizer } from './formatters';
import { nationalities } from '../../../resources/nationalities.json';

const moneyFormatter = new Intl.NumberFormat('es-CL', {
  style: 'currency',
  currency: 'CLP',
  maximumFractionDigits: 2
});

const sumOfFields = (data, fields) => fields.reduce((acc, incr) => acc + data[incr] || 0, 0);

const concatenateFields = (data, [...props]) =>
  props
    .reduce(
      (total, field) => (data[field] ? total.concat(`${data[field]} `) : total.concat(' ')),
      ''
    )
    .trim()
    .toUpperCase();

const formatNames = data => {
  const beneficiaryFullNameFields = [
    'beneficiaryName',
    'beneficiaryLastName',
    'beneficiaryMothersLastName'
  ];
  const causantFullNameFields = ['causantName', 'causantLastName', 'causantMothersLastName'];
  const collectorFullNameFields = [
    'collectorName',
    'collectorLastName',
    'collectorMothersLastName'
  ];

  const beneficiaryFullName = concatenateFields(data, beneficiaryFullNameFields);
  const causantFullName = concatenateFields(data, causantFullNameFields);
  const collectorFullName = concatenateFields(data, collectorFullNameFields);
  return { ...data, beneficiaryFullName, causantFullName, collectorFullName };
};

const formatOnePercents = data => {
  const onePercentFields = {
    onePercentLaAraucana: { name: 'Caja La Araucana' },
    onePercent18: { name: 'Caja 18' },
    onePercentLosAndes: { name: 'Caja Los Andes' },
    onePercentLosHeroes: { name: 'Caja Los Heroes' }
  };
  data.onePercents = 'No';

  return Object.keys(onePercentFields).reduce((obj, field) => {
    if (data[field].toLowerCase() === 'si')
      obj.onePercents = `${
        isNaN(data.onePercentAdjusted) ? 0 : moneyFormatter.format(data.onePercentAdjusted)
      } (${onePercentFields[field].name})`;
    return obj;
  }, data);
};

const formatOtherSocialDiscounts = data => {
  const otherSocialDiscountsFields = ['othersLosHeroes', 'othersLosAndes'];
  const othersDiscountCompensations = sumOfFields(data, otherSocialDiscountsFields);
  return { ...data, othersDiscountCompensations };
};

const formatMoney = data => {
  const monetaryFields = [
    'currentCapital',
    'basePension',
    'totalSocialCreditDiscounts',
    'totalDiscounts',
    'totalAssets',
    'othersDiscountCompensations',
    'healthLoan',
    'health',
    'afp',
    'totalNonFormulable',
    'aps',
    'nationalHolidaysBonus',
    'christmasBonus',
    'winterBonus',
    'marriageBonus',
    'rebsal',
    'adjustedHealthExemption',
    'article40',
    'article41',
    'forFamilyAssignment',
    'taxableTotalNonFormulable',
    'netTotalNonFormulable',
    'forBasePension',
    'forArticle40',
    'forArticle41',
    'forFamilyAssignmentRetroActiveAmount',
    'forTaxableTotalNonFormulableAssets',
    'forInstitutionalPatient',
    'forRejection',
    'forBonuses',
    'forNetTotalNonFormulableAssets',
    'forTotalNonFormulableDiscounts',
    'law19403',
    'law19539',
    'law19953',
    'retroactiveAmountForPaycheck',
    'totalPensionAccrued',
    'strennaRetroConstitution',
    'otherLink',
    'indemnityDiscount',
    'healthDiscountAccrued',
    'afpDiscountAccrued',
    'settlement'
  ];
  return monetaryFields.reduce((obj, field) => {
    obj[field] = isNaN(data[field]) ? 0 : moneyFormatter.format(data[field]);
    return obj;
  }, data);
};

const formatRutFields = data => {
  const rutFields = ['beneficiaryRut', 'causantRut', 'collectorRut'];
  return rutFields.reduce((obj, field) => {
    obj[field] = formatRut(data[field]);
    return obj;
  }, data);
};

const formatDates = data => {
  const dateFields = [
    'beneficiaryBirthDate',
    'endDateOfTheoricalValidity',
    'endDateOfValidity',
    'pensionStartDate',
    'resolutionDate',
    'accidentDate',
    'disabilityStartDate',
    'nextPaymentDate'
  ];
  return dateFields.reduce((obj, field) => {
    obj[field] = data[field] ? formatDate(data[field]) : null;
    return obj;
  }, data);
};

const formatBooleans = data => {
  const booleanFields = ['institutionalPatient'];

  return booleanFields.reduce((obj, field) => {
    obj[field] = data[field] ? 'Si' : 'No';
    return obj;
  }, data);
};

const formatCountry = data => {
  const nationality = nationalities.find(
    ({ key }) =>
      key.toLowerCase() === letterDiacriticsPunctuationMarkSanitizer(data.country.toLowerCase())
  );

  return { ...data, country: nationality?.name };
};

const formatGender = data => {
  const genderField = ['gender'];

  return genderField.reduce((obj, field) => {
    obj[field] = data[field] === 'F' ? 'Femenino' : 'Masculino';
    return obj;
  }, data);
};

const formatsFunctions = [
  formatNames,
  formatOnePercents,
  formatOtherSocialDiscounts,
  formatMoney,
  formatRutFields,
  formatDates,
  formatBooleans,
  formatCountry,
  formatGender
];

const composeFormats = ([...fns]) => data => fns.reduce((y, fn) => fn(y), data);

const formatDataTab = (data = {}) => composeFormats(formatsFunctions)(data);

export default formatDataTab;
