/* eslint-disable no-magic-numbers */
import { formatEmail, truncate, trim, format } from '../../../../../../utils/formatters';

const formattersOnChange = {
  email: [formatEmail, truncate(320)],
  password: [truncate(8)]
};

const formattersOnBlur = {
  name: [trim],
  email: [trim]
};

const formatOnChange = format(formattersOnChange);
const formatOnBlur = format(formattersOnBlur);

export { formatOnChange, formatOnBlur };
