/* eslint-disable no-underscore-dangle */
import React from 'react';
import { TableCell } from '@material-ui/core';
import Rows from './rows';

const headers = [
  { label: 'Tipo de pensión' },
  { label: 'Edad' },
  { label: 'Base mínima' },
  { label: 'Ley 19.403' },
  { label: 'Ley 19.539' },
  { label: 'Ley 19.953' }
];
export const renderTableHeader = () => {
  return headers.map(({ label }) => {
    return <TableCell key={label}>{label}</TableCell>;
  });
};

export const renderTableData = (state, setState, hasError, setHasError, isEditable) => {
  return state.map(({ _id: id, label, value, age, type }) => (
    <Rows
      key={id}
      id={id}
      type={type}
      pensionType={label}
      age={age}
      rowsValues={value}
      handleOnChange={setState}
      hasError={hasError}
      handleError={setHasError}
      isEditable={isEditable}
    />
  ));
};
