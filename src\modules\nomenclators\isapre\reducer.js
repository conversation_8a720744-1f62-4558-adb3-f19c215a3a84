import { CREATE_NOM_ISAPRE, DELETE_NOM_ISAPRE, UPDATE_NOM_ISAPRE, SET_NOM_ISAPRE } from './actions';

const initialState = {
  data: [],
  errors: []
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case CREATE_NOM_ISAPRE: {
      const { isapre } = action.data;
      return {
        ...state,
        data: [...state.data, isapre]
      };
    }
    case UPDATE_NOM_ISAPRE: {
      const data = [...state.data];
      data[data.indexOf(action.data.prevIsapre)] = action.data.newIsapre;
      return { ...state, data };
    }
    case DELETE_NOM_ISAPRE: {
      const data = [...state.data];
      data.splice(data.indexOf(action.data.isapre), 1);
      return { ...state, data };
    }
    case SET_NOM_ISAPRE: {
      const result = action.data;
      return { ...state, data: result };
    }
    default:
      return state;
  }
}
