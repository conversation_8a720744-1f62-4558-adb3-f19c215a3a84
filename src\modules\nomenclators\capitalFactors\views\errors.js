/* eslint-disable react/forbid-prop-types */
/* eslint-disable import/no-unresolved */
import React from 'react';
import { Table<PERSON><PERSON>, TableRow, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Button, Typography } from '@material-ui/core';
import { useSelector } from 'react-redux';
import { Link as RouterLink } from 'react-router-dom';
import Table from 'components/Table';

import useStyles from './styles';

const errorHeaders = [{ label: 'Fila' }, { label: 'Mensaje de error' }];
const FACTORS_REQUIRED_FIELDS = 'Llave, Factor.';
const CONCURRENCIES_REQUIRED_FIELDS = 'Rut beneficiario, % Concurrencias.';
const FACTORS_TYPE = 'factores';

const renderTableHeader = () =>
  errorHeaders.map(key => (
    <TableCell key={key.label} align="left">
      {key.label}
    </TableCell>
  ));

const renderTableData = result =>
  result.map(({ row, errors }) => (
    <TableRow hover key={`${row}`}>
      <TableCell align="left">{row}</TableCell>
      <TableCell align="left">{errors.join(', ')}</TableCell>
    </TableRow>
  ));

const ResumeErrorCapitalFactors = () => {
  const classes = useStyles();
  const errors = useSelector(store => store.capitalFactors.fileError);
  const type = useSelector(store => store.capitalFactors.fileType);
  const requiredFields =
    type === FACTORS_TYPE ? FACTORS_REQUIRED_FIELDS : CONCURRENCIES_REQUIRED_FIELDS;

  return (
    <Card className={classes.content}>
      <CardHeader
        title={`Resumen de Importación ${type === FACTORS_TYPE ? 'Factores' : 'Concurrencias'}`}
        className={classes.headerImport}
      />
      <div className={classes.principalErrors}>
        <Typography>
          <b>Existen errores en el archivo que impidieron su importación</b>
        </Typography>
      </div>
      <Table renderHeader={renderTableHeader} renderTable={() => renderTableData(errors)} />
      <div className={classes.requiredFields}>
        <b>* Campos obligatorios: </b>
        {requiredFields}
      </div>

      <div className={classes.footerButton}>
        <RouterLink
          to={{
            pathname: '/mantenedores/factores-capitales/importar',
            state: { isReturning: true }
          }}
        >
          <Button className={classes.accept}>Aceptar</Button>
        </RouterLink>
      </div>
    </Card>
  );
};

export default ResumeErrorCapitalFactors;
