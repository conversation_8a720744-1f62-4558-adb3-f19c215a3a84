import React from 'react';
import useOnlineStatus from '@rehooks/online-status';
import PropTypes from 'prop-types';
import { Button, Tooltip } from '@material-ui/core';

const AnnulateButton = ({ disabledLink, linkText, handleCancelPensions }) => {
  const onlineStatus = useOnlineStatus();
  return (
    <Tooltip title={linkText}>
      <span>
        <Button
          variant="contained"
          color="default"
          disabled={!onlineStatus || disabledLink}
          onClick={handleCancelPensions}
        >
          Anular enlace
        </Button>
      </span>
    </Tooltip>
  );
};

AnnulateButton.propTypes = {
  disabledLink: PropTypes.bool,
  linkText: PropTypes.string,
  handleCancelPensions: PropTypes.func.isRequired
};
AnnulateButton.defaultProps = {
  linkText: '',
  disabledLink: true
};

export default AnnulateButton;
