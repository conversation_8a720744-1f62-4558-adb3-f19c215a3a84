/* eslint-disable no-console */
import { queryPensions } from '../services/queryPensions.service';
import { setPensionData, setWasDataSearched } from '../actions';
import formatData from '../utils/formatDataTable';

const queryPensionsByField = async (buildQuery, queryToSelect, fieldName, value, dispatch) => {
  try {
    if (value && fieldName) {
      const query = buildQuery[queryToSelect(fieldName)](value);
      const { data } = await queryPensions(query);
      const formattedData = formatData(data);
      dispatch(setPensionData(formattedData));
      dispatch(setWasDataSearched(true));
    }
  } catch (err) {
    console.log(err);
  }
};

const useDataDownload = (buildQuery, queryToSelect, fieldName, value, dispatch) => {
  return () => queryPensionsByField(buildQuery, queryToSelect, fieldName, value, dispatch);
};

export { useDataDownload, queryPensionsByField };
