// Taken from https://gist.github.com/rotvulpix/69a24cc199a4253d058c

const word = '0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäöüëïâêîôûçğş';
const regex = `^([${word}\\.\\-',])+(\\s[${word}\\.\\-',]+)*$`;
// eslint-disable-next-line no-misleading-character-class
const textFieldRegRule = new RegExp(regex, 'i');

const defaultFormatter = (value = '') =>
  value
    .replace(/^\s|[^a-záéíóúàèìòùãẽĩõũỹg̃ñäëïöüâêîôûç%ğş0-9.',´`^¨~-\s]/gi, '')
    .replace(/\s+/g, ' ');

export { textFieldRegRule, defaultFormatter };
