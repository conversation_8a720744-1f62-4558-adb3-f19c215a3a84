/* eslint-disable react/prop-types */
/* eslint-disable import/prefer-default-export */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useRef, useEffect } from 'react';
import { select, scaleBand } from 'd3';

const Legend = ({ colorObj }) => {
  const legendContainer = useRef();

  const svgWidth = 300;
  const svgHeight = 300;
  const margin = { top: 20, right: 20, bottom: 150, left: 15 };
  const graphWidth = svgWidth - margin.left - margin.right;
  const graphHeight = svgHeight - margin.top - margin.bottom;
  const squareSize = 18;
  const sepSquareLegend = 5;

  useEffect(() => {
    const svg = select(legendContainer.current)
      .append('svg')
      .attr('width', graphWidth)
      .attr('height', graphWidth);

    const y = scaleBand()
      .domain(colorObj.map(d => d.name))
      .range([0, graphHeight]);

    const legend = svg
      .selectAll('.legendPlot')
      .data(colorObj)
      .enter()
      .append('g')
      .attr('class', 'legendPlot')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    legend
      .append('rect')
      .attr('y', data => y(data.name))
      .attr('x', 0)
      .attr('width', squareSize)
      .attr('height', squareSize)
      .style('fill', (_d, i) => colorObj[i].color);

    legend
      .append('text')
      .attr('x', squareSize + sepSquareLegend)
      .attr('y', data => y(data.name))
      .attr('dy', '0.9em')
      .style('text-anchor', 'start')
      .text((_d, i) => colorObj[i].legend);
  }, []);

  return <div ref={legendContainer} />;
};

export default Legend;
