/* eslint-disable react/prop-types */
/* eslint-disable react/forbid-prop-types */
import { v4 as uuidv4 } from 'uuid';
import { Tooltip, IconButton, FormLabel } from '@material-ui/core';
import { ArrowDownward, Clear, Check, DeleteOutline, Edit } from '@material-ui/icons';
import MaterialTable, { MTableEditRow } from 'material-table';
import PropTypes from 'prop-types';
import React, { forwardRef, useRef } from 'react';
import { ValidatorForm } from 'react-material-ui-form-validator';
import {
  defaultFormatter,
  isValidDecimal,
  formatDecimal,
  IsvalidDate
} from '../../validator/validField';
import SecureTextField from './secureTextField';
import DateTextField from './dateTextField';
import { checkWritePermission } from 'utils/checkUserPermission';
import useStyles from '../styles';

import moment from 'moment';

const percentageValidator = ['required', 'matchPercentage', 'maxStringLength:9'];
const percentageErrorMessage = [
  'El valor es requerido',
  'Valores permitidos: [0.01 -  999999.99]',
  'Sólo dos decimales permitidos'
];

const formatDate = date => (date ? moment(date).format('DD-MM-YYYY') : '');

const tableIcons = {
  Check: forwardRef((props, ref) => <Check {...props} ref={ref} />),
  Edit: forwardRef((props, ref) => <Edit {...props} ref={ref} />),
  Delete: forwardRef((props, ref) => <DeleteOutline {...props} ref={ref} />),
  Clear: forwardRef((props, ref) => <Clear {...props} ref={ref} />),
  SortArrow: forwardRef((props, ref) => <ArrowDownward {...props} ref={ref} />)
};
const isRowValid = ({ _name, amount, publishDate, validityDate }) => {
  if (
    IsvalidDate(publishDate) !== '' ||
    IsvalidDate(validityDate) !== '' ||
    !isValidDecimal(amount)
  ) {
    return false;
  }
  return true;
};

const lengthObject = { name: '255', amount: '9' };
const getMaxLength = fieldName => ({ maxLength: lengthObject[fieldName] || '255' });
const editableFn = (fieldName, labelName, validators, errorMessages, formater) => ({
  onChange,
  value
}) => (
  <SecureTextField
    name={fieldName}
    id={fieldName}
    size="small"
    displayName={labelName}
    inputprops={getMaxLength(fieldName)}
    validators={validators}
    errorMessages={errorMessages}
    onChange={e =>
      onChange(
        formater
          ? formater(e.target.value.replace('.', ','))
          : defaultFormatter(e.target.value.replace('.', ','))
      )
    }
    onBlur={e => onChange(e.target.value)}
    value={value || ''}
  />
);

const editableDate = (classes, fieldName, labelName, validators, _errorMessages, _formater) => ({
  onChange,
  value
}) => (
  <DateTextField
    classes={classes}
    name={fieldName}
    id={fieldName}
    size="small"
    displayName={labelName}
    inputprops={getMaxLength(fieldName)}
    validators={validators}
    helperText={IsvalidDate(value)}
    error={IsvalidDate(value)}
    onChange={e => onChange(e)}
    onBlur={e => onChange(e.target.value)}
    value={value || ''}
  />
);

const LabelFn = (_fieldName, _labelName) => ({ onChange, value = '' }) => {
  return <FormLabel>{value}</FormLabel>;
};

export default function UnidadTable(props) {
  const classes = useStyles();
  const { data, onCreate, onUpdate, onSubmit, userRole } = props;
  const hasWritePermission = checkWritePermission(userRole);
  const formRef = useRef(null);

  const labelComponent = LabelFn('name', 'Parametro');
  const percentageComponent = editableFn(
    'amount',
    'Monto ($)',
    percentageValidator,
    percentageErrorMessage
  );

  const publishDateComponent = editableDate(classes, 'publishDate', 'Fecha Publicación');

  const validityDateComponent = editableDate(classes, 'validityDate', 'Fecha Vigencia');

  return (
    <ValidatorForm onSubmit={onSubmit} ref={formRef} instantValidate>
      <MaterialTable
        components={{
          Action: p => {
            let { action } = p;
            if (typeof p.action === 'function') {
              action = p.action();
            }
            return (
              <Tooltip title={!action.disabled && !p.disabled ? action.tooltip : ''}>
                <IconButton
                  onClick={event => {
                    action.onClick(event, p.data);
                  }}
                  color="inherit"
                  variant="contained"
                  style={{ textTransform: 'none' }}
                  size={p.size}
                  disabled={!hasWritePermission || action.disabled || p.disabled}
                >
                  {action.icon.render()}
                </IconButton>
              </Tooltip>
            );
          },
          EditRow: p => (
            <MTableEditRow
              {...p}
              onEditingCanceled={(mode, rowData) => {
                return p.onEditingCanceled(mode, rowData);
              }}
              onEditingApproved={async (mode, newData, oldData) => {
                if (!isRowValid(newData)) {
                  formRef.current.submit();
                  return null;
                }
                return p.onEditingApproved(mode, newData, oldData);
              }}
            />
          )
        }}
        icons={tableIcons}
        columns={[
          {
            title: 'Parametro',
            field: 'name',
            sorting: false,
            editComponent: labelComponent
          },
          {
            title: 'Monto ($)',
            field: 'amount',
            sorting: false,
            render: ({ amount }) => formatDecimal(amount),
            editComponent: percentageComponent
          },
          {
            title: 'Fecha Publicación',
            field: 'publishDate',
            sorting: false,
            render: ({ publishDate }) => formatDate(publishDate),
            editComponent: publishDateComponent
          },
          {
            title: 'Fecha Vigencia',
            field: 'validityDate',
            sorting: false,
            render: ({ validityDate }) => formatDate(validityDate),
            editComponent: validityDateComponent
          }
        ]}
        data={data}
        options={{
          addRowPosition: 'last',
          thirdSortClick: false,
          paging: false,
          search: false,
          showTitle: false,
          actionsColumnIndex: -1,
          toolbar: false
        }}
        localization={{
          body: {
            emptyDataSourceMessage: 'No existen registros ingresados',
            deleteTooltip: 'Eliminar',
            editTooltip: 'Editar',
            editRow: {
              deleteText: '¿Está seguro que desea eliminar el elemento?',
              saveTooltip: 'Aceptar',
              cancelTooltip: 'Cancelar'
            }
          },
          header: {
            actions: 'Acciones'
          }
        }}
        editable={{
          onRowAdd: newData =>
            new Promise(async (resolve, reject) => {
              if (!isRowValid(newData)) {
                return reject();
              }
              try {
                await onCreate({ ...newData, id: uuidv4() }, {});
                return resolve();
              } catch (error) {
                return resolve();
              }
            }),
          onRowUpdate: (newData, oldData) =>
            new Promise(async (resolve, reject) => {
              if (!isRowValid(newData)) {
                return reject();
              }
              try {
                await onUpdate(newData, oldData);
                return resolve();
              } catch (error) {
                return resolve();
              }
            })
        }}
      />
    </ValidatorForm>
  );
}

UnidadTable.propTypes = {
  data: PropTypes.oneOfType([PropTypes.array, PropTypes.func]).isRequired,
  onCreate: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  onSubmit: PropTypes.func,
  onErrorSnackbar: PropTypes.func.isRequired
};

UnidadTable.defaultProps = {
  onSubmit: () => {
    /* any */
  }
};
