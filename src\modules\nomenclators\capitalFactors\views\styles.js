/* eslint-disable no-magic-numbers */
import { makeStyles } from '@material-ui/styles';
import { colors } from '@material-ui/core';

const styles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(3)
  },
  errorDisplay: {
    color: 'red',
    fontSize: 10
  },
  formControl: {
    marginTop: 20,
    marginRight: 20,
    marginBottom: 20,
    color: 'black',
    backgroundColor: '#00FFFFF'
  },
  spanFinalize: {
    display: 'inline-block',
    marginLeft: '33%',
    marginTop: 40
  },
  importText: {
    fontSize: 10,
    fontWeight: '1',
    marginLeft: 10,
    color: 'Gray'
  },
  subtitle: {
    fontSize: 11,
    fontWeight: 5
  },
  textfield: {
    width: '35%',
    marginTop: 20,
    marginRight: 20,
    minWidth: 210
  },
  textfieldLoaded: {
    width: '35%',
    marginTop: 20,
    marginRight: 20,
    minWidth: 210,
    '& .MuiInputBase-root.Mui-disabled': {
      color: '#212121'
    }
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15
  },
  divider: {
    backgroundColor: colors.grey[300]
  },
  content: {
    marginTop: theme.spacing(3)
  },
  footerButton: {
    display: 'flex',
    justifyContent: 'center',
    margin: '1rem'
  },
  headerImport: {
    background: '#006531',
    '& div': {
      '& span': {
        color: 'white'
      }
    }
  },
  principalErrors: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: '1rem',
    marginBottom: '1rem'
  },
  acceptButton: {
    display: 'flex',
    margin: 15,
    justifyContent: 'center'
  },
  requiredFields: {
    margin: 15
  },
  finalize: {
    marginTop: 10,
    marginLeft: '20%'
  },
  accept: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.primary.light,
      color: theme.palette.primary.contrastText
    }
  },
  cancel: {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.error.light,
      color: theme.palette.error.contrastText
    }
  }
}));

export default styles;
