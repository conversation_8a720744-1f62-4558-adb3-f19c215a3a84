
const formatter = new Intl.NumberFormat('es-CL', {
  style: 'currency',
  currency: 'CLP',
  maximumFractionDigits: 0
});

const formatMoney = value => {
  return formatter.format(
    String(value)
      .replace(/[^\d]/g, '')
      .slice(0, 6)
  );
};

const formatForUpdateAmount = amount => `${amount}`?.replace(/\./g, '').replace('$', '');

const formatRut = rut =>
  rut
    .replace(/[^\dkK-]/g, '')
    .replace(/^(\d{1,2})(\d{3})(\d{3})-([0-9kK])$/, '$1.$2.$3-$4')
    .toUpperCase();



const isValidCheckDigit = rutWithDV => {
  const [rut, currentDV] = rutWithDV.split('-');
  const module = 11;
  const multipliers = '234567'.split('').map(x => +x);
  const sumOfElements = rut
    .replace(/[^\d]/g, '')
    .split('')
    .reverse()
    .map((value, index) => +value * multipliers[index % multipliers.length])
    .reduce((accumulator, currentValue) => accumulator + currentValue);
  let actualDV = module - (sumOfElements % module);
  if (actualDV === module - 1) {
    actualDV = 'k';
    return actualDV === currentDV.toLowerCase();
  }
  if (actualDV === module) actualDV = 0;
  return actualDV === +currentDV;
};

const isValidRut = rut =>
  /^([1-9][0-9]?)(\.)?\d{3}\2\d{3}-[0-9kK]$/.test(rut) && isValidCheckDigit(rut);

const isValidMoney = money => /^([1-9]\d{0,11}|[1-9]\d{0,2}(\.\d{3}){0,3}|0)$/.test(money);

export { formatMoney, formatRut, isValidRut, isValidMoney, formatForUpdateAmount };
