/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import { Grid } from '@material-ui/core';
import { HeaderContent, Page } from 'components';
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { loadIsapres, processIsapre } from '../services/index';
import { deleteNomenclatorIsapre } from '../actions';

import IsapreTable from './components/isapreTable';
import useStyles from './styles';

const initialRequest = async dispatch => {
  await dispatch(loadIsapres());
};

const IsapreNomenclatorPage = props => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const [enabledButton, setEnabledButton] = useState(false);
  const state = {
    data: useSelector(store => store.isapres.data)
  };

  useEffect(() => {
    initialRequest(dispatch);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const { onSuccessSnackbar, onErrorSnackbar, role } = props;

  const CONFLICT = 409;
  const handleConflict = oldData =>
    (!oldData.code || !oldData.rut) && dispatch(deleteNomenclatorIsapre(oldData));

  const keyValue = { code: 'Código', rut: 'Rut' };
  const handleTranslate = key =>
    keyValue[key]
      ? `El ${keyValue[key]} que intenta guardar ya existe`
      : `El registro que intenta guardar ya existe`;

  const handleProcess = (okMessage, erroMessage, operation) => async (newData, oldData) => {
    try {
      if (operation === 'create' || (operation === 'update' && oldData) || operation === 'delete') {
        const result = await dispatch(processIsapre(oldData, newData, operation))
          .then(() => {
            onSuccessSnackbar(okMessage);
            return true;
          })
          .catch(error => {
            let messageError;
            if (error.response.status === CONFLICT) {
              const key = Object.keys(error.response.data.error.keyValue);
              if (operation === 'update') handleConflict(oldData);
              messageError = handleTranslate(key);
              onErrorSnackbar(messageError);
              setEnabledButton(true);
            } else {
              messageError = erroMessage;
              onErrorSnackbar(messageError);
              setEnabledButton(true);
            }
            throw new Error(messageError);
          });
        if (!result) return false;
      }
      return true;
    } catch ({ message }) {
      onErrorSnackbar(message);
      setEnabledButton(true);
      throw new Error(message);
    }
  };

  return (
    <Page className={classes.root} title="Isapres">
      <Grid container justify="space-between">
        <HeaderContent overline="Configuraciones / Mantenedores" title="Isapres" />
      </Grid>
      <IsapreTable
        userRole={role}
        enabledButton={enabledButton}
        data={state.data}
        onCreate={handleProcess(
          'Los valores se guardaron correctamente',
          'Error en actualización de registro',
          'create',
        )}
        onUpdate={handleProcess(
          'Los valores se guardaron correctamente',
          'Error en actualización de registro',
          'update'
        )}
        onDelete={handleProcess('Registro eliminado', 'Error en eliminación de registro', 'delete')}
      />
    </Page>
  );
};

// eslint-disable-next-line import/prefer-default-export
export { IsapreNomenclatorPage };
