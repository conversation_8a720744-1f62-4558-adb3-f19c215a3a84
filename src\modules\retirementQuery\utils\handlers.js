const handleAmountChange = (
  e,
  {
    key,
    dispatch,
    action,
    actionError,
    modifiedFieldErrors,
    formatter,
    validation,
    isRequired,
    maxLength
  }
) => {
  let value = formatter(e?.target?.value);

  if (value.charAt(0) === '0') value = value.substring(1, value.length);
  if (value.length === 1 && value === '0') value = '';
  if (value.length > maxLength) value = value.substring(0, maxLength);

  const isValidFormatAndLength = value.length <= maxLength && validation.test(value);

  const isCorrect = isRequired
    ? isValidFormatAndLength
    : value.length === 0 || isValidFormatAndLength;
  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));
  dispatch(action(value));
};

// eslint-disable-next-line import/prefer-default-export
export { handleAmountChange };
