import {
  CLEAN_NATIONAL_HOLIDAYS_FILE_ERROR,
  SET_NATIONAL_HOLIDAYS_FILE_ERROR,
  SET_HOLIDAYS_FILEDATA_ERROR
} from './actions';

const initialState = {
  isError: false,
  fileErrors: [],
  errors: []
};

export default function reducer(state = initialState, action = { type: '', data: {} }) {
  switch (action.type) {
    case SET_NATIONAL_HOLIDAYS_FILE_ERROR:
      return {
        ...state,
        fileErrors: [...state.fileErrors, action.data.fileError]
      };
    case SET_HOLIDAYS_FILEDATA_ERROR:
      return {
        ...state,
        isError: action.data.isError,
        errors: action.data.errors
      };
    case CLEAN_NATIONAL_HOLIDAYS_FILE_ERROR:
      return {
        isError: false,
        errors: [],
        fileErrors: []
      };
    default:
      return state;
  }
}
