/* eslint-disable no-console */
/* eslint-disable import/prefer-default-export */
import { updateNomenclatorCaja, loadNomenclatorCaja } from '../actions';

//-----------------------------------------------------------------------------------------------------------
export const processMinimunSalary = (prevSalary, newSalary, operation) => async (
  dispatch,
  _getState,
  { api, axiosRequest }
) => {
  const createdSalary = newSalary;
  createdSalary.amount = newSalary.amount.replace(',', '.');
  if (operation === 'update') {
    const {
      data: { error, isError }
    } = await updateSalary({ axiosRequest, api, im: createdSalary });
    if (isError) {
      throw new Error(error.code);
    }
    createdSalary.amount = newSalary.amount.replace('.', ',');
    await dispatch(updateNomenclatorCaja(prevSalary, createdSalary));
  }
};

const updateSalary = async ({ axiosRequest, api, im }) =>
  axiosRequest.put(`${api}/minimunSalary/im`, { im });

//-----------------------------------------------------------------------------------------------------------
export const loadMinimunSalary = () => async (dispatch, _getState, { api, axiosRequest }) => {
  const {
    data: { result: minimunSalary }
  } = await axiosRequest.get(`${api}/minimunSalary/im`).catch(err => {
    console.error(err);
    return { data: { result: [] } };
  });

  const minimunSalaryFormat = minimunSalary.map(salary => {
    const { amount } = salary;
    return { ...salary, amount: amount.toString().replace('.', ',') };
  });

  await dispatch(loadNomenclatorCaja(minimunSalaryFormat));
  return minimunSalaryFormat;
};
