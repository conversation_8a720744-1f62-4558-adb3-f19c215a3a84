/* eslint-disable import/prefer-default-export */
/* eslint-disable no-console */

import { defaultPlotData } from '../utils/defaultMonths';
import { axiosRequest } from '../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

const numberOfPastMonths = 6;

//------------------------------------------------------------------------------------------------
export const queryDueDays = async setData => {
  const {
    data: { dueDaysForProcesses }
  } = await axiosRequest.get(`${api}/getBusinessDays/due-days`).catch(_err => {
    return {
      data: { dueDaysForProcesses: [] }
    };
  });
  setData(dueDaysForProcesses);
};
//------------------------------------------------------------------------------------------------
export const queryHistoricalData = async setData => {
  const { data = [] } = await axiosRequest.get(`${api}/monthlyExpenses/stats`).catch(_err => {
    return {
      data: defaultPlotData(numberOfPastMonths)
    };
  });
  setData(data);
};
