import React, { createContext, useState } from 'react';
import PropTypes from 'prop-types';
import useOnlineStatus from '@rehooks/online-status';
import { Snackbar, useSnackbar, Progress, useProgress } from '../components';
import jwt_decode from "jwt-decode";
export const AppContext = createContext();

function AppProvider({ children }) {
  const {
    open,
    setOpen,
    stylePreset,
    setStylePreset,
    message,
    setMessage,
    onClose,
    setOnClose,
    actions,
    setActions
  } = useSnackbar();

  let loginUser = null;
  const token  = JSON.parse(localStorage.getItem('pec.token'));
  if(token && token.token)
  {
    const useraux = jwt_decode(token.token);
    const { _id, name, email, role = {} } = useraux;
  
    const { roleName, views = [] } = role;
    const userViews = views.map(({ permission, view: viewItem }) => ({
      view: viewItem?.view,
      module: viewItem?.module,
      viewNumber: viewItem?.viewNumber,
      permission
    }));
    
    loginUser = { _id, name, email, token, role: roleName, userViews };
  }

  const [user, setUser] = useState(loginUser);



  const { loading, setLoading } = useProgress();

  const showSnackbar = ({
    actions: actionList,
    message: text,
    onClose: onCloseFn,
    stylePreset: preset = 'info'
  }) => {
    setActions(actionList);
    setMessage(text);
    setOnClose(() => onCloseFn);
    setStylePreset(preset);
    setOpen(true);
  };

  const isOnline = useOnlineStatus();

  const store = {
    isOnline,
    isLoading: loading,
    showSnackbar,
    setLoading,
    loggedUser: user,
    setLoggedUser: data => {
      setUser(data);
      localStorage.setItem('pec.token', '{ "token":' + JSON.stringify(data.token) + '}');      
    }
  };

  return (
    <>
      <Progress isLoading={loading} />
      <AppContext.Provider value={store}>{children}</AppContext.Provider>
      <Snackbar
        open={open}
        setOpen={setOpen}
        message={message}
        stylePreset={stylePreset}
        onClose={onClose}
        autoHideDuration={5000}
        actions={actions}
      />
    </>
  );
}

AppProvider.propTypes = {
  children: PropTypes.node
};

AppProvider.defaultProps = {
  children: null
};

export default AppProvider;
