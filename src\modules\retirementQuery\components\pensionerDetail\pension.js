/* eslint-disable import/no-unresolved */
/* eslint-disable react/prop-types */
import React, { useState, createRef, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';
import useOnlineStatus from '@rehooks/online-status';
import useRouter from 'utils/useRouter';
import { useSelector, useDispatch } from 'react-redux';

import { Card, CardContent, CardHeader, Divider, Fade } from '@material-ui/core';
import MuiAlert from '@material-ui/lab/Alert';
import { useProgress } from 'components';
import moment from 'moment';
import { AppContext } from '../../../../provider/app';
import createTable from '../../utils/createTable';
import usePdfUpload from '../../hooks/usePdfUpload';
import ImportButton from '../importButton';
import useStyles from './style';
import { handleAmountChange } from '../../utils/handlers';
import {
  setWasModifiedAFieldThatRequiresAFile,
  setWasPdfUploaded,
  setPdfFile,
  setInstitutionalPatient,
  setAfpAffiliation,
  setPensionType,
  setPensionTypeDueToCharges,
  setAccidentDate,
  setModifiedFieldErrors,
  setCun,
  setValidityType,
  setInactivationReason,
  setEndDateOfValidity,
  setEndDateOfTheoricalValidity,
  setInactivationDate,
  setEvaluationDate,
  setReactivationDate,
  setReactivateManually,
  setIsInactivationReasonChange,
  setBasePension
} from '../../actions';

import {
  isPensionTypeEditable,
  changePensionTypeOptions,
  isPensionerWithFamilyCharges
} from '../../utils/pensionTypesMapper';

import {
  isValidityTypeEditable,
  changeValidityTypeOptions,
  changeInactivationByDisabilityOptions,
  especialCaseToNotInactivateThisMonth
} from '../../utils/validityTypeMapper';

import {
  ALPHANUMERIC,
  alphaNumericSanitizer,
  CHILEAN_AMOUNT_PATTERN,
  formatChileanAmount
} from '../../utils/formatters';
import { checkCronExecution, getCurrentDate } from '../../services/queryPensions.service';

const MAX_AMOUNT_LENGTH = 9;
const CronMark = 'INACTIVATE_PENSIONS_WORKER';

const LABEL = 'Fecha de accidente';
const TO_INSTITUTIONAL_PATIENT =
  'Debe importar el certificado neurológico correspondiente antes de realizar el cambio, "Certificado_neurologico.pdf"';
const TO_NON_INSTITUTIONAL_PACIENT =
  'Debe importar el dictamen de tribunales correspondiente antes de realizar el cambio, "Dictamen_de_tribunales.pdf"';
const CHANGE_AFP_AFFILIATION =
  'Debe importar el certificado de afiliación AFP antes de realizar el cambio, "Certificado_de_afiliacion_AFP.pdf"';

const SI = /s[ií]/i;

const MIN_DATE = moment('01-01-1900').toDate();
const MAX_DATE = moment('12-01-2099').toDate();

const CUN_MAX_LENGHT = 100;
const NOT_VALID_PENSION = /No vigente/i;

const ONE_HUNDRED_TEN_YEARS = 110;
const PLUS_ONE_HUNDRED_TEN_YEARS_VIGENCY_TYPES = /vigente vitalicia/i;

const ADMINISTRATOR_ROLE = /Administrador/i;
const PEC_BOSS_ROLE = /Jefe de PEC/i;

const isAdminOrPecBoss = loggedUser =>
  ADMINISTRATOR_ROLE.test(loggedUser?.role) || PEC_BOSS_ROLE.test(loggedUser?.role);
const isAdmin = loggedUser => ADMINISTRATOR_ROLE.test(loggedUser?.role);
const extractValueAndKey = ({
  values,
  afpAffiliation,
  institutionalPatient,
  attachNeurologicalCertificate
}) => {
  if (values.afpAffiliation !== afpAffiliation)
    return { key: 'afpAffiliation', value: afpAffiliation };
  if (values.institutionalPatient !== institutionalPatient)
    return { key: 'institutionalPatient', value: institutionalPatient };
  if (/Si/i.test(institutionalPatient) && attachNeurologicalCertificate)
    return { key: 'institutionalPatient', value: institutionalPatient };
  return {};
};

const alertMessages = {
  institutionalPatient: transformedValue => {
    if (SI.test(transformedValue)) {
      return TO_INSTITUTIONAL_PATIENT;
    }
    return TO_NON_INSTITUTIONAL_PACIENT;
  },
  afpAffiliation: () => CHANGE_AFP_AFFILIATION
};

const Alert = props => {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
};

const alertModifiedField = changes => {
  const { key, value } = extractValueAndKey(changes);
  return (
    key &&
    value && (
      <Fade in timeout={{ enter: 1000 }}>
        <Alert severity="info" variant="filled">
          {alertMessages[key](value)}
        </Alert>
      </Fade>
    )
  );
};

const handleFieldChange = (
  e,
  {
    key,
    dispatch,
    action,
    actionError,
    validation,
    modifiedFieldErrors,
    maxLength,
    formatter,
    isRequired
  }
) => {
  let formattedCun = formatter(e?.target?.value);

  if (formattedCun?.length > maxLength) {
    formattedCun = formattedCun.substring(0, maxLength);
  }

  const isValidFormatAndLength = formattedCun?.length <= maxLength && validation.test(formattedCun);

  const isCorrect = isRequired
    ? isValidFormatAndLength
    : formattedCun.length === 0 || isValidFormatAndLength;

  isCorrect
    ? dispatch(actionError({ ...modifiedFieldErrors, [key]: false }))
    : dispatch(actionError({ ...modifiedFieldErrors, [key]: true }));

  dispatch(action(formattedCun));
};

const Pension = ({
  values,
  editable,
  onErrorSnackbar,
  onSuccessSnackbar,
  _isAfter7BusinessDay
}) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();
  const router = useRouter();
  const fileUploadInput = createRef();
  const { loggedUser } = useContext(AppContext);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const agreesToChangeCollectorRut = useSelector(
    store => store.queryPensions.agreesToChangeCollectorRut
  );
  const wasModifiedAFieldThatRequiresAFile = useSelector(
    store => store.queryPensions.wasModifiedAFieldThatRequiresAFile
  );
  const successfulFileUpload = useSelector(store => store.queryPensions.successfulFileUpload);
  const afpList = useSelector(store => store.queryPensions.afpList);
  const institutionalPatient = useSelector(store => store.queryPensions.institutionalPatient);
  const afpAffiliation = useSelector(store => store.queryPensions.afpAffiliation);
  const pensionType = useSelector(store => store.queryPensions.pensionType);
  const accidentDate = useSelector(store => store.queryPensions.accidentDate);
  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);
  const cun = useSelector(store => store.queryPensions.cun);
  const validityType = useSelector(store => store.queryPensions.validityType);
  const inactivationReason = useSelector(store => store.queryPensions.inactivationReason);
  const endDateOfValidity = useSelector(store => store.queryPensions.endDateOfValidity);
  const endDateOfTheoricalValidity = useSelector(
    store => store.queryPensions.endDateOfTheoricalValidity
  );
  const currentDate = useSelector(store => store.queryPensions.currentDate);
  const basePension = useSelector(store => store.queryPensions.basePension);

  const isInactivationReasonChange = useSelector(
    store => store.queryPensions.isInactivationReasonChange
  );

  const [isCronExecuted, setIsCronExecuted] = useState(false);
  const [validityTypeOptions, setValidityTypeOptions] = useState([]);
  const [inactivationReasonOptions, setInactivationReasonOptions] = useState([]);

  const [maxDateAccidentDate, setMaxDateAccidentDate] = useState(new Date());
  const [firstDayOfCurrentMonth, setFirstDayOfCurrentMonth] = useState(new Date());
  const [minDateNextMonth, setMinDateNextMonth] = useState(new Date());
  const [minDateForEndDateOfTheoricalValidity, setMinDateForEndDateOfTheoricalValidity] = useState(
    new Date()
  );

  const attachNeurologicalCertificate = useSelector(
    store => store.queryPensions.attachNeurologicalCertificate
  );

  const activateImportButton = () => editable && onlineStatus && wasModifiedAFieldThatRequiresAFile;

  const doesGoFromNotValidToValid = (previousValue, currentValue) =>
    NOT_VALID_PENSION.test(previousValue) &&
    !NOT_VALID_PENSION.test(currentValue) &&
    previousValue !== currentValue;
  const doesRemainAValidPension = (previousValue, currentValue) =>
    !NOT_VALID_PENSION.test(previousValue) &&
    !NOT_VALID_PENSION.test(currentValue) &&
    previousValue !== currentValue;
  const doesGoFromValidToNotValid = (previousValue, currentValue) =>
    !NOT_VALID_PENSION.test(previousValue) &&
    NOT_VALID_PENSION.test(currentValue) &&
    previousValue !== currentValue;

  const isDateChange = previousDate => moment(previousDate).isSame(currentDate, 'day');

  const displayImportAndAlert = () =>
    !successfulFileUpload &&
    wasModifiedAFieldThatRequiresAFile &&
    editable &&
    (values.institutionalPatient !== institutionalPatient ||
      values.afpAffiliation !== afpAffiliation ||
      attachNeurologicalCertificate);

  const enableEndDateOfValidity = () => {
    if (
      isCronExecuted &&
      (doesGoFromValidToNotValid(values?.validityType, validityType) ||
        doesRemainAValidPension(values?.validityType, validityType))
    ) {
      return true;
    }
    return false;
  };

  const enableEndDateOfTheoricalValidity = () => {
    if (
      isCronExecuted &&
      (doesGoFromNotValidToValid(values?.validityType, validityType) ||
        doesRemainAValidPension(values?.validityType, validityType))
    ) {
      return true;
    }

    return false;
  };

  const enableInactivationReason = () => {
    if (isCronExecuted && doesGoFromValidToNotValid(values?.validityType, validityType)) {
      return true;
    }

    return false;
  };

  const handledEndDateOfValidityChange = (e, { key, action, actionError }) => {
    const { _d: selectedDate, _isValid } = e || {};

    !selectedDate ||
    !_isValid ||
    e.isAfter(MAX_DATE) ||
    e.isBefore(
      doesRemainAValidPension(values?.validityType, validityType) ? minDateNextMonth : MIN_DATE
    )
      ? dispatch(actionError({ ...modifiedFieldErrors, [key]: true }))
      : dispatch(actionError({ ...modifiedFieldErrors, [key]: false }));

    dispatch(action(selectedDate));

    if (_isValid && e.isBefore(firstDayOfCurrentMonth)) {
      return dispatch(setInactivationDate(currentDate));
    }
    if (_isValid && e.isAfter(moment(currentDate).endOf('month'))) {
      return dispatch(setEvaluationDate(currentDate));
    }
    if (
      _isValid &&
      e.isBetween(firstDayOfCurrentMonth, moment(firstDayOfCurrentMonth).endOf('month')) &&
      especialCaseToNotInactivateThisMonth.includes(inactivationReason)
    ) {
      dispatch(setEvaluationDate(currentDate));
      return dispatch(setInactivationDate(currentDate));
    }
    return dispatch(setInactivationDate(currentDate));
  };

  const handleEndDateOfTheoricalValidity = (e, { key, action, actionError }) => {
    const { _d: selectedDate, _isValid } = e || {};

    !selectedDate ||
    !_isValid ||
    e.isAfter(MAX_DATE) ||
    e.isBefore(minDateForEndDateOfTheoricalValidity)
      ? dispatch(actionError({ ...modifiedFieldErrors, [key]: true }))
      : dispatch(actionError({ ...modifiedFieldErrors, [key]: false }));

    dispatch(action(selectedDate));

    if (doesGoFromNotValidToValid(values?.validityType, validityType)) {
      dispatch(setEndDateOfValidity(selectedDate));
      dispatch(setReactivationDate(currentDate));
      return dispatch(setReactivateManually(true));
    }
    dispatch(setReactivationDate(null));
    return dispatch(setReactivateManually(false));
  };

  const handleDateChangeAccidentDate = (e, { key, action, actionError }) => {
    const { _d: selectedDate, _isValid } = e || {};

    !selectedDate || !_isValid || e.isAfter(maxDateAccidentDate) || e.isBefore(MIN_DATE)
      ? dispatch(actionError({ ...modifiedFieldErrors, [key]: true }))
      : dispatch(actionError({ ...modifiedFieldErrors, [key]: false }));

    dispatch(action(selectedDate));
  };

  const handleInputChangeInstitutionalPatient = e => {
    const { value } = e.target;

    dispatch(setInstitutionalPatient(value));
    dispatch(setPdfFile({}));
    dispatch(setWasPdfUploaded(false));
    dispatch(setAfpAffiliation(values.afpAffiliation));

    if (value === values.institutionalPatient) {
      dispatch(setWasModifiedAFieldThatRequiresAFile(false));
    }
    if (value !== values.institutionalPatient) {
      dispatch(setWasModifiedAFieldThatRequiresAFile(true));
    }
    return value;
  };

  const handleInputChangeAFPAffilliation = e => {
    const { value } = e.target;

    dispatch(setAfpAffiliation(value));
    dispatch(setPdfFile({}));
    dispatch(setWasPdfUploaded(false));
    dispatch(setInstitutionalPatient(values.institutionalPatient));

    if (value === values.afpAffiliation) {
      dispatch(setWasModifiedAFieldThatRequiresAFile(false));
    }
    if (value !== values.afpAffiliation) {
      dispatch(setWasModifiedAFieldThatRequiresAFile(true));
    }
    return value;
  };

  const handleInputChangePensionType = e => {
    const { value } = e.target;

    dispatch(setPensionType(value));
  };

  const handleInputChangeValidityType = (e, { action }) => {
    const { value } = e.target;
    dispatch(action(value));

    if (!NOT_VALID_PENSION.test(values)) {
      const nextMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
      setMinDateForEndDateOfTheoricalValidity(nextMonthDate);
    }

    if (PLUS_ONE_HUNDRED_TEN_YEARS_VIGENCY_TYPES.test(value) && values.beneficiaryBirthDate) {
      const birthDate = moment(values.beneficiaryBirthDate, 'DD-MM-YYYY').toDate();
      const plusOneHundredTenDate = new Date(
        birthDate.getFullYear() + ONE_HUNDRED_TEN_YEARS,
        birthDate.getMonth(),
        birthDate.getDate()
      );
      dispatch(setEndDateOfValidity(plusOneHundredTenDate));
      dispatch(setEndDateOfTheoricalValidity(plusOneHundredTenDate));
    }

    if (doesRemainAValidPension(values?.validityType, value)) {
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          endDateOfValidity: true,
          endDateOfTheoricalValidity: true,
          inactivationReason: false
        })
      );
    }

    if (doesGoFromValidToNotValid(values?.validityType, value)) {
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          endDateOfValidity: true,
          endDateOfTheoricalValidity: false,
          inactivationReason: true
        })
      );
    }

    if (doesGoFromNotValidToValid(values?.validityType, value)) {
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          endDateOfValidity: false,
          endDateOfTheoricalValidity: true,
          inactivationReason: false
        })
      );
    }
  };

  const handleInputChangeInactivationReason = e => {
    const { value } = e.target;

    dispatch(setIsInactivationReasonChange(true));

    !value || value === inactivationReasonOptions[0]
      ? dispatch(setModifiedFieldErrors({ ...modifiedFieldErrors, inactivationReason: true }))
      : dispatch(setModifiedFieldErrors({ ...modifiedFieldErrors, inactivationReason: false }));

    dispatch(setInactivationReason(value));

    if (NOT_VALID_PENSION.test(validityType))
      dispatch(setModifiedFieldErrors({ ...modifiedFieldErrors, inactivationReason: false }));
  };

  const endDateOfValidityErrorMessage = () => {
    if (!endDateOfValidity) return 'El valor no se ha ingresado';
    if (!isDateChange(endDateOfValidity)) return 'Debe seleccionar un  nuevo valor';

    return 'El valor es inválido';
  };

  const endDateOfTheoricalValidityErrorMessage = () => {
    if (!endDateOfTheoricalValidity) return 'El valor no se ha ingresado';
    if (!isDateChange(endDateOfTheoricalValidity)) return 'Debe seleccionar un  nuevo valor';

    return 'El valor es inválido';
  };

  const inactivationReasonErrorMessage = () => {
    if (!inactivationReason) return 'El valor no se ha ingresado';
    if (!isInactivationReasonChange) return 'Debe seleccionar un  nuevo valor';

    return 'Debe seleccionar una opción';
  };

  const handleFileUpload = usePdfUpload({
    router,
    progress,
    onErrorSnackbar,
    onSuccessSnackbar,
    fileUploadInput,
    modifiedField: { values, institutionalPatient, afpAffiliation, attachNeurologicalCertificate },
    dispatch
  });

  const handleUploadButton = () => {
    fileUploadInput.current.value = '';
    fileUploadInput.current.click();
  };

  const rowFormation = [
    [
      {
        key: 'validityType',
        name: 'Tipo de vigencia',
        type: 'select',
        options: validityTypeOptions,
        condition: isValidityTypeEditable(isCronExecuted) && isAdminOrPecBoss(loggedUser),
        selectedValue: validityType,
        handleInputChange: handleInputChangeValidityType,
        dispatch,
        action: setValidityType,
        actionError: setModifiedFieldErrors,
        modifiedField: validityType,
        modifiedFieldErrors
      },
      { key: 'pensionStartDate', name: 'Fecha de inicio de pensión' },
      {
        key: 'institutionalPatient',
        name: 'Paciente institucional',
        type: 'select',
        options: ['Si', 'No'],
        handleInputChange: handleInputChangeInstitutionalPatient,
        selectedValue: institutionalPatient,
        condition: false
      }
    ],
    [
      {
        key: 'endDateOfValidity',
        name: 'Fecha de fin de vigencia',
        type: 'picker',
        toWrite: endDateOfValidity,
        handleInputChange: handledEndDateOfValidityChange,
        condition: enableEndDateOfValidity() && isAdminOrPecBoss(loggedUser),
        isRequired:
          doesGoFromValidToNotValid(values?.validityType, validityType) ||
          doesRemainAValidPension(values?.validityType, validityType),
        errorMessage: endDateOfValidityErrorMessage(),
        dispatch,
        action: setEndDateOfValidity,
        actionError: setModifiedFieldErrors,
        modifiedField: endDateOfValidity,
        modifiedFieldErrors,
        minDate: doesRemainAValidPension(values?.validityType, validityType)
          ? minDateNextMonth
          : MIN_DATE,
        maxDate: MAX_DATE,
        label: LABEL,
        classes
      },
      {
        key: 'pensionType',
        name: 'Tipo de pensión',
        type: 'select',
        options: changePensionTypeOptions(pensionType),
        selectedValue: pensionType,
        condition: isPensionTypeEditable(pensionType) && isAdmin(loggedUser),
        handleInputChange: handleInputChangePensionType
      },
      { key: 'healthAffiliation', name: 'Afiliación de salud' }
    ],
    [
      {
        key: 'endDateOfTheoricalValidity',
        name: 'Fecha de fin de vigencia teórica',
        type: 'picker',
        toWrite: endDateOfTheoricalValidity,
        handleInputChange: handleEndDateOfTheoricalValidity,
        condition: enableEndDateOfTheoricalValidity(),
        isRequired:
          doesGoFromNotValidToValid(values?.validityType, validityType) ||
          doesRemainAValidPension(values?.validityType, validityType),
        errorMessage: endDateOfTheoricalValidityErrorMessage(),
        dispatch,
        action: setEndDateOfTheoricalValidity,
        actionError: setModifiedFieldErrors,
        modifiedField: endDateOfTheoricalValidity,
        modifiedFieldErrors,
        minDate: minDateForEndDateOfTheoricalValidity,
        maxDate: MAX_DATE,
        label: LABEL,
        classes
      },

      { key: 'resolutionNumber', name: 'Número de resolución' },
      { key: 'currentCapital', name: 'Capital vigente' }
    ],
    [
      {
        key: 'inactivationReason',
        name: 'Motivo de inactivación',
        type: 'select',
        options: inactivationReasonOptions,
        selectedValue: inactivationReason,
        condition: enableInactivationReason() && isAdminOrPecBoss(loggedUser),
        isRequired: doesGoFromValidToNotValid(values?.validityType, validityType),
        modifiedFieldErrors,
        actionError: setModifiedFieldErrors,
        errorMessage: inactivationReasonErrorMessage(),
        handleInputChange: handleInputChangeInactivationReason
      },
      { key: 'accidentNumber', name: 'Número de siniestro' },
      {
        key: 'numberOfCharges',
        name: 'Número de cargas familiares'
      }
    ],
    [
      {
        key: 'cun',
        name: 'CUN',
        type: 'text',
        toWrite: cun,
        handleInputChange: handleFieldChange,
        condition: true,
        errorMessage: 'El valor es inválido',
        validation: ALPHANUMERIC,
        formatter: alphaNumericSanitizer,
        dispatch,
        action: setCun,
        actionError: setModifiedFieldErrors,
        modifiedField: cun,
        modifiedFieldErrors,
        maxLength: CUN_MAX_LENGHT
      },
      { key: 'disabilityStartDate', name: 'Fecha de inicio de incapacidad' },
      { key: 'disabilityDegree', name: 'Grado de incapacidad' }
    ],
    [
      {
        key: 'accidentDate',
        name: 'Fecha de accidente',
        type: 'picker',
        toWrite: accidentDate,
        handleInputChange: handleDateChangeAccidentDate,
        condition: true,
        errorMessage: !accidentDate ? 'El valor no se ha ingresado' : 'El valor es inválido',
        dispatch,
        action: setAccidentDate,
        actionError: setModifiedFieldErrors,
        modifiedField: accidentDate,
        modifiedFieldErrors,
        minDate: MIN_DATE,
        maxDate: maxDateAccidentDate,
        label: LABEL,
        classes
      },
      { key: 'disabilityType', name: 'Tipo de incapacidad' },
      { key: 'transient', name: 'Transitoria' }
    ],
    [
      {
        key: 'basePension',
        name: 'Pensión base',
        type: 'text',
        toWrite: basePension,
        handleInputChange: handleAmountChange,
        condition: true,
        errorMessage: 'El valor es inválido',
        validation: CHILEAN_AMOUNT_PATTERN,
        maxLength: MAX_AMOUNT_LENGTH,
        formatter: formatChileanAmount,
        dispatch,
        action: setBasePension,
        actionError: setModifiedFieldErrors,
        modifiedField: basePension,
        modifiedFieldErrors,
        isRequired: true
      },
      { key: 'resolutionDate', name: 'Fecha de resolución' },
      {
        key: 'afpAffiliation',
        name: 'Afiliación AFP',
        type: 'select',
        options: afpList,
        handleInputChange: handleInputChangeAFPAffilliation,
        selectedValue: afpAffiliation,
        condition:
          institutionalPatient === values.institutionalPatient && !agreesToChangeCollectorRut
      }
    ]
  ];

  useEffect(() => {
    dispatch(setWasPdfUploaded(false));
    dispatch(setPdfFile({}));
    dispatch(setWasModifiedAFieldThatRequiresAFile(false));
    checkCronExecution(CronMark)(setIsCronExecuted);
    getCurrentDate(dispatch);
    changeValidityTypeOptions(pensionType, setValidityTypeOptions);
    changeInactivationByDisabilityOptions(pensionType, setInactivationReasonOptions);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isPensionerWithFamilyCharges(pensionType)) {
      dispatch(setPensionTypeDueToCharges(true));
      return;
    }
    dispatch(setPensionTypeDueToCharges(false));
    changeValidityTypeOptions(pensionType, setValidityTypeOptions);
    changeInactivationByDisabilityOptions(pensionType, setInactivationReasonOptions);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pensionType]);

  useEffect(() => {
    const yesterday = moment(currentDate)
      .subtract(1, 'days')
      .toDate();
    const firstDayInMonth = moment(currentDate)
      .startOf('month')
      .toDate();
    const nextMonth = moment(currentDate)
      .add(1, 'month')
      .startOf('month')
      .toDate();

    setMinDateNextMonth(nextMonth);
    setMaxDateAccidentDate(yesterday);
    setFirstDayOfCurrentMonth(firstDayInMonth);
  }, [currentDate]);

  useEffect(() => {
    if (
      doesGoFromValidToNotValid(values?.validityType, validityType) &&
      (!inactivationReason || inactivationReason === inactivationReasonOptions[0])
    ) {
      dispatch(
        setModifiedFieldErrors({
          ...modifiedFieldErrors,
          inactivationReason: true
        })
      );
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [validityType]);

  return (
    <>
      {displayImportAndAlert() &&
        alertModifiedField({
          values,
          afpAffiliation,
          institutionalPatient,
          attachNeurologicalCertificate
        })}
      <Card className={classes.cardContainer}>
        <CardHeader title="Información pensión" className={classes.cardHeader} />
        <div className={classes.divFileUpload}>
          {displayImportAndAlert() && (
            <ImportButton
              classes={{ formControl: classes.formControl }}
              errorMessage={[]}
              onClick={handleUploadButton}
              disabled={!activateImportButton()}
            />
          )}
        </div>
        <Divider />
        <CardContent className={classes.content}>
          {createTable({
            data: values,
            format: rowFormation,
            editable: editable && onlineStatus,
            panelName: 'pension'
          })}
        </CardContent>
      </Card>

      <input
        ref={fileUploadInput}
        type="file"
        style={{ display: 'none' }}
        id="fileUpload"
        disabled={!activateImportButton()}
        onChange={v => v.target.files.length && handleFileUpload(v.target.files[0])}
        accept=".pdf"
      />
    </>
  );
};

Pension.propTypes = {
  values: PropTypes.shape({}).isRequired
};

export default Pension;
