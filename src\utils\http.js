/* eslint-disable no-param-reassign */
/* eslint-disable import/prefer-default-export */
import axios from 'axios';

const { REACT_APP_API_URL } = process.env;

const headers = {
  'Content-Type': 'application/json; charset=utf-8'
};

export const publicInstance = axios.create({
  baseURL: REACT_APP_API_URL,
  timeout: 300000,
  crossdomain: true,
  headers
});

publicInstance.interceptors.response.use(
  response => response,
  error => {
    if (error?.response?.data?.message?.toLowerCase().includes('token')) {
      localStorage.removeItem('pec.token');
      window.location.href = '/';
    }
    throw error;
  }
);

publicInstance.interceptors.request.use(
  config => {
    const loggedUser = JSON.parse(localStorage.getItem('pec.token'));

    if (loggedUser?.token) {
      config.headers.Authorization = loggedUser.token;
    }

    return config;
  },
  err => {
    return Promise.reject(err);
  }
);

export const status = {
  OK: 200,
  CREATED: 201,
  UPDATED: 200,
  DELETED: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOTFOUND: 404,
  INTERNAL_SERVER_ERROR: 500
};
