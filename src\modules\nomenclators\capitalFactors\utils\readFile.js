/* eslint-disable no-useless-escape */
import { getEncoding } from '../../../../utils/encodingFile';

const NOT_VALID_CSV_CHARS = /[^\s\n+\-*/=@#&%$!¡¿?ºª.:;,_|><´`¨""{})'^[\]~áéíóúàèìòùãẽĩõñũỹg̃äöüëïâêîôûçğşa-z0-9]/im;

const NO_DATA = 'Archivo sin data';
const DAMAGED_FILE = 'Archivo dañado';

const readFile = async (file, createJSON) => {
  const encoding = await getEncoding(file);
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const { result } = reader;
      if (NOT_VALID_CSV_CHARS.test(result)) {
        return reject(new Error(DAMAGED_FILE));
      }
      const splittedResult = result.split('\n');
      splittedResult.shift();

      const rows = splittedResult.filter(row => row && row.length);
      if (!rows.length) {
        return reject(new Error(NO_DATA));
      }

      const jsonData = rows.map((row, _i) =>
        createJSON(row.replace(/\"(\d+)[,\.](\d*)\"/g, '$1.$2').split(/,|;/))
      );
      return resolve(jsonData);
    };
    reader.onerror = () => {
      return reject(new Error(DAMAGED_FILE));
    };

    return reader.readAsText(file, encoding);
  });
};

export default readFile;
