import {
  setIsValidDateRangeReports,
  setIsValidDateReports,
  setIsValidEndingDate,
  setIsValidStartingDate
} from '../actions';

const isBeyond = (date, currentDate) => {
  return (
    date.getFullYear() > currentDate.getFullYear() ||
    (date.getFullYear() === currentDate.getFullYear() && date.getMonth() > currentDate.getMonth())
  );
};

const isBelow = (date, minDate) => {
  return (
    date.getFullYear() < minDate.getFullYear() ||
    (date.getFullYear() === minDate.getFullYear() && date.getMonth() < minDate.getMonth())
  );
};

const isInRange = (date, currentDate, minDate) => {
  return !isBelow(date, minDate) && !isBeyond(date, currentDate);
};

const areBothInRange = (isStarting, isEnding) => dispatch => {
  dispatch(setIsValidDateReports(isStarting && isEnding));
};

const isStartingLEQEnding = (startingDate, endingDate, dispatch) => {
  isBeyond(startingDate, endingDate)
    ? dispatch(setIsValidDateRangeReports(false))
    : dispatch(setIsValidDateRangeReports(true));
};

const validateDate = (
  startingDate,
  endingDate,
  currentDate,
  minDate,
  doesStartExist,
  doesEndExist
) => dispatch => {
  const isInRangeStarting = isInRange(startingDate, currentDate, minDate);
  const isInRangeEnding = isInRange(endingDate, currentDate, minDate);
  areBothInRange(isInRangeStarting, isInRangeEnding)(dispatch);
  isInRangeStarting
    ? dispatch(setIsValidStartingDate(true))
    : dispatch(setIsValidStartingDate(false));
  isInRangeEnding ? dispatch(setIsValidEndingDate(true)) : dispatch(setIsValidEndingDate(false));
  isStartingLEQEnding(startingDate, endingDate, dispatch);
  if (!doesStartExist || !doesEndExist) {
    !doesStartExist && setIsValidStartingDate(false);
    !doesEndExist && setIsValidEndingDate(false);
  }
};

export default validateDate;
