/* eslint-disable no-console */

import { axiosRequest, axiosTxt } from '../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

//--------------------------------------------------------
// eslint-disable-next-line import/prefer-default-export
export const getLatestBankFile = () =>
  axiosRequest.get(`${api}/reports/check-latest-bank-file`).catch(err => {
    console.error(err);
    return err;
  });

export const downloadBankFile = async uuid => {
  const { data, isError = false } = await axiosTxt
    .get(`${api}/reports/download-bank-file/${uuid}`)
    .catch(err => {
      console.error(err);
      return { data: [], isError: true };
    });
  return { isError, data };
};
