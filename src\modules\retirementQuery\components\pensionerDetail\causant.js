/* eslint-disable react/prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import createTable from '../../utils/createTable';
import useStyles from './style';

const Causant = ({ values, editable }) => {
  const classes = useStyles();

  const rowFormation = [
    [
      {
        key: 'causantRut',
        name: 'RUT causante'
      }
    ],
    [
      {
        key: 'causantFullName',
        name: 'Nombre causante'
      }
    ]
  ];

  return (
    <Card className={classes.cardContainer}>
      <CardHeader title="Información causante" className={classes.cardHeader} />
      <Divider />
      <CardContent className={classes.content}>
        {createTable({ data: values, format: rowFormation, editable, panelName: 'causant' })}
      </CardContent>
    </Card>
  );
};

Causant.propTypes = {
  values: PropTypes.shape({}).isRequired
};

export default Causant;
