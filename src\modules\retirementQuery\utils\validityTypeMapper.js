const DISABILITY_REASON = /no vigente/i;

const defaultOptionInactivationReason = 'Seleccione motivo de inactivación';

const listOfDisabilityPensions = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i
];
const listOfWidowhoodPensions = [
  /Pensi[oó]n de viudez con hijos/i,
  /Pensi[oó]n de viudez sin hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial sin hijos/i
];
const listOfOrphanhoodPensions = [
  /Pensi[oó]n de orfandad de padre y madre/i,
  /Pensi[oó]n por orfandad/i
];

const validityTypesForDisability = [
  'Vigente hasta la jubilación',
  'Vigente vitalicia',
  'No vigente'
];
const validityTypesForWidowhood = ['Vigente viudez', 'Vigente vitalicia', 'No vigente'];
const validityTypesForOrphanhood = ['Vigente orfandad', 'Vigente vitalicia', 'No vigente'];

const listInactivationReasonForDisabilityPensions = [
  defaultOptionInactivationReason,
  'Fallecimiento',
  'Resolución definitiva',
  'Alta médica',
  'Jubilación'
];
const listInactivationReasonForWidowhoodPensions = [
  defaultOptionInactivationReason,
  'Fallecimiento',
  'Vencimiento de certificado de estudios',
  'Expiración de año de pago',
  'Matrimonio'
];
const listInactivationReasonForOrphanhoodPensions = [
  defaultOptionInactivationReason,
  'Fallecimiento',
  'Vencimiento de certificado de estudios',
  'Cumplimiento de edad límite'
];

const inactivationReasonByWidowhood = [
  /Fallecimiento/i,
  /Vencimiento de certificado de estudios/i,
  /Expiraci[oó]n de año de pago/i,
  /Matrimonio/i
];

const especialCaseToNotInactivateThisMonth = [
  'Resolución definitiva',
  'Alta médica',
  'Jubilación',
  'Matrimonio'
];

const isValidityTypeEditable = IsCronExecuted => IsCronExecuted;
const isEndDateOfValidityEditable = (validityType, IsCronExecuted) => {
  return DISABILITY_REASON.test(validityType) && IsCronExecuted;
};
const isInactivationByDisabilityEditable = (validityType, IsCronExecuted) => {
  return DISABILITY_REASON.test(validityType) && IsCronExecuted;
};
const isEndDateOfTheoricalValidityEditable = IsCronExecuted => IsCronExecuted;

const evaluatePension = (listOfPension, currentPensionType) => {
  return listOfPension.some(pension => pension.test(currentPensionType));
};

const changeValidityTypeOptions = (currentPensionType, setOptions = x => x) => {
  if (evaluatePension(listOfDisabilityPensions, currentPensionType)) {
    return setOptions(validityTypesForDisability);
  }
  if (evaluatePension(listOfWidowhoodPensions, currentPensionType)) {
    return setOptions(validityTypesForWidowhood);
  }
  if (evaluatePension(listOfOrphanhoodPensions, currentPensionType)) {
    return setOptions(validityTypesForOrphanhood);
  }

  return [];
};

const changeInactivationByDisabilityOptions = (currentPensionType, setOptions = x => x) => {
  if (evaluatePension(listOfDisabilityPensions, currentPensionType)) {
    return setOptions(listInactivationReasonForDisabilityPensions);
  }
  if (evaluatePension(listOfWidowhoodPensions, currentPensionType)) {
    return setOptions(listInactivationReasonForWidowhoodPensions);
  }
  if (evaluatePension(listOfOrphanhoodPensions, currentPensionType)) {
    return setOptions(listInactivationReasonForOrphanhoodPensions);
  }

  return [];
};

const isInactivationByWidowhoodEditable = reason =>
  inactivationReasonByWidowhood.some(typeReason => typeReason.test(reason));

export {
  isValidityTypeEditable,
  isInactivationByWidowhoodEditable,
  isInactivationByDisabilityEditable,
  changeValidityTypeOptions,
  changeInactivationByDisabilityOptions,
  isEndDateOfValidityEditable,
  isEndDateOfTheoricalValidityEditable,
  especialCaseToNotInactivateThisMonth
};
