/* eslint-disable no-magic-numbers */
import { makeStyles } from '@material-ui/styles';
import gradients from '../../../utils/gradients';

const styles = makeStyles(theme => ({
  root: {
    height: '100%',
    display: 'flex'
  },
  card: {
    height: '300px',
    width: '600px',
    maxWidth: '100%',
    overflow: 'unset',

    position: 'absolute',
    top: 'calc(50% - 150px)',
    left: `calc(50% - 300px)`
  },
  content: {
    padding: theme.spacing(8, 4, 3, 4)
  },
  icon: {
    backgroundImage: gradients.green,
    color: theme.palette.white,
    borderRadius: theme.shape.borderRadius,
    position: 'relative',
    top: -32,
    left: theme.spacing(3),
    height: 64,
    width: 64,
    fontSize: 32
  },
  loginForm: {
    marginTop: theme.spacing(3)
  },
  divider: {
    margin: theme.spacing(2, 0)
  },
  achsBackground: {
    backgroundImage: `url(${'/images/logos/achs_logo.jpg'})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    position: 'absolute',
    marginTop: '-66px',
    width: '100%',
    height: '101%',
    opacity: 0.8
  }
}));

export default styles;
