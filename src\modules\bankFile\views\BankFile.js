/* eslint-disable import/no-unresolved */
/* eslint-disable prefer-template */
/* eslint-disable import/prefer-default-export */
/* eslint-disable react/prop-types */

import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Alert } from '@material-ui/lab';
import { Card, Grid, Typography } from '@material-ui/core';

import useOnlineStatus from '@rehooks/online-status';
import { useProgress } from 'components';
import { DownloadForm, Header } from './components';
import useDataDownload from '../hooks/getBankFile';
import { resetBankFile, setBankFile } from '../actions';
import { getLatestBankFile } from '../services/bankFileServices';
import useStyles from './styles';

const HTTP_OK_STATUS = 200;
const TITLE = 'Archivo Banco';
const SUBTITLE = 'Configuraciones / Reportería';
const NO_FILE_MESSAGE = 'No hay archivo';

const alertText =
  'Al momento de la generación del archivo banco, los Jefe de PEC serán notificados a través de un email';
const titleNewCurrent = 'Último archivo banco generado';

const BankFile = ({ onErrorSnackbar }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();

  const latestFile = useSelector(store => store.bankFile.latestFile);
  const isDownloading = useSelector(store => store.bankFile.isDownloading);

  useEffect(() => {
    getLatestBankFile().then(res => {
      const { status, data } = res;
      if (status === HTTP_OK_STATUS) {
        dispatch(setBankFile(data.result));
      } else {
        dispatch(resetBankFile);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const progress = useProgress({
    showAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: true } },
    hideAction: { type: 'SET_PROGRESS_STATUS', data: { isInProgress: false } }
  });

  const handleDownloadBankFile = useDataDownload({
    dispatch,
    latestFile,
    progress,
    onErrorSnackbar
  });

  return (
    <>
      <Grid>
        <Header subtitle={SUBTITLE} title={TITLE} />
      </Grid>
      <Grid>
        <Grid className={classes.grid}>
          <Alert className={classes.alertBar} severity="error" color="info">
            {alertText}
          </Alert>
        </Grid>
        <Card className={classes.cardStyle}>
          <Grid className={classes.gripTop}>
            <Grid className={classes.grid}>
              <Grid className={classes.grid}>
                <Typography className={classes.title}>{titleNewCurrent}</Typography>
              </Grid>

              <DownloadForm
                label={latestFile ? 'Descargar' : NO_FILE_MESSAGE}
                value={latestFile ? latestFile.virtualPath?.split('/').pop() : ''}
                tooltipTitle="Descargar"
                onClick={handleDownloadBankFile}
                variant="contained"
                color="primary"
                disabled={!onlineStatus || isDownloading || !latestFile}
              />
            </Grid>
          </Grid>
        </Card>
      </Grid>
    </>
  );
};

export { BankFile };
