/* eslint-disable no-console */
/* eslint-disable react/prop-types */
/* eslint-disable import/no-unresolved */
import { Grid } from '@material-ui/core';
import { HeaderContent, Page } from 'components';
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { loadCaja, processCaja } from '../services';
import { CajaTable } from './components';
import useStyles from './styles';

const TopesMaximoAndUnidadesReajustables = props => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const [isLoading, setLoading] = useState(true);
  const state = {
    data: useSelector(store => store.caja.data)
  };

  const { onSuccessSnackbar, onErrorSnackbar, role } = props;

  const handleLoad = () => {
    return !isLoading
      ? state.data
      : async () => {
          return dispatch(loadCaja()).then(caja => {
            setLoading(false);
            return {
              data: caja,
              totalCount: caja.length
            };
          });
        };
  };
  const handleProcess = (okMessage, erroMessage, operation) => async (newData, oldData) => {
    try {
      if (operation === 'update' && oldData) {
        const result = await dispatch(processCaja(oldData, newData, operation))
          .then(() => {
            onSuccessSnackbar(okMessage);
            return true;
          })
          .catch(_error => {
            let messageError;
            messageError = erroMessage;
            onErrorSnackbar(messageError);
            throw new Error(messageError);
          });
        if (!result) return false;
      }
      return true;
    } catch ({ message }) {
      onErrorSnackbar(message);
      throw new Error(message);
    }
  };

  return (
    <Page className={classes.root} title="Topes, Máximo y Unidades Reajustables">
      <Grid container justify="space-between" alignItems="center" className={classes.headerContent}>
        <HeaderContent
          overline="Configuraciones / Mantenedores"
          title="Topes, Máximo y Unidades Reajustables"
        />
      </Grid>
      <CajaTable
        onErrorSnackbar={onErrorSnackbar}
        userRole={role}
        data={handleLoad()}
        onCreate={handleProcess(
          'Los valores se guardaron correctamente',
          'Error en actualización de registro',
          'create'
        )}
        onUpdate={handleProcess(
          'Los valores se guardaron correctamente',
          'Error en actualización de registro',
          'update'
        )}
        onDelete={handleProcess('Registro eliminado', 'Error en eliminación de registro', 'delete')}
      />
    </Page>
  );
};

TopesMaximoAndUnidadesReajustables.propTypes = {
  onSuccessSnackbar: PropTypes.func.isRequired,
  onErrorSnackbar: PropTypes.func.isRequired
};

// eslint-disable-next-line import/prefer-default-export
export { TopesMaximoAndUnidadesReajustables };
