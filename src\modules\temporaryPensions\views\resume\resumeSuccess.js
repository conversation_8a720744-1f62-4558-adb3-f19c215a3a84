import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/core/styles';
import { TableCell, TableRow, Typography, Button, Grid } from '@material-ui/core';
import Table from 'components/Table';
import useOnlineStatus from '@rehooks/online-status';
import { AppContext } from '../../../../provider/app';

const { resumeStats } = require('./resumeStats');

const useStyles = makeStyles(theme => ({
  principalErrors: {
    display: 'flex',
    justifyContent: 'center',
    margin: '1rem'
  },
  warnings: { marginTop: 30 },
  pensionList: { margin: '1rem 2rem' },
  containerResume: { justifyContent: 'space-between', display: 'flex', margin: 25 },
  pensionTypes: { flex: 3 },
  pensionLink: { flex: 2 },
  buttonPanel: {
    margin: '30px',
    display: 'flex',
    flexDirection: 'column',
    padding: '20px',
    border: '1px solid #eee',
    alignItems: 'center'
  },
  buttonFrame: {
    display: 'flex',
    justifyContent: 'space-between',
    maxWidth: '300px',
    marginTop: '25px',
    '& div': {
      textAlign: 'center'
    },
    '& $button': {
      margin: '5px'
    }
  },
  acceptButton: {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.primary.light,
      color: theme.palette.primary.contrastText
    }
  },
  cancelButton: {
    backgroundColor: theme.palette.error.main,
    color: theme.palette.error.contrastText,
    '&:hover': {
      backgroundColor: theme.palette.error.light,
      color: theme.palette.error.contrastText
    }
  }
}));

const warningsHeaders = [{ label: 'Fila' }, { label: 'Advertencias' }];
const renderTableData = result => {
  return result.map((item, index) => {
    const { row, message } = item;

    return (
      <TableRow hover key={`${index + row}`}>
        <TableCell align="left" style={{ width: '10%' }}>
          {row}
        </TableCell>
        <TableCell align="left" style={{ width: '90%' }}>
          {message}
        </TableCell>
      </TableRow>
    );
  });
};

const renderWarningTableHeader = () =>
  warningsHeaders.map((key, index) => (
    <TableCell key={`${index + key.label}`}>
      <b>{key.label}</b>
    </TableCell>
  ));

const ResumeSuccess = props => {
  const classes = useStyles();
  const { data, warnings = [], title, linkProps } = props;
  const { loggedUser = {} } = useContext(AppContext);
  const { name = '' } = loggedUser;
  const onlineStatus = useOnlineStatus();
  const { pensions, avgPension, linkedPensions, maxPension } = resumeStats(data);

  const actualDate = new Date().toLocaleDateString('es', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric'
  });
  const actualMonth = new Date().toLocaleString('es', { month: 'long' });
  const actualYear = new Date().getFullYear();
  const renderAvgPension = avgPension.toLocaleString('es-CL', {
    style: 'decimal'
  });
  const renderMaxPension = maxPension.toLocaleString('es-CL', {
    style: 'decimal'
  });

  return (
    <>
      <div className={classes.principalErrors}>
        <Typography>
          <b>{linkProps ? linkProps.title : title}</b>
        </Typography>
      </div>
      <div className={classes.containerResume}>
        <div className={classes.pensionTypes}>
          <Typography>Total de pensiones por tipo: </Typography>
          <ol className={classes.pensionList} start="1">
            {pensions.map(([label, value]) => (
              <li key={label}>
                <Typography>{` ${label}: ${value}`}</Typography>
              </li>
            ))}
          </ol>
        </div>
        <div className={classes.pensionLink}>
          <div>
            <Typography>
              <b>{`Fecha: ${actualDate}`}</b>
            </Typography>
          </div>
          <div>
            <Typography>
              <b>{`Usuario: ${name}`}</b>
            </Typography>
          </div>
          <div>
            <ol className={classes.pensionList} start="1">
              <Typography>{`Total de pensiones a enlazar: ${linkedPensions}`}</Typography>
              <Typography>{`Monto máximo de pensión base a enlazar: ${renderMaxPension}`}</Typography>
              <Typography>{`Promedio del monto base de pensiones: ${renderAvgPension}`}</Typography>
            </ol>
          </div>
        </div>
      </div>
      <div className={classes.warnings}>
        {warnings.length > 0 && (
          <Table
            renderHeader={renderWarningTableHeader}
            renderTable={() => renderTableData(warnings)}
          />
        )}
      </div>
      <br />
      {linkProps && (
        <div className={classes.buttonPanel}>
          <Typography align="center">
            {`¿Está seguro que desea realizar el enlace de nuevas pensiones del mes ${actualMonth} del año ${actualYear}?`}
          </Typography>
          <Grid container alignItems="center" className={classes.buttonFrame}>
            <Grid item container xs={6} justifyContent="center">
              <Button
                disabled={!onlineStatus}
                onClick={linkProps.onLink}
                className={classes.acceptButton}
              >
                Aceptar
              </Button>
            </Grid>
            <Grid item container xs={6} justifyContent="center">
              <Button onClick={linkProps.onCancel} className={classes.cancelButton}>
                Cancelar
              </Button>
            </Grid>
          </Grid>
        </div>
      )}
    </>
  );
};

// data, warnings, title, linkProps
ResumeSuccess.propTypes = {
  data: PropTypes.arrayOf(PropTypes.any).isRequired,
  warnings: PropTypes.arrayOf(PropTypes.any),
  title: PropTypes.string,
  linkProps: PropTypes.oneOfType([
    PropTypes.shape({
      title: PropTypes.string,
      onLink: PropTypes.func,
      onCancel: PropTypes.func
    }),
    PropTypes.bool
  ]).isRequired
};
ResumeSuccess.defaultProps = {
  warnings: [],
  title: ''
};

export default ResumeSuccess;
