import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { Snackbar } from '@material-ui/core';
import MuiAlert from '@material-ui/lab/Alert';
import useRouter from 'utils/useRouter';

function Alert(props) {
  return <MuiAlert elevation={6} variant="filled" {...props} />;
}

const SnackbarManager = ({
  text,
  error,
  handleRoute,
  successSnackbar,
  setSuccessSnackbar,
  errorSnackbar,
  setErrorSnackbar
}) => {
  const router = useRouter();

  const handleSuccessSnackbar = () => {
    setSuccessSnackbar(false);
    handleRoute && router.history.push('/home');
  };

  const handleErrorSnackbar = () => {
    setErrorSnackbar(false);
  };

  return (
    <Fragment>
      <Snackbar open={successSnackbar} autoHideDuration={5000} onClose={handleSuccessSnackbar}>
        <Alert onClose={handleSuccessSnackbar} severity="success">
          {text}
        </Alert>
      </Snackbar>
      <Snackbar open={errorSnackbar} autoHideDuration={5000} onClose={handleErrorSnackbar}>
        <Alert onClose={handleErrorSnackbar} severity="error">
          {error}
        </Alert>
      </Snackbar>
    </Fragment>
  );
};

SnackbarManager.propTypes = {
  successSnackbar: PropTypes.bool,
  setSuccessSnackbar: PropTypes.func,
  errorSnackbar: PropTypes.bool,
  setErrorSnackbar: PropTypes.func,
  text: PropTypes.string,
  error: PropTypes.string,
  handleRoute: PropTypes.bool
};
SnackbarManager.defaultProps = {
  text: 'Las nuevas pensiones del mes se enlazaron correctamente',
  error: 'Error: Validación de datos',
  successSnackbar: false,
  handleRoute: true,
  setSuccessSnackbar: () => { /*any*/ },
  errorSnackbar: false,
  setErrorSnackbar: () => { /*any*/ }
};

export default SnackbarManager;
