import { unformatRut } from '../utils/formatters';

const buildQuery = {
  beneficiaryRut: value => ({ beneficiary: { rut: `${unformatRut(value)}` } }),
  causantRut: value => ({ causant: { rut: `${unformatRut(value)}` } }),
  collectorRut: value => ({ collector: { rut: `${unformatRut(value)}` } }),
  pensionCodeId: value => ({ pensionCodeId: `${value}` })
};

const searchOptions = {
  pensionCodeId: 'Número de pensión',
  beneficiaryRut: 'RUT del beneficiario',
  causantRut: 'RUT del causante',
  collectorRut: 'RUT del cobrante'
};

const optionKeys = Object.keys(searchOptions);

const options = ['', ...optionKeys.map(x => searchOptions[x])];

const queryToSelect = chosenOption => optionKeys.filter(x => searchOptions[x] === chosenOption);

export { queryToSelect, buildQuery, searchOptions, options };
