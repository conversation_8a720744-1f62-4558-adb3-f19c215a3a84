/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react/prop-types */
import React, { Fragment, useState, useEffect } from 'react';
import { Grid } from '@material-ui/core';
import { useDispatch, useSelector } from 'react-redux';

import Header from '../../components/Header';
import UsersTable from '../components/UsersTable';
import { deleteNomenclatorUser } from '../actions';
import { processUser, loadUsers } from '../services/users.service';
import { getRoles } from '../../permissionsAndRoles/services/roles.service';

import styles from './styles';

const TITLE = 'Usuarios';
const SUBTITLE = 'Configuraciones / Sistemas';

const Users = ({ onSuccessSnackbar, onErrorSnackbar, role: userRole }) => {
  const dispatch = useDispatch();
  const classes = styles();

  const data = useSelector(store => store.users.data);

  const [enabledButton, setEnabledButton] = useState(false);
  const [roles, setRoles] = useState([]);
  const [role, setRole] = useState('');

  const CONFLICT = 409;
  const handleConflict = oldData => !oldData.user && dispatch(deleteNomenclatorUser(oldData));

  const onRoleFilterChange = ({ target }) => {
    const { value } = target;
    if (!value) return setRole('');
    return setRole(value);
  };

  useEffect(() => {
    const fetchData = async () => {
      await getRoles(setRoles);
      loadUsers(dispatch);
    };
    fetchData();
  }, []);

  const processUserError = (error, erroMessage, operation, oldData) => {
    let messageError;
    if (+error.message === CONFLICT) {
      if (operation === 'update') handleConflict(oldData);
      messageError = 'Usuario duplicado, el nombre de usuario ingresado ya existe';
      onErrorSnackbar(messageError);
      setEnabledButton(true);
    } else {
      messageError = erroMessage;
      onErrorSnackbar(messageError);
      setEnabledButton(true);
    }
    return messageError;
  };

  const handleProcess = (okMessage, erroMessage, operation) => async (newData, oldData) => {
    try {
      if (operation === 'create' || operation === 'update' || operation === 'delete') {
        const result = await dispatch(processUser(oldData, newData, operation))
          .then(() => {
            onSuccessSnackbar(okMessage);

            return true;
          })
          .catch(error => {
            let messageError = processUserError(error, erroMessage, operation, oldData);
            throw new Error(messageError);
          });
        if (!result) return false;
      }
      return true;
    } catch (error) {
      onErrorSnackbar(error.message);
      setEnabledButton(true);
      return false;
    }
  };

  return (
    <Fragment>
      <Grid className={classes.root}>
        <Header title={TITLE} subtitle={SUBTITLE} />
        <Grid className={classes.table}>
          <UsersTable
            userRole={userRole}
            enabledButton={enabledButton}
            data={data}
            roles={roles}
            onRoleFilterChange={onRoleFilterChange}
            selectedRole={role}
            onCreate={handleProcess(
              'Los valores se guardaron correctamente',
              'Error en actualización de registro',
              'create'
            )}
            onUpdate={handleProcess(
              'Los valores se guardaron correctamente',
              'Error en actualización de registro',
              'update'
            )}
            onDelete={handleProcess(
              'Registro eliminado',
              'Error en eliminación de registro',
              'delete'
            )}
          />
        </Grid>
      </Grid>
    </Fragment>
  );
};

export default Users;
