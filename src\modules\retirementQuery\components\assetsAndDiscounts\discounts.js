/* eslint-disable react/prop-types */
import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import useOnlineStatus from '@rehooks/online-status';
import { useSelector, useDispatch } from 'react-redux';
import useStyles from '../pensionerDetail/style';
import createTable from '../../utils/createTable';
import { setHealthLoan, setModifiedFieldErrors } from '../../actions';
import { CHILEAN_AMOUNT_PATTERN, formatChileanAmount } from '../../utils/formatters';
import { handleAmountChange } from '../../utils/handlers';

const MAX_AMOUNT_LENGTH = 10;

const Discounts = ({ values, editable }) => {
  const classes = useStyles();
  const onlineStatus = useOnlineStatus();
  const dispatch = useDispatch();

  const healthLoan = useSelector(store => store.queryPensions.healthLoan);
  const modifiedFieldErrors = useSelector(store => store.queryPensions.modifiedFieldErrors);

  const rowFormation = [
    [
      {
        key: 'onePercents',
        name: 'Descuento 1%'
      }
    ],
    [
      {
        key: 'totalSocialCreditDiscounts',
        name: 'Descuentos créditos sociales'
      }
    ],
    [
      {
        key: 'othersDiscountCompensations',
        name: 'Otros descuentos cajas de compensación'
      }
    ],
    [
      {
        key: 'healthLoan',
        name: 'Descuento por préstamo de salud',
        type: 'text',
        toWrite: healthLoan,
        handleInputChange: handleAmountChange,
        dispatch,
        action: setHealthLoan,
        actionError: setModifiedFieldErrors,
        modifiedField: healthLoan,
        maxLength: MAX_AMOUNT_LENGTH,
        condition: true,
        validation: CHILEAN_AMOUNT_PATTERN,
        errorMessage: 'Valor incorrecto',
        modifiedFieldErrors,
        formatter: formatChileanAmount
      }
    ],
    [
      {
        key: 'health',
        name: 'Descuento salud'
      }
    ],
    [
      {
        key: 'afp',
        name: 'Descuento AFP'
      }
    ],
    [
      {
        key: 'totalNonFormulable',
        name: 'Descuentos no formulable (Totales)'
      }
    ],
    [
      {
        key: 'totalDiscounts',
        name: 'Total descuentos'
      }
    ],
    [
      {
        key: 'forTotalNonFormulableDiscounts',
        name: 'Monto retroactivo por descuentos no formulables totales'
      }
    ],
    [
      {
        key: 'indemnityDiscount',
        name: 'Descuento por indemización'
      }
    ],
    [
      {
        key: 'healthDiscountAccrued',
        name: 'Descuento Salud Devengado'
      }
    ],
    [
      {
        key: 'afpDiscountAccrued',
        name: 'Descuento AFP Devengado'
      }
    ],
    [
      {
        key: 'settlement',
        name: 'Finiquito'
      }
    ]
  ];

  return (
    <Card className={classes.cardContainer}>
      <CardHeader title="Descuentos" className={classes.cardHeader} />
      <Divider />
      <CardContent className={classes.content}>
        {createTable({
          data: values,
          editable: editable && onlineStatus,
          format: rowFormation,
          panelName: 'discounts'
        })}
      </CardContent>
    </Card>
  );
};

Discounts.propTypes = {
  values: PropTypes.shape({}).isRequired
};

export default Discounts;
