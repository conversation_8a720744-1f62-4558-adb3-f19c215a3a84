/* eslint-disable react/forbid-prop-types */
import React, { useEffect } from 'react';

import { useSelector } from 'react-redux';
// eslint-disable-next-line import/no-unresolved
import useRouter from 'utils/useRouter';
import Resume from '../resume';

const OrphanhoodResume = _props => {
  const router = useRouter();

  const isError = useSelector(store => store.regularizedPensions.isError);
  const errors = useSelector(store => store.regularizedPensions.errors);

  useEffect(() => {
    if (errors.length === 0 && !isError) {
      router.history.push('/regularizar-pensiones/inactivar-reactivar/orfandad/');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [errors, isError]);

  return <Resume isError={isError} fileDataError={{ errors }} source="horphanhood" />;
};

export default OrphanhoodResume;
