import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import { TextValidator, ValidatorForm } from 'react-material-ui-form-validator';
import { isValidDecimal } from '../../validator/validField';

const word = '0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäöüëïâêîôûçğş';
const regex = `^[${word}\\s\\.\\-',]+$`;
const regRule = new RegExp(regex, 'i');
const matchRule = (value = '') => regRule.test(value);
const SecureTextField = ({
  name,
  id,
  displayName,
  type,
  isValidListener,
  validators,
  errorMessages,
  inputprops,
  ...props
}) => {
  useEffect(() => {
    ValidatorForm.addValidationRule('matchPercentage', isValidDecimal);
    // returned function will be called on component unmount
    return () => {
      ValidatorForm.removeValidationRule('matchPercentage');
    };
  }, []);
  return (
    <>
      <TextValidator
        name={name}
        id={id}
        label={displayName}
        type={type}
        validators={validators}
        errorMessages={errorMessages}
        variant="outlined"
        inputProps={{ ...inputprops }}
        validatorListener={isValidListener}
        {...props}
      />
    </>
  );
};
SecureTextField.propTypes = {
  name: PropTypes.string.isRequired,
  displayName: PropTypes.string.isRequired,
  type: PropTypes.oneOf(['text', 'number', 'email', 'date']),
  isValidListener: PropTypes.func,
  validators: PropTypes.arrayOf(PropTypes.string),
  errorMessages: PropTypes.arrayOf(PropTypes.string),
  inputprops: PropTypes.objectOf(PropTypes.string).isRequired
};
SecureTextField.defaultProps = {
  type: 'text',
  isValidListener: () => { /*any*/ },
  validators: ['required', 'match'],
  errorMessages: ['El valor es requerido', 'El valor es inválido']
};

export { matchRule };
export default SecureTextField;
