# Node.js Express Web App to Linux on Azure
# Build a Node.js Express app and deploy it to Azure as a Linux web app.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript
### nombre del pipeline, se va a ver reflejado en la seccion pipelines -> runs
name: CICD-WEB_$(SourceBranchName)_$(date:yyyyMMdd)$(rev:.r)
### Opcion para que solo se ejecute el pipeline por motivo del Pull Request. Se deben "apagar" los trigger clasicos
trigger: none
### Variables
variables:
  - group: Variables-CICD_WEB
  - group: Variables-CICD_IaC
  - name: vmImageName
    value: 'ubuntu-latest'
stages:
  - stage: Build
    displayName: Build stage
    jobs:
      - job: Build
        displayName: Build
        pool:
          vmImage: 'ubuntu-latest'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '14.x'
            displayName: 'Install Node.js'
          - task: Npm@1
            displayName: 'npm ci'
            inputs:
              command: custom
              verbose: false
              customCommand: ci

          - script: |
              npm run test:cov
            displayName: 'Test'

          # azure unit tests results
          - task: PublishTestResults@2
            condition: succeededOrFailed()
            inputs:
                testResultsFiles: 'junit.xml'
            displayName: Publish Unit Tests Results

          # azure unit coverage results
          - task: PublishCodeCoverageResults@1
            inputs:
                codeCoverageTool: Cobertura
                summaryFileLocation: 'coverage/cobertura-coverage.xml'
            displayName: Publish Unit Tests Coverage Results

          - task: SonarQubePrepare@5
            inputs:
              SonarQube: 'SonarQube_PEC'
              scannerMode: 'CLI'
              configMode: 'manual'
              cliProjectKey: $(Build.Repository.Name)
              cliProjectName: $(Build.Repository.Name)
              cliProjectVersion:
              cliSources: '.'
              extraProperties: |          
                sonar.qualitygate.wait=true
                sonar.testExecutionReportPaths=test-report.xml
                sonar.javascript.lcov.reportPaths=coverage/lcov.info
                sonar.exclusions=dist/**, lib/**, public/**, node_modules/**, coverage/**, **.test.js, reducer.js
                sonar.sourceEncoding=UTF-8
                sonar.javascript.file.suffixes=.js

          - task: SonarQubeAnalyze@5

          - task: SonarQubePublish@5
            inputs:
              pollingTimeoutSec: '300'

          # GENERATE BUILD AND ARTIFACT WEB DESA #
          - task: Npm@1
            condition: |
              and(
                succeeded(),
                or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch'])),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToQA2']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'npm Build DEV'
            env:
              REACT_APP_API_URL: $(APP_API_DESA)
              REACT_APP_AAD_TENANT_ID: $(TENANT_ID_AR_DEV)
              REACT_APP_AAD_CLIENT_ID: $(CLIENT_ID_AR_DEV)
            inputs:
              workingDir: '.'
              command: custom
              customCommand: run-script "build:dev"
          - task: CopyFiles@2
            condition: |
              and(
                succeeded(),
                or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch'])),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToQA2']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'copy Files DEV'
            inputs:
              SourceFolder: 'build'
              TargetFolder: '$(build.ArtifactStagingDirectory)/dev'
              CleanTargetFolder: true
          - task: PublishBuildArtifacts@1
            condition: |
              and(
                succeeded(),
                or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch'])),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToQA2']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'publish Artifact DEV'
            inputs:
              PathtoPublish: '$(build.ArtifactStagingDirectory)/dev'
              ArtifactName: 'build_dev'
          # GENERATE BUILD AND ARTIFACT WEB QA #
          - task: Npm@1
            condition: |
              and(
                succeeded(),
                or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch']), eq(variables['system.pullRequest.sourceBranch'], variables['targetBranchDeployToQA2'])),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToDESA']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'npm Build QA'
            env:
              REACT_APP_API_URL: $(APP_API_QA)
              REACT_APP_AAD_TENANT_ID: $(TENANT_ID_AR_QA)
              REACT_APP_AAD_CLIENT_ID: $(CLIENT_ID_AR_QA)
            inputs:
              workingDir: '.'
              command: custom
              customCommand: run-script "build:qa"
          - task: CopyFiles@2
            condition: |
              and(
                succeeded(),
                or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch']), eq(variables['system.pullRequest.sourceBranch'], variables['targetBranchDeployToQA2'])),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToDESA']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'copy Files QA'
            inputs:
              SourceFolder: 'build'
              TargetFolder: '$(build.ArtifactStagingDirectory)/qa'
              CleanTargetFolder: true
          - task: PublishBuildArtifacts@1
            condition: |
              and(
                succeeded(),
                or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch']), eq(variables['system.pullRequest.sourceBranch'], variables['targetBranchDeployToQA2'])),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToDESA']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'publish Artifact QA'
            inputs:
              PathtoPublish: '$(build.ArtifactStagingDirectory)/qa'
              ArtifactName: 'build_qa'
          # GENERATE BUILD AND ARTIFACT WEB QA slot1 #
          - task: Npm@1
            condition: |
              and(
                succeeded(),
                or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch'])),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToQA2']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'npm Build QA slot1'
            env:
              REACT_APP_API_URL: $(APP_API_QA_S1)
              REACT_APP_AAD_TENANT_ID: $(TENANT_ID_AR_QA)
              REACT_APP_AAD_CLIENT_ID: $(CLIENT_ID_AR_QA)
            inputs:
              workingDir: '.'
              command: custom
              customCommand: run-script "build:qa"
          - task: CopyFiles@2
            condition: |
              and(
                succeeded(),
                or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch'])),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToQA2']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'copy Files QA slot1'
            inputs:
              SourceFolder: 'build'
              TargetFolder: '$(build.ArtifactStagingDirectory)/qa_1'
              CleanTargetFolder: true
          - task: PublishBuildArtifacts@1
            condition: |
              and(
                succeeded(),
                or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch'])),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToQA2']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'publish Artifact QA slot1'
            inputs:
              PathtoPublish: '$(build.ArtifactStagingDirectory)/qa_1'
              ArtifactName: 'build_qa_1'
         
          # GENERATE BUILD AND ARTIFACT WEB PROD #
          - task: Npm@1
            condition: |
              and(
                succeeded(),
                startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToPROD']),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToPROD']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'npm Build PROD'
            env:
              REACT_APP_API_URL: $(APP_API_PROD)
              REACT_APP_AAD_TENANT_ID: $(TENANT_ID_AR_PROD)
              REACT_APP_AAD_CLIENT_ID: $(CLIENT_ID_AR_PROD)
            inputs:
              workingDir: '.'
              command: custom
              customCommand: run-script build
          - task: CopyFiles@2
            condition: |
              and(
                succeeded(),
                startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToPROD']),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToPROD']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'copy Files PROD'
            inputs:
              SourceFolder: 'build'
              TargetFolder: '$(build.ArtifactStagingDirectory)'
              CleanTargetFolder: true
          - task: PublishBuildArtifacts@1
            condition: |
              and(
                succeeded(),
                startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToPROD']),
                eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToPROD']),
                eq(variables['Build.Reason'], 'PullRequest')
              )
            displayName: 'publish Artifact PROD'
            inputs:
              PathtoPublish: '$(build.ArtifactStagingDirectory)'
              ArtifactName: 'build'
  # DEPLOY WEB DESA #
  # Condiciones para desplegar a DESA
  # - Abrir Pull Request (PR), sin asignar a nadie.
  # - Rama origen PR debe comenzar con nombre 'feature'
  # - Rama destino PR debe ser develop
  - stage: 'CD_WEB_DESA'
    displayName: 'CD_WEB_DESA'
    dependsOn: 'Build'
    condition: |
      and(
        succeeded(),
        or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch'])),
        eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToQA2']),
        eq(variables['Build.Reason'], 'PullRequest')
      )
    jobs:
      - deployment: DeployWEB
        displayName: DeployWEB
        pool:
          vmImage: 'ubuntu-latest'
        environment: CD_WEB_DESA
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  artifact: 'build_dev'                
                - task: AzureCLI@2
                  displayName: Az File Copy to Storage
                  inputs:
                    azureSubscription: $(serviceConnectionNameDESA)
                    scriptLocation: inlineScript
                    scriptType: 'bash'
                    inlineScript: |
                      az storage blob upload-batch \
                        --destination \$web \
                        --account-name "$(sa_front_desa)" \
                        --source "$(Pipeline.Workspace)/build_dev" \
                        --overwrite true

  # DEPLOY WEB QA #
  # Condiciones para desplegar a QA
  # - Al menos un approve
  - stage: 'CD_WEB_QA'
    displayName: 'CD_WEB_QA'
    dependsOn: 'Build'
    condition: |
      and(
        succeeded(),
        or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch']), eq(variables['system.pullRequest.sourceBranch'], variables['targetBranchDeployToQA2'])),
        eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToDESA']),
        eq(variables['Build.Reason'], 'PullRequest')
      )
    jobs:
      - deployment: DeployWEB
        displayName: DeployWEB
        pool:
          vmImage: 'ubuntu-latest'
        environment: CD_WEB_QA
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  displayName: 'Download Artifact: QA'
                  artifact: 'build_qa'                
                - task: AzureCLI@2
                  displayName: Az File Copy to Storage
                  inputs:
                    azureSubscription: $(serviceConnectionNameQA)
                    scriptLocation: inlineScript
                    scriptType: 'bash'
                    inlineScript: |
                      az storage blob upload-batch \
                        --destination \$web \
                        --account-name "$(sa_front_qa)" \
                        --source "$(Pipeline.Workspace)/build_qa" \
                        --overwrite true

  # DEPLOY WEB QA slot1 #
  # Condiciones para desplegar a QA slot1
  # - Al menos un approve
  - stage: 'CD_WEB_QA_SLOT1'
    displayName: 'CD_WEB_QA_SLOT1'
    dependsOn: 'Build'
    condition: |
      and(
        succeeded(),
        or(startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToDESA']), startsWith(variables['system.pullRequest.sourceBranch'], variables['bugfixBranch'])),
        eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToQA2']),
        eq(variables['Build.Reason'], 'PullRequest')
      )
    jobs:
      - deployment: DeployWEB
        displayName: DeployWEB
        pool:
          vmImage: 'ubuntu-latest'
        environment: CD_WEB_QA_SLOT1
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  displayName: 'Download Artifact: QA slot1'
                  artifact: 'build_qa_1'               
                - task: AzureCLI@2
                  displayName: Az File Copy to Storage
                  inputs:
                    azureSubscription: $(serviceConnectionNameQA)
                    scriptLocation: inlineScript
                    scriptType: 'bash'
                    inlineScript: |
                      az storage blob upload-batch \
                        --destination \$web \
                        --account-name "$(sa_front_qa2)" \
                        --source "$(Pipeline.Workspace)/build_qa_1" \
                        --overwrite true


  - stage: RoleAssignRequest
    dependsOn: [Build]
    condition: |
      and(
        succeeded(),
        startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToPROD']),
        eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToPROD']),
        eq(variables['Build.Reason'], 'PullRequest')
      )
    jobs:
      - deployment: RoleAssignRequest
        pool:
          vmImage: $(vmImageName)
        timeoutInMinutes: 0
        # environment: "SQA's_APPROVAL"
        environment: 'RoleAssign'
        variables:
          - group: Constants
          - group: Credentials
          - group: Credentials-PROD
          - group: Constants-PROD
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                  displayName: 'RoleAssignRequest: Repository branch checkout'
                - script: |
                    pwd
                    ls -lah
                    export AZ_DEVOPS_PROJECT_ID=$SYSTEM_TEAMPROJECTID
                    export AZ_DEVOPS_PROJECT_PULLREQUEST_ID=$SYSTEM_PULLREQUEST_PULLREQUESTID
                    echo "---> Validar variables: con _DEVOPS"
                    env | grep _DEVOPS
                    echo "---> Validar variables: con ROLE"
                    env | grep ROLE
                    echo "---> Validar conexion: obtener lista de los nombres de los proyectos de la organizacion"
                    curl -u $AZ_DEVOPS_DSA_NAME:$AZ_DEVOPS_DSA_PAT -H "Content-Type: application/json" "https://dev.azure.com/$AZ_DEVOPS_PROJECT_ORGANIZATION_NAME/_apis/projects?api-version=6.1-preview.4" | jq -r '.value[].name'
                  enabled: true
                  env:
                    AZ_DEVOPS_DSA_NAME: $(AZ_DEVOPS_DSA_NAME)
                    AZ_DEVOPS_DSA_PAT: $(AZ_DEVOPS_DSA_PAT)
                  displayName: 'RoleAssignRequest: troubleshooting'
                - script: |
                    export AZ_DEVOPS_PROJECT_ID=$SYSTEM_TEAMPROJECTID
                    export AZ_DEVOPS_PROJECT_PULLREQUEST_ID=$SYSTEM_PULLREQUEST_PULLREQUESTID

                    export GO_OR_NOT_TO_GO=false

                    echo "---> Paso1: Inicio espera aprobador"
                    while [ "$GO_OR_NOT_TO_GO" == 'false' ]
                    do
                        echo "---> Rescatar nombre aprobador"
                        AZ_DEVOPS_PROJECT_PULLREQUEST_ACTUALAPPROVERSUNIQUENAME=( $(curl -u $AZ_DEVOPS_DSA_NAME:$AZ_DEVOPS_DSA_PAT -H "Content-Type: application/json" "https://dev.azure.com/$AZ_DEVOPS_PROJECT_ORGANIZATION_NAME/$AZ_DEVOPS_PROJECT_ID/_apis/git/pullrequests/$AZ_DEVOPS_PROJECT_PULLREQUEST_ID?api-version=6.1-preview.1" | jq -r '.reviewers[] | select(.vote==10) | .uniqueName') )

                        echo "---> AZ_DEVOPS_PROJECT_PULLREQUEST_ACTUALAPPROVERSUNIQUENAME: "$AZ_DEVOPS_PROJECT_PULLREQUEST_ACTUALAPPROVERSUNIQUENAME
                        echo "---> Inicio validacion nombre en lista blanca"
                        for user in "${AZ_DEVOPS_PROJECT_PULLREQUEST_ACTUALAPPROVERSUNIQUENAME[@]}"
                        do
                            echo "---> Validando que $user sea parte de la lista blanca"
                            GO_OR_NOT_TO_GO=$(cat ACHS_SQA_WHITE_LIST.json | jq -r '.ACHS_SQA_WHITE_LIST | contains(["'$user'"])')
                            echo "---> veredicto: "$GO_OR_NOT_TO_GO
                            if [ "$GO_OR_NOT_TO_GO" == true ]; then
                                echo "---> Se consiguió la aprobación de alguien autorizado, se puede realizar el despliegue"
                                break
                            fi
                        done
                        if [ "$GO_OR_NOT_TO_GO" == false ]; then
                            echo "---> Ningún Aprobador Autorizado ha aprobado"
                            echo "---> Pausamos la ejecución por $ROLE_ASSIGN_REQUEST_WAITING_PR_APPROVE_TIME"
                            sleep $ROLE_ASSIGN_REQUEST_WAITING_PR_APPROVE_TIME
                        fi
                    done
                    echo "---> fin"
                  condition: |
                      and(
                        succeeded(),
                        startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToPROD']),
                        eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToPROD']),
                        eq(variables['Build.Reason'], 'PullRequest')
                      )
                  enabled: true
                  timeoutInMinutes: 0
                  env:
                    AZ_DEVOPS_DSA_NAME: $(AZ_DEVOPS_DSA_NAME)
                    AZ_DEVOPS_DSA_PAT: $(AZ_DEVOPS_DSA_PAT)
                  displayName: 'RoleAssignRequest: waiting PR approve'

                - script: |
                    echo "---> Paso 1: haciendo loggin en Azure Devops con cuenta de servicio $AZ_DEVOPS_DSA_NAME que activará pipeline RoleAssign"
                    az login --debug --username $AZ_DEVOPS_DSA_NAME --password $AZ_DEVOPS_DSA_PASSWORD --tenant $AZ_TENANT_ID --allow-no-subscriptions
                    AZ_DEVOPS_DSA_NAME=`az account show | jq -r .user.name`
                    echo "---> Paso 1: loggin realizado con usuario: "$AZ_DEVOPS_DSA_NAME

                    echo "---> Paso 2: validando conexion, obteniendo ID del proyecto $AZ_DEVOPS_TARGET_PROJECT_NAME que contiene al pipeline RoleAssign"
                    AZ_DEVOPS_TARGET_PROJECT_ID=$(az devops project list --org https://dev.azure.com/$AZ_DEVOPS_TARGET_PROJECT_ORGANIZATION_NAME --query "value[?name=='"$AZ_DEVOPS_TARGET_PROJECT_NAME"'].id" -o tsv)
                    echo "---> Paso 2: el ID del Target project es: "$AZ_DEVOPS_TARGET_PROJECT_ID

                    echo "---> Paso 3: obteniendo ID del pipeline RoleAssign"
                    AZ_DEVOPS_TARGET_PIPELINE_PROJECT_ID=$(az pipelines list --project $AZ_DEVOPS_TARGET_PROJECT_ID --org https://dev.azure.com/$AZ_DEVOPS_TARGET_PROJECT_ORGANIZATION_NAME --query "[?name=='"$AZ_DEVOPS_TARGET_PIPELINE_PROJECT_NAME"'].id" -o tsv)
                    echo "---> Paso 3: el ID del Target pipeline es: "$AZ_DEVOPS_TARGET_PIPELINE_PROJECT_ID

                    echo "---> Paso 4: preparando variables a enviar"
                    VARIABLES_ARRAY=("AZ_DEVOPS_PROJECT_ORGANIZATION_NAME=$AZ_DEVOPS_PROJECT_ORGANIZATION_NAME" "AZ_DEVOPS_PROJECT_NAME=$SYSTEM_TEAMPROJECT" "AZ_DEVOPS_PROJECT_ID=$SYSTEM_TEAMPROJECTID" "AZ_DEVOPS_PROJECT_REPOSITORY_NAME=$BUILD_REPOSITORY_NAME" "AZ_DEVOPS_PROJECT_REPOSITORY_ID=$BUILD_REPOSITORY_ID" "AZ_DEVOPS_PROJECT_PULLREQUEST_ID=$SYSTEM_PULLREQUEST_PULLREQUESTID" "AZ_DEVOPS_PROJECT_PIPELINE_ID=$SYSTEM_DEFINITIONID" "AZ_DEVOPS_PROJECT_PIPELINE_STAGE_NAME=$SYSTEM_STAGENAME" "AZ_DEVOPS_PROJECT_PIPELINE_RUN_NAME=$BUILD_BUILDNUMBER" "AZ_DEVOPS_PROJECT_BUILD_ID=$BUILD_BUILDID");
                    echo "---> Paso 4: Las variables a enviar son: "
                    echo "---> AZ_DEVOPS_PROJECT_ORGANIZATION_NAME: "$AZ_DEVOPS_PROJECT_ORGANIZATION_NAME
                    echo "---> AZ_DEVOPS_PROJECT_NAME: "$SYSTEM_TEAMPROJECT
                    echo "---> AZ_DEVOPS_PROJECT_ID: "$SYSTEM_TEAMPROJECTID
                    echo "---> AZ_DEVOPS_PROJECT_REPOSITORY_NAME: "$BUILD_REPOSITORY_NAME
                    echo "---> AZ_DEVOPS_PROJECT_REPOSITORY_ID: "$BUILD_REPOSITORY_ID
                    echo "---> AZ_DEVOPS_PROJECT_PULLREQUEST_ID: "$SYSTEM_PULLREQUEST_PULLREQUESTID
                    echo "---> AZ_DEVOPS_PROJECT_PIPELINE_ID: "$SYSTEM_DEFINITIONID
                    echo "---> AZ_DEVOPS_PROJECT_PIPELINE_STAGE_NAME: "$SYSTEM_STAGENAME
                    echo "---> AZ_DEVOPS_PROJECT_PIPELINE_RUN_NAME: "$BUILD_BUILDNUMBER
                    echo "---> AZ_DEVOPS_PROJECT_BUILD_ID: "$BUILD_BUILDID

                    echo "---> Paso 5: trigger pipeline target"
                    az pipelines run --id $AZ_DEVOPS_TARGET_PIPELINE_PROJECT_ID --project $AZ_DEVOPS_TARGET_PROJECT_NAME --org https://dev.azure.com/$AZ_DEVOPS_TARGET_PROJECT_ORGANIZATION_NAME --debug --variables  "${VARIABLES_ARRAY[@]}"
                    echo "---> Paso 5: asignacion de roles fue solicitada"
                  enabled: true
                  env:
                    AZ_DEVOPS_DSA_NAME: $(AZ_DEVOPS_DSA_NAME)
                    AZ_DEVOPS_DSA_PASSWORD: $(AZ_DEVOPS_DSA_PASSWORD)
                    AZ_SP_ID: $(AZ_SP_ID)
                    AZ_SP_PASSWORD: $(AZ_SP_PASSWORD)
                  displayName: 'RoleAssignRequest: request applyRole'
                - script: |
                    echo "---> Paso 1: limpiando credenciales"
                    az account clear

                    echo "---> Paso 2: haciendo login hacia azure con Service principal (SP)"
                    az login --debug --service-principal --username $AZ_SP_ID --password $AZ_SP_PASSWORD --tenant $AZ_TENANT_ID --allow-no-subscriptions

                    echo "---> Paso 3: validando cuenta usada"
                    az account show

                    echo "---> Paso 4: Validando parametros con los que se realizó la solicitud"
                    AZ_ROLEASSIGN_SCOPE=/subscriptions/$AZ_SUSCRIPTION_ID/resourceGroups/$AZ_RESOURCEGROUP_NAME
                    echo "---> AZ_ROLEASSIGN_SCOPE: "$AZ_ROLEASSIGN_SCOPE
                    echo "---> AZ_SP_ID: "$AZ_SP_ID
                    echo "---> AZ_CUSTOMROLE_NAME: "$AZ_CUSTOMROLE_NAME

                    echo "---> Paso 5: listando roles actuales"
                    az role assignment list --assignee $AZ_SP_ID --scope $AZ_ROLEASSIGN_SCOPE

                    echo "---> Paso 6: esperando asignacion de rol"
                    ROLE_ASSIGN_RESULT=$(az role assignment list --assignee $AZ_SP_ID --scope $AZ_ROLEASSIGN_SCOPE | jq -r '.[] | select (.roleDefinitionName=="'$AZ_CUSTOMROLE_NAME'") | .scope')
                    echo "---> Paso 6: ROLE_ASSIGN_RESULT: "$ROLE_ASSIGN_RESULT
                    n=1
                    while [ "$ROLE_ASSIGN_RESULT" != "$AZ_ROLEASSIGN_SCOPE" ] && [ $n -le $ROLE_ASSIGN_REQUEST_WAITING_APPLY_ROLE_CYCLES ]
                    do
                      echo "---> Paso 6: fecha: "
                      date
                      echo "---> Paso 6: sleep for $ROLE_ASSIGN_REQUEST_WAITING_PR_APPROVE_TIME..."
                      sleep $ROLE_ASSIGN_REQUEST_WAITING_PR_APPROVE_TIME
                      n=$((n+1))
                      ROLE_ASSIGN_RESULT=$(az role assignment list --assignee $AZ_SP_ID --scope $AZ_ROLEASSIGN_SCOPE | jq -r '.[] | select (.roleDefinitionName=="'$AZ_CUSTOMROLE_NAME'") | .scope')
                      echo "---> Paso 6: ROLE_ASSIGN_RESULT: "$ROLE_ASSIGN_RESULT
                    done

                    if [ $n -gt $ROLE_ASSIGN_REQUEST_WAITING_APPLY_ROLE_CYCLES ]; then
                      echo "---> Paso 6: OJO: Se cumplió tiempo maximo de espera para que aplicaran los permisos"
                      exit 1
                    fi
                  enabled: true
                  env:
                    AZ_DEVOPS_DSA_NAME: $(AZ_DEVOPS_DSA_NAME)
                    AZ_DEVOPS_DSA_PASSWORD: $(AZ_DEVOPS_DSA_PASSWORD)
                    AZ_SP_ID: $(AZ_SP_ID)
                    AZ_SP_PASSWORD: $(AZ_SP_PASSWORD)
                  displayName: 'RoleAssignRequest: waiting applyRole'

  #DEPLOY WEB PROD #
  # - Al menos un approve
  # Completar pr
  - stage: 'CD_WEB_PROD'
    displayName: 'CD_WEB_PROD'
    dependsOn: [RoleAssignRequest]
    condition: |
      and(
        succeeded(),
        startsWith(variables['system.pullRequest.sourceBranch'], variables['sourceBranchDeployToPROD']),
        eq(variables['system.pullRequest.targetBranch'], variables['targetBranchDeployToPROD']),
        eq(variables['Build.Reason'], 'PullRequest')
      )
    jobs:
      - deployment: DeployWEB
        displayName: DeployWEB
        pool:
          vmImage: 'ubuntu-latest'
        environment: CD_WEB_PROD
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  artifact: 'build'
                #CD PROD               
                - task: AzureCLI@2
                  displayName: Az File Copy to Storage
                  inputs:
                    azureSubscription: $(serviceConnectionNamePROD)
                    scriptLocation: inlineScript
                    scriptType: 'bash'
                    inlineScript: |
                      az storage blob upload-batch \
                        --destination \$web \
                        --account-name "$(sa_front_prod)"  \
                        --source "$(Pipeline.Workspace)/build"  \
                        --overwrite true


