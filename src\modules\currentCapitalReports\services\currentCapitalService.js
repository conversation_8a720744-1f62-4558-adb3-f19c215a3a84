/* eslint-disable no-console */

import { axiosRequest, excelRequest } from '../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

//------------------------------------------------------------------------------------------------
export const getCurrentCapitalReport = async reportType => {
  const { data, isError } = await excelRequest
    .get(`${api}/currentcapitalreport/${reportType}`)
    .catch(err => {
      console.error(err);
      return { data: null, isError: true };
    });

  return { data, isError };
};

//------------------------------------------------------------------------------------------------
export const checkCronExecution = cronName => async setIsCronExecuted => {
  const {
    data: {
      result: { alreadyExecuted }
    }
  } = await axiosRequest
    .get(`${api}/verifycronexecution/${cronName}`)

    .catch(err => {
      console.error(err);
      return { data: { result: { alreadyExecuted: false, error: '' } } };
    });
  setIsCronExecuted(alreadyExecuted);
};
