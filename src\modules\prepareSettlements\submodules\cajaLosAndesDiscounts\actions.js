//------------------------------------------------------------------------------------------------
export const SET_CURRENT_MONTH_YEAR = 'SET_CURRENT_MONTH_YEAR';
export const setCurrentMonthYear = currentMonthYear => {
  return {
    type: SET_CURRENT_MONTH_YEAR,
    data: { currentMonthYear }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_WAS_LOSANDES_EXECUTED = 'SET_WAS_LOSANDES_EXECUTED';
export const setWasLosAndesProcessExecuted = bool => {
  return {
    type: SET_WAS_LOSANDES_EXECUTED,
    data: bool
  };
};
//------------------------------------------------------------------------------------------------
export const SET_IN_DAYS_LIMIT_RANGE = 'SET_IN_DAYS_LIMIT_RANGE';
export const setInDaysLimitRange = isInDaysLimitRange => {
  return {
    type: SET_IN_DAYS_LIMIT_RANGE,
    data: { isInDaysLimitRange }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_IN_NUMBER_DAYS_LIMIT_RANGE = 'SET_IN_NUMBER_DAYS_LIMIT_RANGE';
export const setInNumberDaysLimitRange = isInNumberDaysLimitRange => {
  return {
    type: SET_IN_NUMBER_DAYS_LIMIT_RANGE,
    data: { isInNumberDaysLimitRange }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CRON_BASE_MINIMUM_PENSION_EXECUTED = 'SET_CRON_BASE_MINIMUM_PENSION_EXECUTED';
export const setBaseMinimumPensionExecuted = cronBaseMinimumPensionExecuted => {
  return {
    type: SET_CRON_BASE_MINIMUM_PENSION_EXECUTED,
    data: { cronBaseMinimumPensionExecuted }
  };
};
//------------------------------------------------------------------------------------------------
export const CLEAN_CAJA_LOS_ANDES_FILE_MEMBERSHIPS = 'CLEAN_CAJA_LOS_ANDES_FILE_MEMBERSHIPS';
export const cleanFileMemberships = () => {
  return {
    type: CLEAN_CAJA_LOS_ANDES_FILE_MEMBERSHIPS
  };
};
//------------------------------------------------------------------------------------------------
export const CLEAN_CAJA_LOS_ANDES_FILE_CREDITS = 'CLEAN_CAJA_LOS_ANDES_FILE_CREDITS';
export const cleanFileCredits = () => {
  return {
    type: CLEAN_CAJA_LOS_ANDES_FILE_CREDITS
  };
};
//------------------------------------------------------------------------------------------------
export const CLEAN_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS =
  'CLEAN_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS';
export const cleanFileAnotherDiscounts = () => {
  return {
    type: CLEAN_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_NAME = 'SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_NAME';
export const setFileMembershipsName = fileName => {
  return {
    type: SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_NAME,
    data: { fileName }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CAJA_LOS_ANDES_FILE_CREDITS_NAME = 'SET_CAJA_LOS_ANDES_FILE_CREDITS_NAME';
export const setFileCreditsName = fileName => {
  return {
    type: SET_CAJA_LOS_ANDES_FILE_CREDITS_NAME,
    data: { fileName }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_NAME =
  'SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_NAME';
export const setFileAnotherDiscountsName = fileName => {
  return {
    type: SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_NAME,
    data: { fileName }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_DATA = 'SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_DATA';
export const setFileMembershipsData = fileData => {
  return {
    type: SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_DATA,
    data: { fileData }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CAJA_LOS_ANDES_FILE_CREDITS_DATA = 'SET_CAJA_LOS_ANDES_FILE_CREDITS_DATA';
export const setFileCreditsData = fileData => {
  return {
    type: SET_CAJA_LOS_ANDES_FILE_CREDITS_DATA,
    data: { fileData }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_DATA =
  'SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_DATA';
export const setFileAnotherDiscountsData = fileData => {
  return {
    type: SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_DATA,
    data: { fileData }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_ERROR =
  'SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_ERROR';
export const setFileMembershipsError = fileError => {
  return {
    type: SET_CAJA_LOS_ANDES_FILE_MEMBERSHIPS_ERROR,
    data: { fileError }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CAJA_LOS_ANDES_FILE_CREDITS_ERROR = 'SET_CAJA_LOS_ANDES_FILE_CREDITS_ERROR';
export const setFileCreditsError = fileError => {
  return {
    type: SET_CAJA_LOS_ANDES_FILE_CREDITS_ERROR,
    data: { fileError }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_ERROR =
  'SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_ERROR';
export const setFileAnotherDiscountsError = fileError => {
  return {
    type: SET_CAJA_LOS_ANDES_FILE_ANOTHER_DISCOUNTS_ERROR,
    data: { fileError }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_SNACKBAR_MESSAGE_ERROR = 'SET_SNACKBAR_MESSAGE_ERROR';
export const setSnackbarMessageError = messageError => {
  return {
    type: SET_SNACKBAR_MESSAGE_ERROR,
    data: { messageError }
  };
};
//------------------------------------------------------------------------------------------------
export const SET_OPEN_SNACKBAR_ERROR = 'SET_OPEN_SNACKBAR_ERROR';
export const setOpenSnackbarError = openSnackbarError => {
  return {
    type: SET_OPEN_SNACKBAR_ERROR,
    data: { openSnackbarError }
  };
};
