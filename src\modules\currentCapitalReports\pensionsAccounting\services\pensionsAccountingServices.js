/* eslint-disable no-console */

import { excelRequest, axiosRequest } from '../../../../services/axiosRequest';

const { REACT_APP_API_URL } = process.env;
const api = REACT_APP_API_URL;

//--------------------------------------------------------
// eslint-disable-next-line import/prefer-default-export
export const getPensionsAccountingReport = async () => {
  const { data, isError } = await excelRequest.get(`${api}/pensionsAccountingReport`).catch(err => {
    console.error(err);
    return { data: null, isError: true };
  });

  return { data, isError };
};

export const existsData = async setExistsTable => {
  const { data } = await axiosRequest
    .get(`${api}/pensionsAccountingReport/existsData`)
    .catch(err => {
      console.error(err);
      return err;
    });

  const { doesDataExists } = data;

  setExistsTable(doesDataExists);
};
